import { Button } from 'antd';
import { FC } from 'react';
import { useSelector } from 'umi';
import './index.less';

interface IHeaderProps {
  title?: string;
  /** skip按钮 */
  showSkip?: boolean;
  onSkip?: () => void;
}

const TestSystemHeader: FC<IHeaderProps> = ({ title = '考试系统', showSkip, onSkip }) => {
  const userInfo = useSelector((state: any) => state.userInfo);
//   const { parameterConfig, permissions, modules } = useSelector<
//   { permission: any },
//   any
// >(({ permission }) => permission);
// const globalData = useSelector<any, any>((state) => {
//   return state.globalData.major;
// });
// console.log(parameterConfig, permissions, modules, 'globalData')
  return (
    <div className="test_system_header">
      <div className="system_name">{title}</div>
      <div className="system_right">
        {showSkip && (
          <Button type="primary" onClick={onSkip}>
            跳过说明
          </Button>
        )}
        <img className="user_avatar" src={userInfo?.avatar} />
      </div>
    </div>
  );
};

export default TestSystemHeader;
