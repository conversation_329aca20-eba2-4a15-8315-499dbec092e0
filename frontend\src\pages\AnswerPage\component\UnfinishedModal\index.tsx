import { ExclamationCircleFilled } from '@ant-design/icons';
import { Button, Modal } from 'antd';
import { FC } from 'react';
import './index.less';

const UnfinishedModal: FC<{
  isShow: boolean;
  onClose: () => void;
  onConfirm: () => void;
}> = ({ isShow, onClose, onConfirm }) => {
  return (
    <Modal
      title="信息提示"
      open={isShow}
      onOk={onClose}
      onCancel={onClose}
      footer={
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
          }}
        >
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" onClick={onConfirm}>
            确认
          </Button>
        </div>
      }
    >
      <div className="unfinished_modal_container">
        <ExclamationCircleFilled
          style={{
            color: 'red',
            width: '36px',
            height: '36px',
            fontSize: '36px',
          }}
        />
        <div style={{ marginTop: 20 }}>还有试题尚未作答，确认交卷？</div>
      </div>
    </Modal>
  );
};

export default UnfinishedModal;
