tinymce.PluginManager.add('sobeymath', function (editor, url) {
  var pluginName = '插入公式';
  var iframe1 = window.location.origin + '/matheditor/#/';
  var show = false;
  var target = null;

  let mathjaxClassName = "math-tex";
  let mathjaxTempClassName = mathjaxClassName + '-original';
  let mathjaxSymbols = { start: '\\(', end: '\\)' };
  let loadover = false;


  let init = function (){
    // if (loadover) {
    //   return;
    // }
    let scripts = editor.getDoc().getElementsByTagName('script');
    let sobeyScripts = ['tinymce/plugins/sobeymath/config.js','tinymce/tex-svg.js'];
    for (let i = 0; i < sobeyScripts.length; i++) {
      let newurl = location.pathname + sobeyScripts[i];
      // 检查script是否已加载
      let id = editor.dom.uniqueId();
      let script = editor.dom.create('script', { id: id, type: 'text/javascript', src: newurl });
      if(script.src.indexOf(sobeyScripts[1])>0){
        script.async = true;
        script.onload = function () {
          console.log(MathJax,editor.getDoc().defaultView.MathJax);
          console.log('插件加载完毕');
          loadover = true;
          postmessage();
        };
      }
      let found = false;
      for (let j = 0; j < scripts.length; j++) {
        if (scripts[j].src == script.src || scripts[j].src == newurl) {
          editor.dom.remove(script);
          found = true;
          break;
        }
      }

      editor.getDoc().getElementsByTagName('head')[0].appendChild(script);
      
      // // 加载script
      // if (!found) {
      //   editor.getDoc().getElementsByTagName('head')[0].appendChild(script);
      //   console.log(editor.getDoc().defaultView.MathJax);
      // }else{

      // }
    }
  }

  let getMathText = function (value, symbols) {
    if (!symbols) {
      symbols = mathjaxSymbols;
    }
    return symbols.start + ' ' + value + ' ' + symbols.end;
  };

  let checkElement = function (element) {
    if (element.childNodes.length != 2) {
      element.setAttribute('contenteditable', false);
      element.style.cursor = 'pointer';
      let latex = element.getAttribute('data-latex') || element.innerHTML;
      element.setAttribute('data-latex', latex);
      element.innerHTML = '';

      let math = editor.dom.create('span');
      math.innerHTML = latex;
      math.classList.add(mathjaxTempClassName);
      element.appendChild(math);

      let dummy = editor.dom.create('span');
      dummy.classList.add('dummy');
      dummy.innerHTML = 'dummy';
      dummy.setAttribute('hidden', 'hidden');
      element.appendChild(dummy);
    }
  };

  // 监听postmessage事件
   function postmessage(){
    window.addEventListener('message', function (e) {
      if (e.origin === window.location.origin) {
        if (typeof e.data === 'string' && show) {
          const { action, value } = JSON.parse(e.data);
          if (action == 'instermath') {
            if (target) {
              target.innerHTML = '';
              target.setAttribute('data-latex', getMathText(value));
              checkElement(target);
            } else {
              let newElement = editor.getDoc().createElement('span');
              newElement.innerHTML = getMathText(value);
              newElement.classList.add(mathjaxClassName);
              checkElement(newElement);
              editor.insertContent(newElement.outerHTML);
            }
            editor.windowManager.close();
          } else if (action == 'closemathwin') {
            editor.windowManager.close();
          } else {
  
          }
  
        }
      }
    });


     // refresh mathjax on set content
      editor.on('SetContent', function (e) {
        console.log('SetContent',e);
        if (loadover && editor.getDoc().defaultView.MathJax.typesetPromise) {
          editor.getDoc().defaultView.MathJax.typesetPromise();
        }else{
          init();
        }
      });

      editor.on('Change', function (data) {
        console.log('Change',data);
        elements = editor.dom.getRoot().querySelectorAll('.' + mathjaxClassName);
        if (elements.length) {
          for (let i = 0; i < elements.length; i++) {
            checkElement(elements[i]);
          }
          if (loadover && editor.getDoc().defaultView.MathJax.typesetPromise) {
            editor.getDoc().defaultView.MathJax.typesetPromise();
          }else{
            init();
          }
        }
      });
  }

  editor.on('init', init);

  editor.on('CloseWindow', function (e) {
    show = false;
    target = null;
  });

  editor.on('GetContent', function (e) {
    let div = editor.dom.create('div');
    div.innerHTML = e.content;
    let elements = div.querySelectorAll('.' + mathjaxClassName);

    for (let i = 0; i < elements.length; i++) {
      let children = elements[i].querySelectorAll('span');
      for (let j = 0; j < children.length; j++) {
        children[j].remove();
      }
      let latex = elements[i].getAttribute('data-latex');
      elements[i].removeAttribute('contenteditable');
      elements[i].removeAttribute('style');
      elements[i].removeAttribute('data-latex');
      elements[i].innerHTML = latex;
    }
    e.content = div.innerHTML;
  });


  // add dummy tag on set content
  editor.on('BeforeSetContent', function (e) {
    let div = editor.dom.create('div');
    div.innerHTML = e.content;
    let elements = div.querySelectorAll('.' + mathjaxClassName);

    for (let i = 0; i < elements.length; i++) {
      checkElement(elements[i]);
    }
    e.content = div.innerHTML;
  });

    // refresh mathjax on set content
    editor.on('SetContent', function(e) {
        
        if (editor.getDoc().defaultView?.MathJax?.typesetPromise) {
        editor.getDoc().defaultView.MathJax.typesetPromise();
        }
    });

    editor.on('Change', function(data) {
        elements = editor.dom.getRoot().querySelectorAll('.' + mathjaxClassName);
        if (elements.length) {
          for (let i = 0 ; i < elements.length; i++) {
            checkElement(elements[i]);
          }
          if (editor.getDoc().defaultView?.MathJax?.typesetPromise) {
            editor.getDoc().defaultView.MathJax.typesetPromise();
          }
        }
      });
    
    // handle dblclick on existing
    editor.on("dblclick", function (e) {
        let closest = e.target.closest('.' + mathjaxClassName);
        if (closest) {
            let latex = '';
            if (closest) {
                latex_attribute = closest.getAttribute('data-latex');
                if (latex_attribute.length >= (mathjaxSymbols.start + mathjaxSymbols.end).length) {
                    latex = latex_attribute.substr(mathjaxSymbols.start.length, latex_attribute.length - (mathjaxSymbols.start + mathjaxSymbols.end).length);
                }
            }
            show = true;
            target = closest;
            let winDialog =  openDialog(latex);
            // setTimeout(() => {
            //     winDialog.sendMessage(JSON.stringify({ action: 'selectlatex',value:latex}),'*')
            // }, 3000);
        }
    });

  window.tinymceLng = '';
  window.tinymceLat = '';
  var openDialog = function (latex) {
    return editor.windowManager.openUrl({
      title: pluginName,
      size: 'large',
      width: 1200,
      height: 680,
      url: latex ? iframe1 + '?latex=' + encodeURIComponent(latex) : iframe1,
      buttons: [],
    });
  };

  editor.ui.registry.getAll().icons.sobeymath || editor.ui.registry.addIcon('sobeymath', '<svg t="1717598937965" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3359" width="24" height="24"><path d="M870.570667 508.017778a42.666667 42.666667 0 0 1 5.262222 60.117333l-120.263111 143.303111 77.824 134.784a42.666667 42.666667 0 1 1-73.898667 42.666667l-62.293333-107.875556-95.630222 113.962667a42.666667 42.666667 0 1 1-65.365334-54.840889l115.584-137.770667-77.184-133.717333a42.666667 42.666667 0 1 1 73.898667-42.666667l61.667555 106.808889 100.280889-119.509333a42.666667 42.666667 0 0 1 60.117334-5.262222zM611.555556 113.777778a42.666667 42.666667 0 0 1 2.503111 85.262222L611.555556 199.111111h-19.256889a128 128 0 0 0-126.577778 108.984889l-0.512 3.768889L463.374222 327.111111H554.666667a28.444444 28.444444 0 0 1 28.444444 28.444445v28.444444a28.444444 28.444444 0 0 1-28.444444 28.444444h-101.532445l-37.176889 309.859556a213.333333 213.333333 0 0 1-207.061333 187.875556l-4.750222 0.042666H184.888889a42.666667 42.666667 0 0 1-2.503111-85.262222L184.888889 824.888889h19.256889a128 128 0 0 0 126.577778-108.984889l0.512-3.768889L367.175111 412.444444H270.222222a28.444444 28.444444 0 0 1-28.444444-28.444444v-28.444444a28.444444 28.444444 0 0 1 28.444444-28.444445h107.192889l3.072-25.415111a213.333333 213.333333 0 0 1 207.061333-187.875556L592.298667 113.777778H611.555556z" fill="#333333" p-id="3360"></path></svg>');

  editor.ui.registry.addButton('sobeymath', {
    icon: 'sobeymath',
    tooltip: pluginName,
    onAction: function () { 
      show = true;
      openDialog();
      if(!editor.getDoc().defaultView.MathJax.typesetPromise){
        init();
      }
    }
  });
  editor.ui.registry.addMenuItem('sobeymath', {
    text: pluginName,
    onAction: function () {
      show = true;
      openDialog();
    }
  });
  return {
    getMetadata: function () {
      return {
        name: pluginName,
        url: "https://www.sobey.com",
      };
    }
  };
});
