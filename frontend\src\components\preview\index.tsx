import { getstudentinfo } from '@/api/student';
import examType from '@/types/examType';
import { noSupport } from '@/utils';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { But<PERSON>, Checkbox, Drawer, Image, Radio } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { IConfig, useSelector } from 'umi';
import RenderHtml from '../renderHtml';
import './index.less';
interface previewprops {
  detail: any;
  visible: boolean;
  onClose: () => void;
  handlecopy?: () => void;
  handledit?: () => void;
}

const Preview: React.FC<previewprops> = (props) => {
  const { detail, visible, onClose, handlecopy, handledit } = props;
  const [showdetail, setshowdetail] = React.useState(false);
  const [userlist, setuserlist] = React.useState<any[]>([]);
  const [perviewSrc, setPerviewSrc] = React.useState<any>('');
  const [imgvisible, setImgVisible] = React.useState<any>(false);
  const close = () => {
    onClose();
  };
  const copy = () => {
    if (configs.mobileFlag) {
      noSupport();
      return;
    }
    handlecopy && handlecopy();
  };
  const edit = () => {
    if (configs.mobileFlag) {
      noSupport();
      return;
    }
    handledit && handledit();
  };
  const configs: IConfig = useSelector<{ config: any }, IConfig>(
    ({ config }) => config,
  );

  useEffect(() => {
    if (detail && detail.share_users) {
      getshareuserinfo(detail.share_users);
    }
  }, [detail]);

  // 获取用户信息
  const getshareuserinfo = (share_users: any) => {
    if (share_users.length) {
      getstudentinfo({ ids: share_users }).then((res: any) => {
        if (res && res.length) {
          setuserlist(res);
        } else {
          setuserlist([]);
        }
      });
    } else {
      setuserlist([]);
    }
  };

  // 预览图片
  const perviewimg = (e: any) => {
    if (e.target.nodeName === 'IMG') {
      setImgVisible(true);
      setPerviewSrc(e.target.currentSrc);
    }
  };

  return (
    <Drawer
      width={configs.mobileFlag ? '100%' : 580}
      className="preview"
      extra={
        <>
          {detail?.editable && (
            <Button type="primary" onClick={edit}>
              编辑
            </Button>
          )}
          {handlecopy && (
            <Button type="primary" onClick={copy}>
              复制
            </Button>
          )}
        </>
      }
      title={'题目预览'}
      placement="right"
      onClose={close}
      visible={visible}
    >
      {detail && (
        <div className="content_row">
          <div className="content">
            <div className="type">
              (
              <span>{`*${examType.optionType_[detail.questions_type]}题`}</span>
              )
            </div>
            <RenderHtml
              cname="auto-img"
              value={detail.questions_content}
              onClick={(e) => perviewimg(e)}
            ></RenderHtml>
          </div>
          {detail.fileList?.length > 0 && (
            <div className="fileList_">
              <span>题目附件：</span>
              <div>
                {detail.fileList.map((item: any, index: number) => {
                  return (
                    <a
                      href={item.attachmentSource}
                      key={index}
                      target={item.attachmentSource}
                      title={item.attachmentName || ''}
                    >
                      {item.attachmentName || ''}
                    </a>
                  );
                })}
              </div>
            </div>
          )}
          {detail.hasAttachment?.length > 0 && (
            <div className="fileList_upload">
              <span>上传附件：</span>
              <div>
                {detail.hasAttachment.map((item: any, index: number) => {
                  return (
                    <div key={index}>
                      <span>{item.name}</span>
                      <span>{item.required && `(必传*)`}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
          <div className="answers">
            {detail.questions_type === 0 ? ( //单选
              <Radio.Group value={detail.questions_answers[0]}>
                {detail.questions_options.map(
                  (item_0: any, index_0: number) => {
                    return (
                      <div className="answer_item" key={index_0}>
                        <Radio
                          value={String.fromCharCode(64 + Number(index_0 + 1))}
                        >
                          {String.fromCharCode(64 + Number(index_0 + 1))}
                        </Radio>
                        <RenderHtml
                          cname="radio_content auto-img"
                          value={item_0.content}
                          onClick={(e) => perviewimg(e)}
                        ></RenderHtml>
                      </div>
                    );
                  },
                )}
              </Radio.Group>
            ) : detail.questions_type === 1 ? ( //多选
              <Checkbox.Group value={detail.questions_answers}>
                {detail.questions_options.map(
                  (item_1: any, index_1: number) => {
                    return (
                      <div className="answer_item" key={index_1}>
                        <Checkbox
                          value={String.fromCharCode(64 + Number(index_1 + 1))}
                        >
                          {String.fromCharCode(64 + Number(index_1 + 1))}
                        </Checkbox>
                        <RenderHtml
                          cname="auto-img"
                          value={item_1.content}
                          onClick={(e) => perviewimg(e)}
                        ></RenderHtml>
                      </div>
                    );
                  },
                )}
              </Checkbox.Group>
            ) : detail.questions_type === 2 ? ( // 填空题
              detail.questions_options.map((item_2: any, index_2: number) => {
                const dataRange = `${item_2?.answerMin}~${item_2?.answerMax}`;
                return (
                  <div className="answer_item blanks" key={index_2}>
                    <span>{`第${index_2 + 1}空：`}</span>
                    {/* <span>{`${item_2.content || dataRange}`}</span> */}
                    <RenderHtml cname="auto-img"  onClick={(e:any) => perviewimg(e)} value={item_2.content || dataRange}></RenderHtml>
                    {/* <div
                      className="spcialDom"
                      dangerouslySetInnerHTML={createMarkup(
                        item_2.content || dataRange,
                      )}
                    ></div> */}
                  </div>
                );
              })
            ) : detail.questions_type === 3 ? ( // 主观题
              <div className="answer_item">
                <span>解析：</span>
                <RenderHtml
                  cname="auto-img"
                  value={detail.questions_analysis}
                  onClick={(e) => perviewimg(e)}
                ></RenderHtml>
              </div>
            ) : (
              // 判断题
              <Radio.Group value={detail.questions_answers[0]}>
                {detail.questions_options.map(
                  (item_4: any, index_4: number) => {
                    return (
                      <div className="answer_item" key={index_4}>
                        <Radio
                          value={String.fromCharCode(64 + Number(index_4 + 1))}
                        >
                          {String.fromCharCode(64 + Number(index_4 + 1))}
                        </Radio>
                        <div className="radio_content">{item_4.content}</div>
                      </div>
                    );
                  },
                )}
              </Radio.Group>
            )}
          </div>
          <div className="see_jiexi">
            <span
              onClick={() => setshowdetail(!showdetail)}
              style={{ marginRight: '5px' }}
            >
              查看解析
            </span>
            {showdetail ? <UpOutlined /> : <DownOutlined />}
          </div>
          {showdetail && (
            <div className="xiangjie">
              <RenderHtml
                value={detail?.questions_analysis}
                onClick={(e) => perviewimg(e)}
              ></RenderHtml>
            </div>
          )}
        </div>
      )}
      <div className="other_view">
        <div className="left_view">
          <span>知识点：</span>
        </div>
        <div className="right_view">
          {detail?.knowledge_points?.map((item_: any, index: number) => {
            if (item_.entity) {
              return (
                <span className="circle_view" key={index}>
                  {item_.entity}
                </span>
              );
            }
          })}
        </div>
      </div>
      <div className="other_view">
        <div className="left_view">
          <span>认知层次：</span>
        </div>
        {detail?.cognitive_level && (
          <div className="right_view">
            <span className="circle_view">{detail?.cognitive_level}</span>
          </div>
        )}
      </div>
      <div className="other_view">
        <div className="left_view">
          <span>标签：</span>
        </div>
        <div className="right_view">
          {detail?.labels?.map((item_: any, index: number) => {
            if (item_) {
              return (
                <span className="circle_view" key={index}>
                  {item_}
                </span>
              );
            }
          })}
        </div>
      </div>
      <div className="other_view">
        <div className="left_view">
          <span>难度：</span>
        </div>
        <div className="right_view">
          <span>{detail?.questions_difficulty}</span>
        </div>
      </div>
      <div className="other_view">
        <div className="left_view">
          <span>适用层次：</span>
        </div>
        <div className="right_view">
          {detail?.questions_level?.map((item_: any, index: number) => {
            let arr = ['本科生', '研究生'];
            return (
              <span style={{ marginRight: '10px' }} key={index}>
                {arr[item_]}
              </span>
            );
          })}
        </div>
      </div>
      <div className="other_view">
        <div className="left_view">
          <span>适用院系/部门：</span>
        </div>
        <div className="right_view">
          {detail?.questions_major?.map((item_: any, index: number) => {
            return (
              <span key={index} style={{ marginRight: '10px' }}>
                {item_.split(',')[1]}
              </span>
            );
          })}
        </div>
      </div>
      <div className="other_view">
        <div className="left_view">
          <span>适用课程：</span>
        </div>
        <div className="right_view">
          {detail?.questionCourseList?.map((item_: any, index: number) => {
            if (item_.courseName) {
              return (
                <span className="circle_view" key={index}>
                  {item_.courseName}
                </span>
              );
            }
          })}
        </div>
      </div>
      <div className="other_view">
        <div className="left_view">
          <span>创建人：</span>
        </div>
        <div className="right_view">
          <span style={{ marginRight: '10px' }}>{detail?.add_username}</span>
        </div>
      </div>
      <div className="other_view">
        <div className="left_view">
          <span>创建时间：</span>
        </div>
        <div className="right_view">
          <span style={{ marginRight: '10px' }}>
            {dayjs(detail?.creator_time).format('YYYY-MM-DD HH:mm:ss')}
          </span>
        </div>
      </div>
      <div className="other_view">
        <div className="left_view">
          <span>分享给：</span>
        </div>
        <div className="right_view">
          {userlist.map((item_: any, index: number) => {
            return (
              <span key={index} style={{ marginRight: '10px' }}>
                {item_.name}
              </span>
            );
          })}
        </div>
      </div>
      {/* 预览图片 */}
      <Image
        width={200}
        style={{ display: 'none' }}
        preview={{
          visible: imgvisible,
          src: perviewSrc,
          onVisibleChange: (value) => {
            setImgVisible(value);
            if (!value) {
              setPerviewSrc('');
            }
          },
        }}
      />
      {/* <div className='content_row'>
        <p>类型</p>
        <p>{examType.optionType_[detail?.questions_type]}</p>
      </div>
      <div className='content_row'>
        <p>难度</p>
        <p>{detail?.questions_difficulty}</p>
      </div>
      <div className='content_row'>
        <p>知识点</p>
        <p>{detail?.knowledge_points?.map((item_: any) =>item_.entity).join(',')}</p>
      </div>
      <div className='content_row'>
        <p>正确率</p>
        <p>{detail?.accuracy}</p>
      </div>
      <div className='content_row'>
        <p>适用层次</p>
        <p>{detail?.questions_level?.map((item_: any) => { return examType.levelType_[Number(item_)] + ' ' })}</p>
      </div>
      <div className='content_row'>
        <p>适用专业</p>
        <p>{codeToName(detail?.questions_major)}</p>
      </div>
      <div className='content_row'>
        <p>适用课程</p>
        <p>{detail?.questions_lesson}</p>
      </div>
      <div className='content_row tag'>
        <p>标签</p>
        <p className='item'>
        {detail?.labels?.map((tag:any, index:number) => {
          const tagElem = (
            <Tag
              className="topic-tag"
              key={index}
              closable={false}
            >
              <span>
                {tag.length > 20 ? `${tag.slice(0, 20)}...` : tag}
              </span>
            </Tag>
          );
          return tag.length > 20 ? (
            <Tooltip title={tag} key={tag}>
              {tagElem}
            </Tooltip>
          ) : (
            tagElem
          );
        })}
        </p>

      </div>
      <div className='content_row'>
        <p>题目</p>
        <p className='spcialDom' dangerouslySetInnerHTML={createMarkup(detail?.questions_content)}></p>
      </div>
      <div className='content_row fileList'>
        <p>题目附件</p>
        <p>
          {detail?.fileList?.map((item:any,index:number)=>{
            return <a href={item.attachmentSource} key={index} target={item.attachmentSource} title={item.attachmentName||''}>{item.attachmentName||''}</a>
          })}
        </p>
      </div>
     {
     detail?.questions_type==3 &&
      <div className='content_row uploadFileList'>
          <p>上传附件</p>
          <p>
            {detail?.hasAttachment?.map((item:any,index:number)=>{
              return <div><span>{item.name}</span><span>{item.required && `(必传*)`}</span></div>
            })}
          </p>
        </div>
      }
      <div className='content_row'>
        <p>答案</p>
        <p>{detail?.questions_answers?.join(',')}</p>
      </div>
      <div className='content_row'>
        <p>解析</p>
        <p className='spcialDom' dangerouslySetInnerHTML={createMarkup(detail?.questions_analysis)}></p>
      </div>
      <div className='content_row'>
        <p>更新时间</p>
        <p>{dayjs(detail?.update_time).format('YYYY-MM-DD HH:mm:ss')}</p>
      </div>
      <div className='content_row'>
        <p>创建人</p>
        <p>{detail?.add_username}</p>
      </div> */}
    </Drawer>
  );
};

export default Preview;
