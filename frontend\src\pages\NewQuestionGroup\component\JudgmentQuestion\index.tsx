/**
 * 判断题
 */

import { Editor, IconFont } from '@/components';
import Retract from '@/images/icons/retract.png';
import Unfold from '@/images/icons/unfold.png';
import { getSensitiveWordPost } from '@/utils';
import { CloseOutlined, PaperClipOutlined } from '@ant-design/icons';
import { Button, message, Radio, Upload } from 'antd';
import { FC, useEffect, useRef, useState } from 'react';
import { IQuestionComponentProp, IQuestionGropItem } from '../../types';
import { fetchEditorContent, formatFileList, initOptions } from '../../utils';
import './index.less';

const JudgmentQuestion: FC<IQuestionComponentProp> = ({
  detail,
  itemIndex,
  editType,
  onChangeItem,
  onDeleteTopic,
}) => {
  const topicId = useRef('topic_' + detail.id);
  const analysisId = useRef('analysis_' + detail.id);

  const [options, setOptions] = useState<any[]>([]); //选项内容
  const [answer, setAnswer] = useState<any>(undefined);
  // 试题是否隐藏
  const [isShowQuestion, setIsShowQuestion] = useState(true);
  // 数据副本
  const initData = useRef(detail);

  const topicChange = (e: any, item: any) => {
    const data = e.level?.content;
    if (item === topicId.current) {
      //通过e.target.content获取的内容可能不是想要的数据，并且直接使用getContent会使图片的src是相对路径，跨项目引用会访问不到,只能手动避开；
      const realTopic = fetchEditorContent(topicId.current) ? data : '';
      if (realTopic === undefined) return; //处理插入化学公式会出现的bug;
      handleUpdate('questions_content', realTopic);
    } else {
      const realAnalysis = fetchEditorContent(analysisId.current) ? data : '';
      handleUpdate('questions_analysis', realAnalysis);
    }
  };

  // options的修改
  const updateOptions = (value: any) => {
    setOptions(value);
    handleUpdate('questions_options', value);
  };

  useEffect(() => {
    if (editType === 'edit') {
      setAnswer(detail?.questions_answers?.[0]);
      setOptions(detail?.questions_options || []);
      setFileList(formatFileList(detail?.fileList || []));
      return;
    }
    initOptions(4, detail, updateOptions);
  }, []);

  useEffect(() => {
    if (detail?.isSort) {
      setAnswer(detail?.questions_answers?.[0]);
      setOptions(detail?.questions_options || []);
      setFileList(formatFileList(detail?.fileList || []));
      handleUpdate('isSort', false);
    }
  }, [detail?.isSort]);

  //#region 文件上传
  const uploadRef = useRef<any>(null);
  const [fileList, setFileList] = useState<any>([]);

  //更新数据
  const handleUpdate = (type: keyof IQuestionGropItem, value: any) => {
    onChangeItem(type, value, initData.current.id);
  };
  // 答案更新
  const updateAnswer = (value: any) => {
    setAnswer(value);
    handleUpdate('questions_answers', !value ? undefined : [value]);
  };

  //附件添加
  const uploadProps = {
    name: 'file',
    action: '/rman/v1/upload/reference/material/import',
    headers: {
      authorization: 'authorization-text',
    },
    beforeUpload: async (file: any, list: any) => {
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('上传文件不能超过100M');
      }
      if (fileList.length + 1 > 10) {
        message.info('上传文件数量不能超过10个');
        return false;
      }
      let names = list.map((e: any) => e.name).join('');
      let res: any = await getSensitiveWordPost(
        names,
        '附件名',
        () => true,
        () => false,
      );
      if (!res) return Upload.LIST_IGNORE;
      return isLt100M || Upload.LIST_IGNORE; //隐藏不符合列表的文件
    },
    fileList,
    showUploadList: {
      removeIcon: <CloseOutlined />,
    },
    maxCount: 10,
    onChange(info: any) {
      setFileList(info.fileList);
      handleUpdate('fileList', info.fileList);
      if (info.file.status !== 'uploading') {
      }
      if (info.file.status === 'done' && info.file.response?.success) {
        message.success(`${info.file.name} 上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败.`);
      } else if (info.file.status === 'done' && !info.file.response?.success) {
        message.error(`${info.file.name} 上传失败,不支持该格式`);
        const cloneList = fileList.map((item: any) => {
          if (item.uid === info.file.uid) {
            return {
              ...item,
              status: 'error',
            };
          }
          return item;
        });
        setFileList(cloneList);
        handleUpdate('fileList', cloneList);
      }
    },
  };
  //#endregion

  //答案选项
  const answerChange = (e: any, item?: any) => {
    //单选题
    // setAnswer(e.target.value);
    updateAnswer(e.target.value);
  };

  return (
    <div className="judge_container">
      <div className="form_item">
        <div className="form_item_header">
          <img
            className="unfold_icon"
            src={isShowQuestion ? Unfold : Retract}
            onClick={() => setIsShowQuestion(!isShowQuestion)}
          />
          <span className="tag"></span>
          <span>小题{itemIndex + 1}</span>
        </div>
        <div className="form_item_body">
          <Editor
            name={topicId.current}
            value={initData.current?.questions_content}
            onChange={(e: any) => topicChange(e, topicId.current)}
            addBlanks={undefined}
            addFile={() => {
              uploadRef.current?.click();
            }}
            // addCase={parameterConfig.target_customer === "tcm" ? () => setCaseVisible(true) : undefined}
            textSetting={{
              max: 5000,
              spaces: true,
              toast: function () {
                message.info(`题目输入不能超过${(this as any).max}个字`);
              },
            }}
          />
          <Button
            title="删除此选项"
            type="link"
            icon={<IconFont type="iconweiwancheng1" />}
            className="del_item"
            onClick={() => onDeleteTopic(initData.current.id)}
          ></Button>
        </div>
        <div className={`enclosure${fileList.length > 0 ? '' : ' hidden'}`}>
          <span className="label">附件列表:</span>
          <Upload {...uploadProps}>
            <Button ref={uploadRef} icon={<PaperClipOutlined />} type="ghost">
              添加附件
            </Button>
          </Upload>
        </div>
      </div>

      {isShowQuestion ? (
        <>
          <div className="form_item">
            <div className="form_item_header">
              <span className="tag"></span>
              <span>选项</span>
            </div>
            <div className="form_item_body">
              <Radio.Group
                className="answer_container"
                onChange={answerChange}
                value={answer}
              >
                {options.map((item: any, index: number) => {
                  return (
                    <div className="answer_list" key={index}>
                      <div>
                        <Radio
                          value={String.fromCharCode(64 + Number(item.seq))}
                        >
                          {String.fromCharCode(64 + Number(item.seq))}
                        </Radio>
                      </div>
                      <span className="judge">
                        {index === 0 ? '正确' : '错误'}
                      </span>
                    </div>
                  );
                })}
              </Radio.Group>
            </div>
          </div>

          <div className="form_item">
            <div className="form_item_header">
              <span className="tag"></span>
              <span>解析</span>
            </div>
            <div className="form_item_body">
              <Editor
                name={analysisId.current}
                value={initData.current?.questions_analysis}
                height={115}
                onChange={(e: any) => topicChange(e, analysisId.current)}
                textSetting={{
                  max: 5000,
                  spaces: true,
                  toast: function () {
                    message.info(`解析输入不能超过${(this as any).max}个字`);
                  },
                }}
              />
            </div>
          </div>
        </>
      ) : (
        <div />
      )}
    </div>
  );
};

export default JudgmentQuestion;
