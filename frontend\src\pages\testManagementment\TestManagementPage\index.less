/* 顶层布局 */
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f7f9fa;

  /* 设置输入框统一宽度 */
  .ant-input,
  .ant-select {
    width: 400px !important;
  }

  .ant-picker {
    width: 440px !important;
  }

  .ant-radio-group {
    width: 400px !important;
  }
}

/* 顶部 Header 样式 */
.header-container {
  background: #ffffff !important;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e0e0e0;
  height: 64px;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.header-title .arrow-icon {
  font-size: 16px;
  margin-right: 8px;
  color: #1890ff;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

/* 主布局 */
.main-layout {
  flex: 1;
  padding: 20px;
  display: flex;
  gap: 10px;
}

/* 左侧导航 */
.sider-container {
  background: #ffffff;
  height: 100%;
  position: sticky;
  top: 0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  border-right: 1px solid #e0e0e0;
  border-radius: 10px;
}

.menu-container {
  height: 100%;
  border-right: 0;
}

/* 内容区域 */
.content-container {
  margin: 0 10px;
  overflow: auto;
  flex: 1;
  background: #ffffff;
  border-radius: 10px;
  padding: 20px;
}

/* 模块样式 */
.module-section {
  margin-bottom: 20px;
  padding: 20px;

  .flexs {
    display: flex;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18px;
    color: #525252;
    line-height: 25px;
    align-items: center;
    margin-bottom: 20px;

    .jux {
      margin-right: 10px;
      width: 4px;
      height: 23px;
      background: var(--primary-color);
    }
  }

  .times {
    display: flex;
    align-items: baseline;

    .ant-select {
      width: 109px !important;
    }
  }

  .ant-upload.ant-upload-drag {
    margin-left: 107px;
    width: 286px;
  }
}

.module-section h2 {
  font-size: 18px;
  margin-bottom: 20px;
}

.Scoresettings {
  .settings_right {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .sptext {
      color: '#595959';
      font-size: 15px;
      font-weight: 600;
    }

    .setting {
      color: #ffffff;
      font-weight: 600;
      margin-left: 25px;
      padding: 3px 10px;
      border-radius: 4px;
      background-color: #559cff;
      cursor: pointer;
    }
  }
}

.table-add-button {
  margin-top: 16px;
}


.basic-info-section {
  margin-bottom: 50px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  background: #ffffff;
}

.basic-info-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  border-left: 4px solid #1890ff;
  padding-left: 8px;
  margin-bottom: 20px;
}

.upload-section {
  display: flex;
  align-items: center;
  gap: 20px;

  .img-box {
    width: 330px;
    height: 186px;
    background: #f7f9fa;
    border-radius: 4px;
    border: 1px solid #cbcbcb;
    margin-bottom: 1%;

    .ant-image {
      width: 100%;
      height: 100%;

      .ant-image-img {
        display: block;
        width: 100%;
        max-height: 100%;
      }

    }
  }
}

.upload-hint {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.Cover {
  display: flex;

}

.Fractions {
  display: flex;

  .ant-input {
    width: 240px !important;
  }
}

//消息
.information {
  display: inline-block;
  margin: 0 10px;

  .ant-input {
    width: 114px !important;
  }
}

.jsTOP {
  margin-top: 20px;

  .jsflex {
    display: inline-block;
    margin: 0 10px;

    .ant-input {
      width: 40px !important;
    }
  }

  // .ma {
  //   margin: 10px;
  // }

  // .spans {
  //   line-height: 48px;
  // }

  .spansLh {
    line-height: 32px;
  }
}

.radio-group {
  display: flex;
  align-items: center;
  gap: 20px;
}

.footer-buttons {
  display: flex;
  justify-content: flex-start;
  gap: 8px;
}
