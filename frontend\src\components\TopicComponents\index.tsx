import examManageApis from '@/api/exam';
import React, { useEffect, useState, } from 'react';
import { history, IConfig, useLocation, useSelector } from 'umi';
import './index.less';
import {
    DownOutlined,
    ExclamationCircleOutlined,
    PlusCircleOutlined,
    SmileOutlined,
} from '@ant-design/icons';
import { Avatar, Badge, Button, Checkbox, Empty, Pagination, Popover, Tooltip, Tree } from 'antd';
import examType from '@/types/examType';
import { IconFont } from '../iconFont/iconFont';
import RenderHtml from '../renderHtml';
import dayjs from 'dayjs';
import { DataNode } from 'antd/lib/tree';
interface props {
    rowSelection: any; // 多选框
    dataSource: any; // 表格数据
    total: number; // 表格数据总数
    handledit?: (Item: any) => any; // 删除
    handpreview?: (Item: any) => any; // 预览
    handedit?: (Item: any) => any; // 编辑
    currentPage?: number; // 当前页码
    onPageChange?: (page: number, pageSize: number) => void; // 分页
    btnList?: any; // 按钮列表
    initCourseTree?: (courseId: string) => any; // 初始化课程树
    handbatchimport?: () => any; // 批量导入
    handnewTopic_?: () => any; // 新增
    onSelectionChange?: (keys: any[], rows: any[]) => void;
    onCourseChange?: (courseIds: string[], chapterIds: string[], sectionIds: string[]) => void;
}
const TopicComponents: React.FC<props> = (props) => {
    const {
        rowSelection,
        dataSource,
        total,
        handledit,
        handedit,
        handpreview,
        onPageChange,
        currentPage,
        btnList,
        handbatchimport,
        handnewTopic_,
        onSelectionChange,
        initCourseTree,
        onCourseChange
    } = props;
    const configs: IConfig = useSelector<{ config: any }, IConfig>(
        ({ config }) => config,
    );
    // 在组件顶部添加状态


    useEffect(() => {
        console.log(rowSelection, 'rowSelection');

        if (rowSelection) {
            setSelectedIds(rowSelection);
            setSelectedItems(selectedItems.filter(item =>
                rowSelection.includes(item.id)
            ));
        } else if (rowSelection.length === 0) {
            setSelectedIds([]);
            setSelectedItems([]);
        }
    }, [rowSelection]);

    const [treeData, setTreeData] = useState<any[]>([]);
    // 课程树
    const [initCourseTrees, setInitCourseTree] = useState<any>([]);

    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedItems, setSelectedItems] = useState<any[]>([]);

    const handleCheckboxChange = (item: any, checked: boolean) => {
        let newSelectedIds;
        let newSelectedItems;

        if (checked) {
            newSelectedIds = [...selectedIds, item.id];
            newSelectedItems = [...selectedItems, item];
        } else {
            newSelectedIds = selectedIds.filter(id => id !== item.id);
            newSelectedItems = selectedItems.filter(i => i.id !== item.id);
        }

        setSelectedIds(newSelectedIds);
        setSelectedItems(newSelectedItems);

        // 调用父组件的处理函数
        if (onSelectionChange) {
            onSelectionChange(newSelectedIds, newSelectedItems);
        }
    };
    const handleSelectAll = (checked: boolean) => {
        let newSelectedIds: string[] = [];
        let newSelectedItems: any[] = [];

        if (checked) {
            newSelectedIds = dataSource.map((item: any) => item.id);
            newSelectedItems = [...dataSource];
        }

        setSelectedIds(newSelectedIds);
        setSelectedItems(newSelectedItems);

        if (onSelectionChange) {
            onSelectionChange(newSelectedIds, newSelectedItems);
        }
    };
    // true 
    // 初次加载一级目录
    useEffect(() => {
        (async () => {
            const res = await examManageApis.TreecoureList();
            if (res.status === 200) {
                setTreeData(
                    res.data.data.map((item: any) => ({
                        title: item.name,
                        key: item.uuid,
                        isLeaf: false,
                        children: [],                // ← add this
                        dataRef: { courseIds: item.courseIds },
                    }))
                );

            }
        })();
    }, []);
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [selectedTreeKeys, setSelectedTreeKeys] = useState<React.Key[]>([]); // 新增状态
    // 递归查找路径
    const findPath = (
        nodes: DataNode[],
        targetKey: string,
        path: DataNode[] = []
    ): DataNode[] | null => {
        for (const node of nodes) {
            const newPath = [...path, node];
            if (node.key === targetKey) {
                return newPath;
            }
            if (node.children) {
                const res = findPath(node.children, targetKey, newPath);
                if (res) return res;
            }
        }
        return null;
    };

    // 异步加载子节点（章节/节）
    const onLoadData = ({ key, children, dataRef }: any): Promise<void> => {
        if (children && children.length) {
            return Promise.resolve();
        }
        const loadFn = dataRef.courseIds
            ? examManageApis.chapterList
            : examManageApis.sectionList;
        const param = dataRef.courseIds
            ? dataRef.courseIds
            : dataRef.chapterIds;
        return loadFn(param).then((res: any) => {
            if (res.status === 200) {
                const newChildren = res.data.map((item: any) => {
                    if (dataRef.courseIds) {
                        // 章节
                        return {
                            title: item.name,
                            key: item.uuid,
                            isLeaf: false,
                            children: [],
                            dataRef: { chapterIds: item.chapterIds }
                        };
                    } else {
                        // 节
                        return {
                            title: item.name,
                            key: item.uuid,
                            isLeaf: true,
                            dataRef: { sectionIds: item.sectionIds, questionIds: item.questionIds }
                        };
                    }
                });
                const updateTree = (list: DataNode[]): DataNode[] =>
                    list.map(node => {
                        if (node.key === key) {
                            return { ...node, children: newChildren };
                        } else if (node.children) {
                            return { ...node, children: updateTree(node.children) };
                        }
                        return node;
                    });
                setTreeData(origin => updateTree(origin));
            }
        });
    };
    // 选中树节点回调：返回从根到当前的所有级别
    const onSelect = (_: React.Key[], info: any) => {
        console.log(expandedKeys, 'expandedKeys');

        const courseIds1: string[] = [];
        const pathNodes = findPath(treeData, info.node.key as string) || [];
        const pathkey = info.node.key.split('-');
        const courseIds: string[] = [];
        const chapterIds: string[] = [];
        const sectionIds: string[] = [];
        console.log(pathkey, 'pathkey');
        if (pathkey?.length === 3) {
            // 解析路径格式如0-1-1
            const [rootIndex, childIndex, grandChildIndex] = pathkey?.map(Number);
            // 获取一级节点数据
            if (treeData[childIndex]?.dataRef?.courseIds) {
                courseIds.push(treeData[childIndex].dataRef.courseIds);
            }

            // 获取二级节点数据
            if (treeData[childIndex]?.children?.[grandChildIndex]?.dataRef?.chapterIds) {
                chapterIds.push(treeData[childIndex].children[grandChildIndex].dataRef.chapterIds);
            }


        } else {
            pathNodes.forEach((node, idx) => {
                if (idx === 0 && node.dataRef.courseIds) {
                    courseIds.push(node.dataRef.courseIds);
                }
                if (idx === 1 && node.dataRef.chapterIds) {
                    chapterIds.push(node.dataRef.chapterIds);
                }
                if (idx === 2 && node.dataRef.sectionIds) {
                    sectionIds.push(node.dataRef.sectionIds);
                }
            });
        }

        console.log(courseIds, chapterIds, sectionIds, 'selected ids');

        onCourseChange && onCourseChange(courseIds, chapterIds, sectionIds);
    };


    return (
        <div className="topic_components">
            <div className='components_right'>
                <div className='right_headerss' >
                    <div className="opt_btns" >
                        <Checkbox
                            checked={selectedIds.length === dataSource.length && dataSource.length > 0}
                            indeterminate={selectedIds.length > 0 && selectedIds.length < dataSource.length}
                            onChange={(e) => handleSelectAll(e.target.checked)}
                        >
                            全选
                        </Checkbox>
                        {configs.mobileFlag ? (
                            <>
                                {btnList.length > 3 ? (
                                    <>
                                        {btnList.slice(0, 1).map((item: any, index: number) => {
                                            return (
                                                <Button
                                                    key={index}
                                                    className={item.disabled ? 'disabled' : ''}
                                                    onClick={() => {
                                                        if (!item.disabled) {
                                                            setOpreatMenuVisible(false);
                                                            item.func();
                                                        }
                                                    }}
                                                >
                                                    {item.dom}
                                                    {item.title}
                                                </Button>
                                            );
                                        })}
                                        {btnList.slice(1, btnList.length).length > 0 && (
                                            <Popover
                                                placement="bottomLeft"
                                                className="mobile_btns_popover"
                                                getPopupContainer={(e: any) => e.parentNode}
                                                onOpenChange={(newOpen: boolean) =>
                                                    setOpreatMenuVisible(newOpen)
                                                }
                                                open={operatMenuVisible}
                                                content={
                                                    <div className="mobile_btns">
                                                        {btnList
                                                            .slice(1, btnList.length)
                                                            .map((item: any, index: number) => {
                                                                return (
                                                                    <div
                                                                        key={index}
                                                                        className={item.disabled ? 'disabled' : ''}
                                                                        onClick={() => {
                                                                            if (!item.disabled) {
                                                                                setOpreatMenuVisible(false);
                                                                                item.func();
                                                                            }
                                                                        }}
                                                                    >
                                                                        {item.dom}
                                                                        <span>{item.title}</span>
                                                                    </div>
                                                                );
                                                            })}
                                                    </div>
                                                }
                                            >
                                                <Button
                                                    onClick={(e: any) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        setOpreatMenuVisible(!operatMenuVisible);
                                                    }}
                                                >
                                                    <IconFont type="iconziyuanku1" />
                                                    管理
                                                </Button>
                                            </Popover>
                                        )}
                                    </>
                                ) : (
                                    btnList.map((item: any, index: number) => {
                                        return (
                                            <Button
                                                key={index}
                                                className={item.disabled ? 'disabled' : ''}
                                                onClick={() => {
                                                    if (!item.disabled) {
                                                        setOpreatMenuVisible(false);
                                                        item.func();
                                                    }
                                                }}
                                            >
                                                {item.dom}
                                            </Button>
                                        );
                                    })
                                )}
                            </>
                        ) : (
                            btnList.map((item: any, index: number) => {
                                return (
                                    <div
                                        key={index}
                                        className={item.disabled ? 'disabled item_' : 'item_'}
                                        onClick={() => {
                                            if (!item.disabled) {
                                                item.func();
                                            }
                                        }}
                                    >
                                        {item.dom}
                                        <span>{item.title}</span>
                                    </div>
                                );
                            })
                        )}
                    </div>
                    <div>
                        <Button onClick={handbatchimport} style={{
                            background: 'rgba(84,156,255,0.1)',
                            borderRadius: '16px',
                            border: '1px solid #549CFF',
                            color: '#549CFF'
                        }} type="primary" >
                            <IconFont type="iconbatchImport" />
                            批量导入
                        </Button>
                        <Button onClick={handnewTopic_} style={{
                            borderRadius: '16px',
                            margin: '0 30px'
                        }} type="primary" >
                            {/* onClick={newTopic_} */}
                            <PlusCircleOutlined />
                            新建题目
                        </Button>
                    </div>
                </div>
                <div className='rightoverflow' >

                    {dataSource.length > 0 ? (
                        dataSource.map((item: any) => (
                            <div style={{ display: 'flex', alignItems: 'center', }} >
                                <div>
                                    <Checkbox
                                        checked={selectedIds.includes(item.id)}
                                        onChange={(e) => handleCheckboxChange(item, e.target.checked)}
                                    />
                                </div>
                                <div onClick={(e) => {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    // batchdelete(record);
                                    handpreview(item)
                                }} className='right_box' >
                                    <div className='right_header' >
                                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
                                            <div className="opt_btns">
                                                <Badge
                                                    className="site-badge-count-109"
                                                    count={examType.optionType_[Number(item.questions_type)]?.charAt(0) ?? ''}
                                                    style={{ backgroundColor: '#DED9FF', color: '#7949FE' }}
                                                />
                                                <div className='item_' title={examType.optionType_[Number(item.questions_type)]} >{examType.optionType_[Number(item.questions_type)]}</div>
                                                {item?.applicationClasses?.length > 0 && (
                                                    <div
                                                        className='item_'
                                                        title={item.applicationClasses.map((cls: any) => cls.applicationClassName).join(',')}
                                                    >
                                                        {item.applicationClasses[0].applicationClassName}
                                                    </div>
                                                )}
                                                {item.assessment?.assessmentName && (
                                                    <div className='item_' title={item.assessment.assessmentName} >
                                                        {item.assessment.assessmentName}
                                                    </div>
                                                )}
                                                {item?.questions_difficulty && (
                                                    <div title={`难度: ${item?.questions_difficulty}`} className='item_'>
                                                        难度: {item?.questions_difficulty}
                                                    </div>
                                                )}

                                                {item?.accuracy && (
                                                    <div className='item_'>
                                                        正确率: {item?.accuracy}
                                                    </div>
                                                )}
                                                {item?.knowledge_points?.length > 0 && (
                                                    <div className='itemBOX' style={{ height: '24px', overflow: 'hidden', flexShrink: 0, maxWidth: '257px' }}>
                                                        {item?.knowledge_points?.map((appItem: any, index: number) => (
                                                            <Badge
                                                                key={index}
                                                                className="site-badge-count-109"
                                                                count={appItem?.entity}
                                                                style={{ borderRadius: '2px', backgroundColor: '#fff4ea', color: '#FF9630', marginLeft: 4, marginBottom: '2px', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis', maxWidth: '100px' }}
                                                            />
                                                        ))}
                                                    </div>
                                                )}

                                            </div>
                                            <div className='opt_bj' >
                                                {/* <Button onClick={(e) => {
                                                    e.stopPropagation();
                                                    e.preventDefault();
                                                    // batchdelete(record);
                                                    handpreview(item)
                                                }} className='dels' type="link" title="预览">
                                                    <IconFont type="iconyulan" />
                                                    <span>预览</span>
                                                </Button> */}
                                                <Button
                                                    type="link"
                                                    className='dels'
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        e.preventDefault();
                                                        // batchdelete(record);
                                                        handledit(item)
                                                    }}
                                                    title="删除"
                                                >
                                                    <IconFont type="iconshanchu-heise-copy" />
                                                    <span>删除</span>
                                                </Button>
                                                <Button
                                                    type="link"
                                                    disabled={!item.editable}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        e.preventDefault();
                                                        // edit(record);
                                                        handedit(item)
                                                    }}
                                                    title="编辑"
                                                >
                                                    <IconFont type="iconbianji-heise" />
                                                    <span>编辑</span>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                    <div className='right_centre' >
                                        <RenderHtml cname="auto-img" value={item.questions_content}></RenderHtml>
                                    </div>
                                    <div className='right_footer' >
                                        <div className='footerBox' >
                                            <div>
                                                {/* <span>
                                                    {(item?.questions_level !== null &&
                                                        (!Array.isArray(item?.questions_level) || item?.questions_level?.length > 0)) && (
                                                            <span className='item_'>
                                                                {[item.questions_level].flat().map(v =>
                                                                    ({ '0': '本科生', '1': '研究生' })[v] || v
                                                                ).join(' ')} ｜
                                                            </span>
                                                        )}
                                                </span> */}
                                                <span>
                                                    {(item?.questions_level !== null &&
                                                        (!Array.isArray(item?.questions_level) || item?.questions_level?.length > 0)) && (
                                                            <span className='item_'
                                                                title={[item.questions_level].flat().map(v =>
                                                                    ({ '0': '本科生', '1': '研究生' })[v] || v
                                                                ).join(' ')}
                                                            >
                                                                {[item.questions_level].flat().map(v =>
                                                                    ({ '0': '本科生', '1': '研究生' })[v] || v
                                                                ).join(' ')} ｜
                                                            </span>
                                                        )}
                                                </span>
                                                {/* questionCourseList */}

                                                {item?.questionCourseList?.length > 0 && (
                                                    (() => {
                                                        const courseNames = item.questionCourseList.map(course => course.courseName);
                                                        const allCourseNames = courseNames.join(', ');
                                                        const displayedCourseNames = courseNames.slice(0, 2).join(', ');
                                                        return (
                                                            <span className='item_' title={allCourseNames}>
                                                                适用课程: {displayedCourseNames}{courseNames.length > 2 ? '...' : ''} ｜
                                                            </span>
                                                        );
                                                    })()
                                                )}

                                                {item?.questions_major && (
                                                    <span
                                                        title={item.questions_major.map(item_ => item_.split(',')[1]).join(', ')}
                                                        className='item_'
                                                    >
                                                        适用院系/部门:
                                                        {item.questions_major.slice(0, 3).map((item_: any, index: number) => (
                                                            <span key={index} style={{ marginRight: '10px' }}>
                                                                {item_.split(',')[1]}
                                                            </span>
                                                        ))}
                                                        {item.questions_major.length > 3 && '...'} ｜
                                                    </span>
                                                )}
                                                {item?.questionSourceName && (
                                                    <span className='item_'>
                                                        题目来源: {item?.questionSourceName} ｜
                                                    </span>
                                                )}
                                                {item?.creator_time && (
                                                    <span className='item_'>
                                                        创建时间: {dayjs(item?.creator_time).format('YYYY-MM-DD HH:mm:ss')} ｜
                                                    </span>
                                                )}
                                                {item?.add_username && (
                                                    <span className='item_'>
                                                        创建人： {item.add_username}
                                                    </span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))
                    ) : (
                        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }}>
                            <Empty />
                        </div>
                    )}
                </div>
                {/* 分页 */}
                <div className='pagetion'>
                    <Pagination
                        current={currentPage}
                        total={total}
                        onChange={(page, size) => {
                            if (props.onPageChange) {
                                props.onPageChange(page, size);
                            }
                        }}
                    />
                </div>
            </div>
            <div className='components_left'>
                <div className='components_left_top'>
                    <div className='left_top_qb' >
                        <span onClick={() => {
                            if (onCourseChange) {
                                onCourseChange([], [], []); // 清空课程、章节和节ID
                            }
                            setExpandedKeys([]);
                            setSelectedTreeKeys([]); // 清空
                        }} >全部题目</span>
                        <Tooltip
                            placement="top"
                            title="通过题目的适用课程、所属章节进行的题目归类"
                            className="question-tooltip"
                        >
                            <ExclamationCircleOutlined />
                        </Tooltip>
                    </div>
                    <div className='trees' >

                        {
                            (treeData && treeData.length > 0) ? (
                                <Tree
                                    treeData={treeData}
                                    loadData={onLoadData}
                                    expandedKeys={expandedKeys}
                                    onExpand={keys => setExpandedKeys(keys)}
                                    onSelect={(_, info) => {
                                        setSelectedTreeKeys([info.node.key]);
                                        onSelect(_, info);
                                    }}
                                    selectedKeys={selectedTreeKeys} // 添加选中键
                                    height={`calc(100vh - 270px)`}
                                    style={{
                                        fontFamily: 'PingFangSC, PingFang SC',
                                        // fontWeight: 500,
                                        fontSize: '16px',
                                        color: '#525252',
                                    }}
                                />
                            ) : (
                                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '60vh' }}>
                                    <Empty description="暂无数据" />
                                </div>
                            )
                        }
                    </div>
                </div>
            </div>
        </div>
    );
};
export default TopicComponents;
