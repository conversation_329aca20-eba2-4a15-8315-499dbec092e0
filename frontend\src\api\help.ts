import HTTP from './index'
// 查询当前页面的指引
export function getguidedirectory(pageCode: string) {
    return HTTP.get(`/unifiedplatform/v1/newhandguide/guidedirectory?pageCode=${pageCode}&isAll=false`).then(res => {
      return res
    }).catch(error => {
        console.error(error)
    })
}


// 查询系统seo配置
export function getSeoConfig() {
    return HTTP.get(`/unifiedplatform/v1/setting/seo`).then(res => {
      return res
    }).catch(error => {
        console.error(error)
    })
}


//敏感词检测
export function querySensitiveWord(detectionText:string) {
  return HTTP.get(`/sensitiveword/match`,{ params:{detectionText}}).then(res => {
    return res
  }).catch(error => {
      console.error(error)
  })
}

// 检测敏感词（大数据情况，使用Post）
export function querySensitiveWordPost(detectionText: string) {
  return HTTP.post(`/sensitiveword/all/match`, {detectionText}).then(res => {
    return res
  }).catch(error => {
      console.error(error)
  })
}