import React, { FC, useState } from 'react';
import { Table, Input, Button, Modal,message } from 'antd';
import examType from '@/types/examType';
import  './index.less'
import { MinusSquareOutlined,PlusSquareOutlined } from '@ant-design/icons';
import { useEffect } from 'react';
import { createMarkup } from '@/utils';
import RenderHtml from '../renderHtml';
interface params {
    title: any,
    tempData?: any,
    visible: boolean,
    callback: (data: any) => void,
    onclose: (data: any) => void,
}
const PointSettingModal: React.FC<params> = (params) => {
    const { title, tempData, visible, callback, onclose } = params
    const [check,setCheck] = useState<boolean>(true);
    const [data, setData] = useState<any>([ //模拟数据结构
        {
            type:0,
            point_set:undefined,
            expand:false,
            children:[
                {
                    questions_content:'1',
                    points:0
                }
            ]
        },
        {
            type:1,
            point_set:undefined,
            expand:false,
            children:[
                {
                    questions_content:'2',
                    points:0
                }
            ]
        }
    ]);
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width:'20%',
            ellipsis: true,
            render: (item: any, record: any,index:number) => (
                <div>{index+1}</div>
            )
        },
        {
            title: '题目',
            dataIndex: 'questions_content',
            width:'60%',
            key: 'questions_content',
            ellipsis: true,
            render: (item: any, record: any) => (
                // <div>{examType.optionType_[Number(item)]}</div>
                <RenderHtml value={item}></RenderHtml>
            )
        },
        {
            title: '分数',
            dataIndex: 'points',
            key: 'points',
            width: '20%',
            ellipsis: true,
            render: (item: any, record: any) => (
                // <div>{examType.optionType_[Number(item)]}</div>
                <Input onChange={(e)=>singleSet(record,e)} value={item}/>
            )
        },
    ];
    useEffect(()=>{
        let temp_:any = [
            {
                type:0,
                point_set:undefined,
                expand:false,
                children:[
                ]
            },
            {
                type:1,
                point_set:undefined,
                expand:false,
                children:[
                ]
            },
            {
                type:2,
                point_set:undefined,
                expand:false,
                children:[
                ]
            },
            {
                type:3,
                point_set:undefined,
                expand:false,
                children:[
                ]
            },
            {
                type:4,
                point_set:undefined,
                expand:false,
                children:[
                ]
            }
        ]
        console.log('tempData',tempData)
        if(tempData?.length>0){ //构造数据
            tempData.forEach((item:any,index:number) => {
                if(item.questions_type === 0){ //单选
                    temp_[0].children.push({
                        questions_content:item.questions_content,
                        id:item.id,
                        type:item.questions_type,
                        points:item.question_mark?item.question_mark:0
                    })
                }
                else if(item.questions_type === 1){ //多选
                    temp_[1].children.push({
                        questions_content:item.questions_content,
                        id:item.id,
                        type:item.questions_type,
                        points:item.question_mark?item.question_mark:0
                    })
                }
                else if(item.questions_type === 2){ //填空
                    temp_[2].children.push({
                        questions_content:item.questions_content,
                        id:item.id,
                        type:item.questions_type,
                        points:item.question_mark?item.question_mark:0
                    })
                }
                else if(item.questions_type === 3){ //主观
                    temp_[3].children.push({
                        questions_content:item.questions_content,
                        id:item.id,
                        type:item.questions_type,
                        points:item.question_mark?item.question_mark:0
                    })
                }else{ //判断
                    temp_[4].children.push({
                        questions_content:item.questions_content,
                        id:item.id,
                        type:item.questions_type,
                        points:item.question_mark?item.question_mark:0
                    })
                }
            });
        }
        const result = temp_.filter((item:any)=>item.children.length>0);
        console.log('result',result)

        setData(result)
    },[tempData])
    // useEffect(()=>{
    //     for(let i=0;i<data.length;i++){
    //         const flag = data[i].children.some((item_:any,index_:number)=>item_.points<0);
    //         if(flag){
    //             setCheck(false)
    //             break;
    //         }
    //     }
    // },[data])
    const on=(item:any)=>{
        // console.log('当前设置对象',item)
        const temp = JSON.parse(JSON.stringify(data));
        temp.forEach((item_:any,index:number) => {
            if(item_.type === item.type){
                item_.expand = true
            }
        });
        setData(temp)
    }
    const off=(item:any)=>{
        // console.log('当前设置对象',item)
        const temp = JSON.parse(JSON.stringify(data));
        temp.forEach((item_:any,index:number) => {
            if(item_.type === item.type){
                item_.expand = false
            }
        });
        setData(temp)
    }
    const batchSet=(item:any,e:any)=>{
        // console.log('当前设置对象',item,e)
        const num = Number(e.target.value);
        if(num < 0) {
            message.error('分数不能为负数');
            return
        }
        if(isNaN(num)){
            message.error('请设置有效分数');
            return
        }
        const temp = JSON.parse(JSON.stringify(data));
        for(let i=0;i<temp.length;i++){
            if(temp[i].type===item.type){
                temp[i].children?.map((item_:any)=>{
                    item_.points = num
                })
                temp[i].point_set = num
                break;
            }
        }
        setData(temp);
    }
    //单个设置
    const singleSet=(item:any,e:any)=>{
        // console.log('当前设置对象',item)
        const num = Number(e.target.value);
        if(num < 0) {
            message.error('分数不能为负数');
            return
        }
        if(isNaN(num)){
            message.error('请设置有效分数');
            return
        }
        const temp = JSON.parse(JSON.stringify(data));
        let flag:boolean = true;
        for(let i=0;i<temp.length;i++){
            if(temp[i].type===item.type){
                temp[i].children?.map((item_:any)=>{
                    if(item_.id === item.id){
                        item_.points = num
                    }
                    if(item_.points !== temp[i].children[0].points){
                        flag = false
                    }
                })
                if(!flag){
                    temp[i].point_set = undefined
                }else{
                    temp[i].point_set = num
                }
                break;
            }
        }
        setData(temp)
    }
    return (
        <Modal
            title={title}
            visible={visible}
            onCancel={onclose}
            onOk={()=>callback(data)}
            width={740}
            className='point_modal'
        >
            <div className='header'>
                <span></span>
                <span>题型</span>
                <span>分数</span>
            </div>
            {
                data.map((item:any,index:number)=>{
                    return <>
                        <div className='content_row' 
                            // key={JSON.stringify(item)}
                            >
                            {item.expand?<MinusSquareOutlined  onClick={()=>off(item)}/>:<PlusSquareOutlined  onClick={()=>on(item)}/>}
                            <span>{examType.optionType_[Number(item.type)]}</span>
                            <Input onChange={(e:any)=>batchSet(item,e)}  value={item.point_set}/>
                        </div>
                        {item.expand && <Table
                            columns={columns}
                            rowKey={'id'}
                            dataSource={item.children}
                            pagination={false}
                        />}
                    </>
                })
            }
        </Modal>
    );
};

export default PointSettingModal;