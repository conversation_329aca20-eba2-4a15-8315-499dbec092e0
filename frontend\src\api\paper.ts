import HTTP from ".";

/**
 * @msg: 上传文件流
 * @param {any} data
 * @return {*}
 */
namespace paperManageApis {
  //试卷列表查询
  export const fetchPaperList = (data: any) =>
    HTTP(`/exam-api/paper/list`, {
      method: 'POST',
      data
    });
  //试卷新增
  export const addPaper = (data: any) =>
    HTTP(`/exam-api/paper/add`, {
      method: 'POST',
      data
    });
  //试卷详情
  export const paperDetail = (data: any) =>
    HTTP(`/exam-api/paper/detail/${data}`, {
      method: 'GET',
    });
  //试卷修改
  export const paperUpdate = (id: any, data: any) =>
    HTTP(`/exam-api/paper/update/${id}`, {
      method: 'POST',
      data
    });
  //试题批量复制
  export const paperBatchCopy = (data: any) =>
    HTTP(`/exam-api/paper/copy`, {
      method: 'POST',
      data
    });
  //试题批量分享
  export const paperBatchShare = (data: any) =>
    HTTP(`/exam-api/paper/share`, {
      method: 'POST',
      data
    });
  //试题批量删除
  export const paperBatchDelete = (data: any) =>
    HTTP(`/exam-api/paper/delete`, {
      method: 'POST',
      data
    });
}


export default paperManageApis
