:global {
  .topic_manage {
    .content {
      background-color: red !important;
      .ant-table-wrapper {
        .ant-spin-container {
          background-color: red !important;
        }
      }
    }
  }
  .ant-table-expanded-row-fixed {
    position: relative !important;
  }
}
.group_expand {
  background-color: red !important;
  .ant-table-expanded-row-fixed {
    position: relative !important;
  }
}
.topic_manage {
  
  height: calc(100% - 40px);
  .search_box {
    margin: 20px 20px 0px 20px;
    #topic_form {
      display: flex;
      .form_select {
        flex: 0 0 10%;
      }
      .form_select_major {
        flex: 1;
      }
      .form_input {
        flex: 0 0 14%;
      }
      > .ant-form-item {
        margin-right: 30px;
        border-radius: 4px;
        &.last {
          // margin: 0;
          margin-right: 0;
        }
      }
      .ant-btn {
        height: 32px;
        margin-right: 20px;
        border-radius: 4px;
        margin: 0;
      }
      .moreSearch {
        &.none {
          top: -100%;
        }
        position: fixed;
        top: 0;
        left: 0;
        transition: top 0.5s;
        background: white;
        z-index: 1000;
        width: 100%;
        // height: 60%;
        padding: 5px 5%;
        .head {
          margin: 20px 0;
          text-align: center;
          > span:first-child {
            font-size: 18px;
          }
          .anticon {
            position: absolute;
            right: 7%;
            line-height: 30px;
          }
        }
        .public_group {
          margin-top: 20px;
          > .item {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-bottom: 20px;
            > div {
              width: 100%;
            }
          }
          .ant-picker-time-panel {
            display: none;
          }
        }
        .btns {
          display: flex;
          flex-direction: row;
          justify-content: space-evenly;
          flex-wrap: nowrap;
          margin-bottom: 10px;
        }
      }
    }
  }
  .split_line {
    width: 100%;
    height: 1px;
    border-bottom: 2px #F7F9FA solid;
  }
  .content {
    height: calc(100% - 120px);
    margin: 0px 30px;
    .opt_btn {
      margin-bottom: 20px;
      display: flex;
      flex-direction: row;
      button.ant-btn.ant-btn-text:not(:last-child) {
        margin: 0;
      }
      .disabled {
        color: rgba(0, 0, 0, 0.25);
      }
      .item_ {
        padding: 4px 15px;
        position: relative;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
          .ant-btn {
            color: var(--primary-color);
          }
          color: var(--primary-color);
        }
        cursor: pointer;
        &.disabled {
          cursor: no-drop;
          color: rgba(0, 0, 0, 0.25) !important;
          .ant-btn {
            color: rgba(0, 0, 0, 0.25) !important;
          }
        }
        > span:last-child {
          margin-left: 8px;
        }
      }
      .item_:not(:last-child) {
        &::after {
          content: '';
          display: inline-block;
          width: 1px;
          height: 16px;
          background: #d8d8d8;
          right: 0;
          top: 8px;
          position: absolute;
        }
      }
      .ant-btn {
        margin-right: 14px;
        display: flex;
        justify-items: center;
        align-items: center;
        margin: 0;
        span.anticon {
          font-size: 16px;
        }
        &.ant-btn-primary {
          border-radius: 16px;
        }
      }
      .ant-btn-link:not(:last-child)::after {
        content: '';
        display: inline-block;
        width: 1px;
        height: 16px;
        background: #d8d8d8;
        right: 0;
        top: 8px;
        position: absolute;
      }
    }
    .ant-table-wrapper {
      .table_opt {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        .ant-btn {
          padding: 0;
        }
      }
      .ant-table-body {
        overflow-x: hidden;
        .ant-table-row {
          .ant-table-cell {
            p {
              margin-bottom: 0;
            }
            .spcialDom {
              max-height: 100px;
              overflow: hidden;
              img {
                max-width: 50px;
                height: auto;
                vertical-align: bottom;
              }
            }
            .auto-img {
              max-height: 100px;
              overflow: hidden;
              img {
                // max-width: 50px;
                height: auto;
                vertical-align: bottom;
              }
            }
          }
        }
      }
    }
  }
}
.topic_manage_mobile {
  height: calc(100% - 30px) !important;
  .content {
    height: 100%;
    .opt_btn {
      margin-bottom: 10px !important;
    }
    .list {
      height: calc(100% - 37px);
      .ant-checkbox-group {
        height: 100%;
        width: 100%;
        overflow-y: auto;
        .item {
          margin-bottom: 10px;
          width: 100%;
          background: #ffffff;
          box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.07);
          border-radius: 10px;
          padding: 15px;
          border: 1px solid #eee;
          .head {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            white-space: nowrap;
            color: #272727;
            font-size: 16px;
            line-height: 20px;
            margin-bottom: 15px;
            .type {
              font-weight: bold;
              margin-left: -4px;
            }
            .topic {
              max-height: 44px;
              width: calc(100% - 72px);
              overflow: hidden;
              text-overflow: ellipsis;
              > p {
                margin-bottom: 0;
              }
            }
          }
          .answer,
          .analysis {
            font-weight: bold;
            font-size: 14px;
            line-height: 16px;
            padding: 0 0 8px 15px;
            color: #525252;
            max-height: 44px;
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            white-space: nowrap;
            overflow: hidden;
            > div {
              > p {
                margin-bottom: 0;
              }
            }
          }
          .analysis {
            border-bottom: 1px solid #f0f0f0;
          }
          .info {
            > div {
              display: flex;
              align-items: center;
              margin-top: 8px;
              > div {
                width: 50%;
                display: flex;
                align-items: center;
                > label {
                  margin-right: 5px;
                  color: #9d9d9d;
                  &::after {
                    content: ':';
                  }
                }
                > span {
                  width: calc(100% - 65px);
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
            }
          }
          .bottom {
            margin: 15px 0 0;
            display: flex;
            justify-content: space-between;
            flex-direction: row;
            align-items: center;
            .ant-btn {
              border-color: var(--primary-color);
              color: var(--primary-color);
            }
          }
        }
      }
    }
    .pagination {
      display: flex;
      align-items: center;
      flex-direction: row;
      justify-content: center;
    }
  }
}
