.test_header_container {
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  .header_left {
    display: flex;
    gap: 20px;
    .test_title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 22px;
      color: #282c3c;
    }
    .test_time {
      width: 166px;
      height: 38px;
      background: rgba(241, 84, 74, 0.1);
      border-radius: 6px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-weight: 500;
      font-size: 14px;
      color: #f1544a;
      .test_time_text {
        font-weight: 500;
        font-size: 14px;
        color: #f1544a;
      }
      .test_left_time {
        // width: 28px;
        height: 28px;
        padding: 0 5px;
        background: #ffffff;
        border-radius: 4px;
        font-weight: bold;
        font-size: 18px;
        color: #f1544a;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .header_right {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    .btns {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;
      .btn_item {
        width: 104px;
        height: 36px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid var(--primary-color);
        font-weight: 400;
        font-size: 16px;
        color: var(--primary-color);
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .primary {
        background: var(--primary-color);
        color: #ffffff;
      }
    }
    .tester_info {
      display: flex;
      gap: 10px;
      align-items: center;
      .test_avatar {
        width: 46px;
        height: 46px;
        border-radius: 50%;
        background-color: #f1544a;
      }
      .test_desc {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        .username {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #2a2a2a;
          max-width: 100px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .phone {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #525252;
        }
      }
    }
  }
}
