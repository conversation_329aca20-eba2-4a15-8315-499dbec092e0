import paperManageApis from '@/api/Contract';
import { IconFont } from '@/components';
import TestSystemHeader from '@/components/TestSystemHeader';
import { FC, useEffect, useState } from 'react';
import ClassifyContent from './ClassifyContent';
import './index.less';
import { useSelector } from 'umi';

const TestReport: FC = () => {
  const [menuList, setMenuList] = useState<any[]>([]);

  const [selectedMenu, setSelectMenu] = useState<any>('');
  //
  const [menuTabList, setMenuTabList] = useState<any[]>([]);

  const fetchClassify = async () => {
    const res = await paperManageApis.classification({ page: 1, size: 100 });
    if (res.status === 200) {
      setMenuList(res.data?.data || []);
      setSelectMenu(res.data?.data?.[0]?.id || '');
      setMenuTabList(res.data?.data?.[0]?.children || []);
    }
  };
  useEffect(() => {
    fetchClassify();
  }, []);
  // 左边菜单切换
  const handleChangeMunu = (id: string) => {
    if (id !== selectedMenu) {
      setSelectMenu(id);
      const children = menuList?.find((item: any) => item.id === id)?.children;
      setMenuTabList(children || []);
    }
  };
  const { title, logoUrl, isShow } = useSelector<any, any>(
    state => state.themes,
  );
  return (
    <div className="test_report_container">
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          backgroundColor: ' #ffffff'
        }}
        onClick={() => {
          window.location.href = '/exam/#/testsystem';
        }}
        className="header_clickable"
      >
        <span className='icon_box'>
          <img
            src={logoUrl || require('@/images/login/default_logo.png')}
            style={{
              height: '32px'
            }}
          />
        </span>
        <TestSystemHeader title="测验报告" />
      </div>
      <div className="content_container">
        <div className="left_menu_container">
          {menuList.map((item: any) => {
            return (
              <div
                onClick={() => handleChangeMunu(item.id)}
                className={`menu_item ${item.id === selectedMenu ? 'menu_item_active' : ''
                  }`}
                key={item.id}
              >
                <IconFont type={item?.icon || 'iconhuishouzhan'} />
                {item.name}
              </div>
            );
          })}
        </div>
        <div className="right_content_container">
          <ClassifyContent tabList={menuTabList} />
        </div>
      </div>
    </div>
  );
};

export default TestReport;
