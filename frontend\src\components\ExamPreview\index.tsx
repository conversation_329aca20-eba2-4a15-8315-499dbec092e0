import examType from '@/types/examType';
import { noSupport } from '@/utils';
import { Button, Checkbox, Drawer, Empty, Modal, Radio } from 'antd';
import React, { useEffect } from 'react';
import { IConfig, useSelector } from 'umi';
import RenderHtml from '../renderHtml';
import './index.less';
interface previewprops {
    detail: any;
    editObject?: any;
    title?: string;
    visible: boolean;
    onClose: () => void;
    handlecopy?: () => void;
    handledit?: () => void;
}

const ExamPreview: React.FC<previewprops> = (props) => {
    const { detail, title, visible, onClose, handlecopy, handledit, editObject } =
        props;
    const close = () => {
        onClose();
    };
    const copy = () => {
        if (configs.mobileFlag) {
            noSupport();
            return;
        }
        handlecopy && handlecopy();
    };
    const configs: IConfig = useSelector<{ config: any }, IConfig>(
        ({ config }) => config,
    );
    useEffect(() => {

    }, [detail]);

    const edit = () => {
        handledit && handledit();
    };
    return (
        <Modal
            open={visible}
            width={'53%'}
            className="paperPreview"
            title={title ? title : '题目预览'}
            onCancel={close}
            footer={null}
        >
            {
                detail?.part?.map((item: any, index: number) => {
                    return (
                        <div className="content_row" key={index}>
                            <div className="content">
                                <h2>
                                    {item.name}
                                </h2>

                                <div>
                                    {Array.isArray(item.questionList) && item.questionList.length > 0 ? (
                                        <div className="questions_list">
                                            {item.questionList.map((item: any, qIndex: number) => {
                                                return (
                                                    <div className="answers" key={qIndex}>
                                                        <div className="type">
                                                            <span className='fs' >{qIndex + 1}、</span>
                                                            <div>
                                                                (
                                                                <span  >{`${examType.optionType_[item.questions_type]}题: ${item.score ?? 0
                                                                    }分`}  </span>
                                                                )
                                                            </div>
                                                            <div className='fs'  >
                                                                <RenderHtml cname="auto-img"
                                                                    value={item.questions_content}>
                                                                </RenderHtml>
                                                            </div>
                                                        </div>
                                                        <div className='type_xz' >
                                                            {item.questions_type === 0 ? ( //单选
                                                                <Radio.Group value={item?.questions_answers[0]}>
                                                                    {item.questions_options.map(
                                                                        (item_0: any, index_0: number) => {
                                                                            return (
                                                                                <div className="answer_item" key={index_0}>
                                                                                    <Radio
                                                                                        value={String.fromCharCode(
                                                                                            64 + Number(index_0 + 1),
                                                                                        )}
                                                                                    >
                                                                                        {String.fromCharCode(64 + Number(index_0 + 1))}
                                                                                    </Radio>
                                                                                    <RenderHtml
                                                                                        cname="radio_content spcialDom"
                                                                                        value={item_0.content}
                                                                                    ></RenderHtml>
                                                                                </div>
                                                                            );
                                                                        },
                                                                    )}
                                                                </Radio.Group>
                                                            ) : item.questions_type === 1 ? ( //多选
                                                                <Checkbox.Group value={item?.questions_answers}>
                                                                    {item.questions_options.map(
                                                                        (item_1: any, index_1: number) => {
                                                                            return (
                                                                                <div className="answer_item" key={index_1}>
                                                                                    <Checkbox
                                                                                        value={String.fromCharCode(
                                                                                            64 + Number(index_1 + 1),
                                                                                        )}
                                                                                    >
                                                                                        {String.fromCharCode(64 + Number(index_1 + 1))}
                                                                                    </Checkbox>
                                                                                    <RenderHtml
                                                                                        cname="spcialDom"
                                                                                        value={item_1.content}
                                                                                    ></RenderHtml>
                                                                                </div>
                                                                            );
                                                                        },
                                                                    )}
                                                                </Checkbox.Group>
                                                            ) : item.questions_type === 2 ? ( // 填空题
                                                                item.questions_options.map((item_2: any, index_2: number) => {
                                                                    const blankAnswer =
                                                                        item_2?.answerRange === 2
                                                                            ? `${item_2.answerMin}~${item_2.answerMax}`
                                                                            : item_2.content;

                                                                    return (
                                                                        <div className="answer_item blanks" key={index_2}>
                                                                            <span>{`第${index_2 + 1}空：`}</span>
                                                                            {/* <span>{`${item_2.content}`}</span> */}
                                                                            <RenderHtml
                                                                                cname="spcialDom"
                                                                                value={blankAnswer}
                                                                            ></RenderHtml>
                                                                        </div>
                                                                    );
                                                                })
                                                            ) : item.questions_type === 3 ? ( // 主观题
                                                                <div className="answer_item" key={index}>
                                                                    <span>解析：</span>
                                                                    <RenderHtml
                                                                        cname="spcialDom"
                                                                        value={item.questions_analysis}
                                                                    ></RenderHtml>
                                                                </div>
                                                            ) : item.questions_type === 5 ? ( // 题组
                                                                <div className="" key={index}>

                                                                    {item?.groupQuestions?.map((queItem: any) => {
                                                                        return (
                                                                            <div className="content_row" key={queItem.id}>
                                                                                <div className="content">
                                                                                    <div className="type" style={{ display: 'flex', alignItems: ' baseline' }}>
                                                                                        (
                                                                                        <span>{`${examType.optionType_[queItem.questions_type]
                                                                                            }题`}</span>
                                                                                        )
                                                                                        <RenderHtml
                                                                                            cname="auto-img"
                                                                                            value={queItem.questions_content}
                                                                                        // onClick={(e: any) => perviewimg(e)}
                                                                                        ></RenderHtml>
                                                                                    </div>

                                                                                </div>
                                                                                {queItem.fileList?.length > 0 && (
                                                                                    <div className="fileList_">
                                                                                        <span>题目附件：</span>
                                                                                        <div>
                                                                                            {queItem.fileList.map((item: any, index: number) => {
                                                                                                return (
                                                                                                    <a
                                                                                                        href={item.attachmentSource}
                                                                                                        key={index}
                                                                                                        target={item.attachmentSource}
                                                                                                        title={item.attachmentName || ''}
                                                                                                    >
                                                                                                        {item.attachmentName || ''}
                                                                                                    </a>
                                                                                                );
                                                                                            })}
                                                                                        </div>
                                                                                    </div>
                                                                                )}
                                                                                {queItem.hasAttachment?.length > 0 && (
                                                                                    <div className="fileList_upload">
                                                                                        <span>上传附件：</span>
                                                                                        <div>
                                                                                            {queItem.hasAttachment.map((item: any, index: number) => {
                                                                                                return (
                                                                                                    <div key={index}>
                                                                                                        <span>{item.name}</span>
                                                                                                        <span>{item.required && `(必传*)`}</span>
                                                                                                    </div>
                                                                                                );
                                                                                            })}
                                                                                        </div>
                                                                                    </div>
                                                                                )}
                                                                                <div className="answers">
                                                                                    {queItem.questions_type === 0 ? ( //单选
                                                                                        <Radio.Group value={queItem?.questions_answers?.[0] || null}>
                                                                                            {queItem.questions_options.map(
                                                                                                (item_0: any, index_0: number) => {
                                                                                                    return (
                                                                                                        <div className="answer_item" key={index_0}>
                                                                                                            <Radio
                                                                                                                value={String.fromCharCode(
                                                                                                                    64 + Number(index_0 + 1),
                                                                                                                )}
                                                                                                            >
                                                                                                                {String.fromCharCode(64 + Number(index_0 + 1))}
                                                                                                            </Radio>
                                                                                                            <RenderHtml
                                                                                                                cname="radio_content auto-img"
                                                                                                                value={item_0.content}
                                                                                                                onClick={(e: any) => perviewimg(e)}
                                                                                                            ></RenderHtml>
                                                                                                        </div>
                                                                                                    );
                                                                                                },
                                                                                            )}
                                                                                        </Radio.Group>
                                                                                    ) : queItem.questions_type === 1 ? ( //多选
                                                                                        <Checkbox.Group value={queItem.questions_answers}>
                                                                                            {queItem.questions_options.map(
                                                                                                (item_1: any, index_1: number) => {
                                                                                                    return (
                                                                                                        <div className="answer_item" key={index_1}>
                                                                                                            <Checkbox
                                                                                                                value={String.fromCharCode(
                                                                                                                    64 + Number(index_1 + 1),
                                                                                                                )}
                                                                                                            >
                                                                                                                {String.fromCharCode(64 + Number(index_1 + 1))}
                                                                                                            </Checkbox>
                                                                                                            <RenderHtml
                                                                                                                cname="auto-img"
                                                                                                                value={item_1.content}
                                                                                                                onClick={(e: any) => perviewimg(e)}
                                                                                                            ></RenderHtml>
                                                                                                        </div>
                                                                                                    );
                                                                                                },
                                                                                            )}
                                                                                        </Checkbox.Group>
                                                                                    ) : queItem.questions_type === 2 ? ( // 填空题
                                                                                        queItem.questions_options.map(
                                                                                            (item_2: any, index_2: number) => {
                                                                                                const dataRange = `${item_2?.answerMin}~${item_2?.answerMax}`;
                                                                                                return (
                                                                                                    <div className="answer_item blanks" key={index_2}>
                                                                                                        <span>{`第${index_2 + 1}空：`}</span>
                                                                                                        {/* <span>{`${item_2.content || dataRange}`}</span> */}
                                                                                                        <RenderHtml
                                                                                                            cname="auto-img"
                                                                                                            onClick={(e: any) => perviewimg(e)}
                                                                                                            value={item_2.content || dataRange}
                                                                                                        ></RenderHtml>
                                                                                                    </div>
                                                                                                );
                                                                                            },
                                                                                        )
                                                                                    ) : queItem.questions_type === 3 ? ( // 主观题
                                                                                        <div className="answer_item">
                                                                                            <span>解析：</span>
                                                                                            {/* <RenderHtml
                                                                                            cname="auto-img"
                                                                                            value={queItem.questions_analysis}
                                                                                            onClick={(e: any) => perviewimg(e)}
                                                                                        ></RenderHtml> */}
                                                                                        </div>
                                                                                    ) : (
                                                                                        // 判断题
                                                                                        <Radio.Group value={queItem?.questions_answers?.[0] || null}>
                                                                                            {queItem.questions_options.map(
                                                                                                (item_4: any, index_4: number) => {
                                                                                                    return (
                                                                                                        <div className="answer_item" key={index_4}>
                                                                                                            <Radio
                                                                                                                value={String.fromCharCode(
                                                                                                                    64 + Number(index_4 + 1),
                                                                                                                )}
                                                                                                            >
                                                                                                                {String.fromCharCode(64 + Number(index_4 + 1))}
                                                                                                            </Radio>
                                                                                                            <div className="radio_content">
                                                                                                                {item_4.content}
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    );
                                                                                                },
                                                                                            )}
                                                                                        </Radio.Group>
                                                                                    )}
                                                                                </div>
                                                                                {/* <div className="see_jiexi">
                                                                                <span
                                                                                    onClick={() => handleShowAnalyze(queItem.id)}
                                                                                    style={{ marginRight: '5px' }}
                                                                                >
                                                                                    查看解析
                                                                                </span>
                                                                                {queItem.showDetail ? <UpOutlined /> : <DownOutlined />}
                                                                            </div>
                                                                            {queItem.showDetail && (
                                                                                <div className="xiangjie">
                                                                                    <RenderHtml
                                                                                        value={queItem?.questions_analysis}
                                                                                        onClick={(e: any) => perviewimg(e)}
                                                                                    ></RenderHtml>
                                                                                </div>
                                                                            )} */}
                                                                            </div>
                                                                        );
                                                                    })}


                                                                </div>

                                                            ) : (
                                                                // 判断题  value={item?.questions_answers[0]}
                                                                <Radio.Group  value={item?.questions_answers?.[0] || null} >
                                                                    {item.questions_options.map(
                                                                        (item_4: any, index_4: number) => {
                                                                            return (
                                                                                <div className="answer_item" key={index_4}>
                                                                                    <Radio
                                                                                        value={String.fromCharCode(
                                                                                            64 + Number(index_4 + 1),
                                                                                        )}
                                                                                    >
                                                                                        {String.fromCharCode(64 + Number(index_4 + 1))}
                                                                                    </Radio>
                                                                                    <div className="radio_content">
                                                                                        {item_4.content}
                                                                                    </div>
                                                                                </div>
                                                                            );
                                                                        },
                                                                    )}
                                                                </Radio.Group>
                                                            )}
                                                        </div>

                                                    </div>
                                                );
                                            })}
                                        </div>
                                    ) : (
                                        <div>暂无问题列表</div> // 如果没有 questionList 或者为空
                                    )}
                                </div>
                            </div>
                        </div>
                    );
                })
            }
        </Modal>
    );
};

export default ExamPreview;
