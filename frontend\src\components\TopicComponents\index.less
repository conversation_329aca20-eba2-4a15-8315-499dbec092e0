.topic_components {
  width: 100%;
  display: flex;
  // height: calc(100vh - 269px);


  .components_right {
    width: 80%;
    // 保留纵向滚动条
    background: #FFFFFF;
    border-radius: 6px;
    padding: 10px;

    .rightoverflow {
      height: calc(100vh - 280px);
      // overflow-x: hidden; // 隐藏横向滚动条
      overflow-y: auto;
    }

    .right_headerss {
      padding: 15px 0;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .opt_btns {
        display: flex;
        flex-direction: row;
        align-items: center;

        button.ant-btn.ant-btn-text:not(:last-child) {
          margin: 0;
        }

        .disabled {
          color: rgba(0, 0, 0, 0.25);
        }

        .item_ {
          padding: 4px 15px;
          position: relative;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            .ant-btn {
              color: var(--primary-color);
            }

            color: var(--primary-color);
          }

          cursor: pointer;

          &.disabled {
            cursor: no-drop;
            color: rgba(0, 0, 0, 0.25) !important;

            .ant-btn {
              color: rgba(0, 0, 0, 0.25) !important;
            }
          }

          >span:last-child {
            margin-left: 8px;
          }
        }

        .item_:not(:last-child) {
          &::after {
            content: '';
            display: inline-block;
            width: 1px;
            height: 16px;
            background: #d8d8d8;
            right: 0;
            top: 8px;
            position: absolute;
          }
        }

        .ant-btn {
          margin-right: 14px;
          display: flex;
          justify-items: center;
          align-items: center;
          margin: 0;

          span.anticon {
            font-size: 16px;
          }

          &.ant-btn-primary {
            border-radius: 16px;
          }
        }

        .ant-btn-link:not(:last-child)::after {
          content: '';
          display: inline-block;
          width: 1px;
          height: 16px;
          background: #d8d8d8;
          right: 0;
          top: 8px;
          position: absolute;
        }
      }
    }

    .pagetion {
      display: flex;
      justify-content: center;
      align-items: center;
      // margin: 20px 0;
      margin-top: 20px;
    }

    .right_box {
      // padding-left: 10px;
      padding: 0 10px;
      // max-width: 807px;
      margin: 10px 10px;
      width: 100%;
      border-bottom: 1px solid #f2f2f2;
      background: #FFFFFF;
      box-shadow: 2px 2px 11px 0px rgba(192, 192, 192, 0.5);
      border-radius: 6px;

      .right_header {
        .opt_btns {
          padding: 10px 0;
          min-width: 541px;
          // padding: 10px 0;
          // margin-bottom: 20px;
          display: flex;
          flex-direction: row;
          align-items: center;

          button.ant-btn.ant-btn-text:not(:last-child) {
            margin: 0;
          }

          .disabled {
            color: rgba(0, 0, 0, 0.25);
          }

          .item_ {
            padding: 4px 16px; // 增大左右内边距，增加间距
            position: relative;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #525252;
            max-width: 160px; // 限制最大宽度，超出显示省略号
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            /* 显示不下时出现... */
            // margin: 0 5px;
            // margin-right: 12px; // 增加右侧外边距，防止按钮挤在一起
            // &:hover {
            //   .ant-btn {
            //     color: var(--primary-color);
            //   }

            //   color: var(--primary-color);
            // }

            cursor: pointer;

            &.disabled {
              cursor: no-drop;
              color: rgba(0, 0, 0, 0.25) !important;

              .ant-btn {
                color: rgba(0, 0, 0, 0.25) !important;
              }
            }

            >span:last-child {
              margin-left: 8px;
            }
          }

          .item_:not(:last-child) {
            &::after {
              content: '';
              display: inline-block;
              width: 1px;
              height: 16px;
              background: #d8d8d8;
              right: 0;
              top: 8px;
              position: absolute;
            }
          }

          .ant-btn {
            margin-right: 14px;
            display: flex;
            justify-items: center;
            align-items: center;
            margin: 0;

            span.anticon {
              font-size: 16px;
            }

            &.ant-btn-primary {
              border-radius: 16px;
            }
          }

          .ant-btn-link:not(:last-child)::after {
            content: '';
            display: inline-block;
            width: 1px;
            height: 16px;
            background: #d8d8d8;
            right: 0;
            top: 8px;
            position: absolute;
          }
        }

        .opt_bj {
          display: flex;
          // padding-right: 40px;

          .dels:not(:last-child) {
            &::after {
              content: '';
              display: inline-block;
              width: 1px;
              height: 16px;
              background: #d8d8d8;
              right: 0;
              top: 8px;
              position: absolute;
            }
          }
        }
      }

      .right_footer {
        background: #F9F9F9;
        padding: 6px 0;
        margin: 20px 0px 10px 0px;

        .footerBox {
          padding: 2px 6px;
          .item_ {
            margin-right: 10px;
          }
        }
      }
    }

    .right_centre {
      .auto-img {
        font-family: AppleSystemUIFont;
        font-size: 16px;
        color: #2E2E2E;
      }
    }
  }

  .components_left {
    width: 20%;
    background-color: #f2f2f2;
    margin: 0 20px;
    // height: calc(100vh - 209px);
    background: #FFFFFF;
    border-radius: 6px;

    .components_left_top {
      background-color: #ffffff;
      padding: 10px;
      // height: calc(100vh - 291px);
      border-radius: 6px;


      .left_top_qb {
        display: flex;
        font-size: 16px;
        align-items: center;

        &::before {
          content: '';
          width: 4px;
          height: 17px;
          background: linear-gradient(180deg, rgba(84, 156, 255, 0) 0%, #549CFF 100%);
        }

        >span {
          margin: 0 5px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #2A2A2A;
          cursor: pointer; // 鼠标指针样式为手型
        }
      }

    }

    .trees {
      padding: 10px 0px;

      .ant-tree-treenode {
        // margin: 10px 0;

      }

      .ant-tree-treenode {
        padding: 10px;
      }

      // .ant-tree-switcher {
      //   line-height: 37px;
      // }

      // .ant-tree-node-selected {
      //   background: rgba(84, 156, 255, 0.1);
      //   border-radius: 6px;
      //   padding: 10px;
      // }
    }
  }
}
