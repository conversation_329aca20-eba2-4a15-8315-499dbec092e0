import HTTP from './index';

// 获取课程章节
export const reqCourseChapterApi = (data: any) => {
    return HTTP(`/learn/v1/teaching/course/get/chapter/contents`, {
        method: 'GET',
        params: data
    })
}

// 获取课程列表(班级课和公开课)
export const reqCourseListApi = (data: any) => {
    return HTTP(`/learn/v1/teaching/course/get/courses/list`, {
        method: 'GET',
        params: data
    })
}

// 获取课程列表(微课)
export const reqCourseListApi2 = (data: any) => {
    return HTTP(`/learn/v1/course/release`, {
        method: 'POST',
        data: data
    })
}

// 获取地图
export const reqMapApi = (data: any) => {
    return HTTP(`/learn/m1/knowledge/query/page`, {
        method: 'GET',
        params: data
    })
}

export const queryResourceLabel = (ids: string[]) => HTTP.post(`/learn/v1/homepage/resource/info`, ids)

// 导入第三方资源
export function importThirdResource(data: any) {
    return HTTP('/rman/v1/upload/third-resource/import', {
        method: 'post',
        data
    })
}

// 查询我的个人资源
export function GetUserFolders() {
    return HTTP(`/canvas-lms-adapter/Graph/UserFolders`, {
        method: 'get'
    })
}

//   查询我的课程资源 
export function GetUserCourses() {
    return HTTP(`/canvas-lms-adapter/Graph/UserCourses`, {
        method: 'get'
    })
}

//   查询课程的下一级
export function GetCoursesFoldersChild(id: any) {
    return HTTP.get(`/canvas-lms-adapter/Graph/CoursesFolders?course_id=${id}`)
}

//   获取文件夹下的文件
export function GetFoldersFiles(id: any) {
    return HTTP.get(`/canvas-lms-adapter/Graph/FolderFiles?folder_id=${id}`)
}

// 获取文件夹下的子文件夹
export function GetFolderFolders(id: any) {
    return HTTP.get(`/canvas-lms-adapter/Graph/FolderFolders?folder_id=${id}`)
}

// 根据id查询地图基本信息
export const querymapinfo = (params: any) =>
    HTTP(`/learn/m1/knowledge/info`, {
        method: 'GET',
        params,
    })

export function searchResAll(
    id: any,
    type: string,
    path: string,
    keyword: string,
    starttime: string,
    endtime: string,
    current: number,
    isconvert?: boolean,
    condition?: any,
) {

    let conditions: any = []
    if (type) {
        conditions.push({
            field: 'type_',
            searchRelation: 0,
            value: [type],
        })
    }
    if (isconvert) {
        conditions.push({
            field: "fileext",
            searchRelation: 11,
            value: ["PDF"]
        })
    }
    conditions = conditions.concat(condition ?? []);
    return HTTP('/rman/v1/search/all', {
        method: 'post',
        data: {
            folderId: id,
            keyword: [keyword],
            folderPath: path,
            conditions: conditions,
            sortFields: [
                {
                    field: 'createDate_',
                    isDesc: true,
                },
            ],
            pageIndex: current,
            pageSize: 9,
        }
    })
}

export function folderChildren(folderId: string) {
    // return HTTP.get(`/rman/v1/folder/children?folderId=${folderId}`)
    return HTTP.get(`/rman/v1/folder/children?folderPath=${encodeURIComponent(folderId)}%2F&isChildCount=true`)
}

//根据名称查询所有符合要求的资源知识点片段
export const getAllpoint = (params: any) => {
    return HTTP("/rman/v1/search/knowledge/point", {
        method: "POST",
        params,
    })
}

export function getTreebylevel(level: number = 2) {
    return HTTP.get(`/rman/v1/folder/all/tree?level=${level}`)
}


//查询我的收藏
export const getmycollectionlist = (data: any) => {
    return HTTP(`/rman/v1/metadata/resource/search/collection`, {
        method: 'POST',
        data: data
    })
}

//我的录播
export const getmyvideolist = (data: any) => {
    return HTTP(`/rman/v1/search/my/video`, {
        method: 'POST',
        data: data
    })
}

//查询推荐知识点
export const queryRecommendPoint = (data: any, params: any) => {
    return HTTP("/rman/v1/search/knowledge/point", {
        method: "POST",
        data,
        params
    })
}

//查询知识点推荐资源
export const queryRecommendResource = (params: any) => {
    return HTTP("/rman/v1/search/knowledge/point/recommend/resource", {
        method: "GET",
        params
    })
}


//查询分享给自己的
export const shareMyself = (data: any) => {
    return HTTP(`/rman/v1/share/search`, {
        method: 'POST',
        data
    })
}

export function searchResList(id: any, path: any, current: number, type: string[] | undefined, pageSize?: number, keyword?: string, condition?: any) {
    if (type) {
        condition = [
            ...condition,
            {
                field: 'type_',
                searchRelation: 0,
                value: type,
            },
        ];
    }
    return HTTP('/rman/v1/search/folder', {
        method: 'post',
        data: {
            folderId: id,
            folderPath: path + '/',
            // path: encodeURIComponent(id),
            keyword: [keyword],
            conditions: condition,
            sortFields: [
                {
                    field: 'createDate_',
                    isDesc: true,
                },
            ],
            pageIndex: current,
            pageSize: pageSize || 9,
            anonymous: true
        }
    })
}

//查询资源
export function resourceDetail(id: string, pathType?: number) {
    return HTTP.get(`/rman/v1/entity/base/${id}`, {
        params: {
            pathType,
        },
    })
}

export function getTree(params: { mapId: string }) {
    return HTTP.get(`/learn/m1/knowledge/course/find/nodes`, {
        params,
    });
}



// ai生成
/**
 * 流处理
 * @param url  url地址
 * @param data 请求body
 * @returns
 */
const abortController = new AbortController();
const signal = abortController.signal;
const postStream: (url: string, data?: unknown) => Promise<any> | any = (
    url,
    data
) => {
    const headers: HeadersInit = { 'Content-Type': 'application/json' }
    return fetch(url, {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
        signal,
        headers: headers
    })
}
// 通过输入文本生成题目
export function generateByBrief(data: any) {
    return postStream(`/aiclass/sse/question/generateByBrief`, data);
}

// 通过资源库视频生成题目
export function enerateByVideoId(data: any) {
    return postStream(`/aiclass/sse/question/generateByVideoId`, data);
}

// 通过文件生成题目
export function generateByFile(data: any) {
    return fetch(`/aiclass/sse/question/generateByFile`, {
        method: 'POST',
        body: data,
        signal
    });
}

// 通过知识点生成题目
export function generateByPoint(data: any) {
    return postStream(`/aiclass/sse/question/generateByPoint`, data);
}

// 通过章节生成题目
export function generateByChapter(data: any) {
    return postStream(`/aiclass/sse/question/generateByChapter`, data);
}

// 加入题库
export function examinationAdd(data: any) {
    return HTTP(`/exam-api/examination/add`, {
        data,
        method: 'POST',
    });
}