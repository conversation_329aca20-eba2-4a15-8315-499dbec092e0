import http from '@/http/http';
import deleteTypes from '@/types/deleteTypes';

namespace deleteApis {
  //资源删除检查
  // export const resourceCkeck = (folderIds: Array<string>, contentIds: Array<string>) => {
  //     return http<boolean>('/metadata/resource/delete/check', {
  //         method: 'POST',
  //         data: {
  //             folderIds,
  //             contentIds
  //         }
  //     })
  // }
  // //资源删除检查结果
  // export const ckeckResult = (taskId: string) => {
  //     return http(`/metadata/resource/delete/check/result?taskId=${taskId}`)
  // }
  //资源删除
  export const deleteResource = (data: Array<string>, isPublic?: boolean) => {
    return http<deleteTypes.IdeleteResource>('/recycle/delete', {
      method: 'POST',
      params: {
        isPublic: isPublic,
      },
      data: data,
    });
  };
  //删除回收站资源
  export const deleteResourceRecycle = (data: Array<string>) => {
    return http<deleteTypes.IdeleteResource>('/recycle/post/delete', {
      method: 'POST',
      data: data,
    });
  };
  //回收站数据还原
  export const reductionResource = (data: any) => {
    return http<deleteTypes.IdeleteResource>('/recycle/restore', {
      method: 'POST',
      data: data,
    });
  };
  //获取删除进度
  export const deleteResult = (processid: string) => {
    return http<deleteTypes.IdeleteResult>(
      `/recycle/delete/process?processid=${processid}`,{
        ifHideError: true
      }
    );
  };
  //同步删除的视频数据
  export const syncDeleteData = (data: any) => {
    return http<deleteTypes.IdeleteResult>(
      `/learn/v1/course/recording/video/delete`,{
        method: 'POST',
        data: data,
      }
    );
  };
  //还原已删除的视频数据
  export const syncReductionData = (data: any) => {
    return http<deleteTypes.IdeleteResult>(
      `/learn/v1/course/recording/video/reduction`,{
        method: 'POST',
        data: data,
      }
    );
  };
}

export default deleteApis;
