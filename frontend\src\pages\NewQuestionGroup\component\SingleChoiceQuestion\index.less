.single_container {
  width: 100%;
  .form_item {
    .form_item_header {
      position: relative;
      display: flex;
      align-items: center;
      .unfold_icon {
        position: absolute;
        width: 16px;
        height: 16px;
        left: -26px;
        cursor: pointer;
      }
      .tag {
        margin-right: 10px;
        display: inline-block;
        width: 4px;
        height: 16px;
        background: var(--primary-color);
      }
      .opt_box {
        position: absolute;
        right: 66px;
        .addBlanks {
          margin-left: 20px;
        }
      }
    }
    .form_item_body {
      padding: 20px 66px 0 66px;
      position: relative;
      .del_item {
        position: absolute;
        top: 50%;
        right: 32px;
      }
      .form_item_content {
        display: flex;
        justify-content: space-between;
      }
      .ant-row {
        display: flex;
        justify-content: space-between;
        .ant-form-item-label {
          width: 20%;
          text-align: right;
        }
        .tagItem {
          .ant-form-item-control-input {
            border: 1px solid #d9d9d9;
            .ant-form-item-control-input-content {
              display: flex;
              flex-direction: row;
              flex-wrap: wrap;
              .ant-tag {
                padding: 0px 5px;
                margin: 0 0 0 5px;
              }
              .tag-input {
                width: 80px;
                padding: 0px 5px;
                margin: 0 0 0 5px;
              }
            }
          }
        }
      }
      .answer_container {
        width: 100%;
        .answer_list {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          .ant-input {
            flex: 1;
            margin: 0 10px;
          }
          .judge {
            font-size: 14px;
          }
        }
      }
    }
    .enclosure {
      &.hidden {
        height: 0;
        overflow: hidden;
      }
      padding: 0 0 0 66px;
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      .ant-upload {
        width: 0;
        overflow: hidden;
      }
      > .label {
        white-space: nowrap;
      }
      > span {
        display: flex;
        .ant-upload-list {
          margin-left: 10px;
          display: flex;
          flex-wrap: wrap;
          flex-direction: row;
          .ant-upload-list-item {
            margin: 0 !important;
          }
        }
      }
    }
    &.attachment {
      margin-bottom: 20px;
      .form_item_header {
        .ant-checkbox-wrapper {
          margin-right: 10px;
        }
      }
      .form_item_body {
        .file-support-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          .file {
            line-height: 32px;
            margin-right: 20px;
            & > span {
              display: inline-block;
            }
          }
          .file-name {
            & > span {
              margin-right: 10px;
            }
          }
          .file-required {
            & > span {
              margin-left: 10px;
            }
          }
          .anticon-delete {
            font-size: 16px;
            color: var(--primary-color);
            // line-height: 32px;
          }

          .ant-input {
            width: 300px;
          }
        }
      }
    }
  }
}
