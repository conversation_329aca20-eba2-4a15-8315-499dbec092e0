.test_report_detail_container {
  width: 100vw;
  height: 100vh;
  display: flex;
  padding: 0 20px 20px;
  gap: 20px;
  background: #f7f9fa;

  .content_left {
    flex: 1;

    .header {
      height: 96px;
      line-height: 96px;
      text-align: center;
      font-weight: 600;
      font-size: 24px;
      color: #2a2a2a;
    }

    .content {
      display: flex;
      flex-direction: column;
      gap: 20px;
      width: 100%;
      height: calc(100% - 96px);

      .top_rank {
        width: 100%;
        height: 245px;
        background: #ffffff;
        border-radius: 10px;
        padding: 30px 40px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .rank_header {
          width: 100%;
          display: flex;
          gap: 104px;
          justify-content: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #2a2a2a;
        }

        .rank_item_list {
          display: flex;
          width: 100%;
          height: 130px;
          //   justify-content: space-between;
          gap: 40px;

          .rank_item {
            width: 100%;
            height: 100%;
            position: relative;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-size: 100% 100%;
            background-repeat: no-repeat;

            .line {
              position: absolute;
              top: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 64px;
              height: 4px;
              border-radius: 2px;
              z-index: 2;
            }

            .rank_item_title {
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              font-size: 18px;
              color: #2a2a2a;
            }

            .rank_num {
              font-family: PingFangSC, PingFang SC;
              font-weight: 500;
              font-size: 28px;
              color: #2a2a2a;
            }
          }

          .rank_one {
            background-image: url('~@/assets/testSystem/rank_one.png');

            .line {
              background: #549cff;
            }
          }

          .rank_two {
            background-image: url('~@/assets/testSystem/rank_two.png');

            .line {
              background: #fc592b;
            }

            .rank_num {
              color: #fc592b;
            }
          }

          .rank_three {
            background-image: url('~@/assets/testSystem/rank_three.png');

            .line {
              background: #53c9d2;
            }

            .rank_num {
              color: #53c9d2;
            }
          }

          .rank_four {
            background-image: url('~@/assets/testSystem/rank_four.png');

            .rank_num {
              color: #ffb730;
            }
          }
        }
      }

      .table_detail {
        flex: 1;
        width: 100%;
        height: 100%;
        background: #ffffff;
        border-radius: 10px;
        padding: 30px;
      }
    }
  }

  .content_right {
    width: 360px;
    height: 100%;

    .top_btn {
      height: 96px;
      width: 100%;
      display: flex;
      justify-content: end;
      align-items: center;
    }

    .title_num {
      width: 100%;
      height: calc(100% - 96px);
      background: #ffffff;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      row-gap: 20px;

      .analys_title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #2a2a2a;
        cursor: pointer;
      }

      .line {
        width: 100%;
        height: 1px;
        border: 1px dashed #c5c5c5;
      }

      .title_list {
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        row-gap: 15px;

        // row-gap: 30px;
        .title_part {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;

          .title_item {
            width: 36px;
            height: 36px;
            background: #ffffff;
            border-radius: 6px;
            border: 1px solid #c5c5c5;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #525252;
          }

          .correct_items {
            background: rgba(23, 193, 100, 0.1);
            border: 1px solid #17c164;
          }

          .wrong_items {
            background: rgba(255, 97, 97, 0.11);
            border: 1px solid #ff6161;
          }
        }
      }
    }
  }
}
