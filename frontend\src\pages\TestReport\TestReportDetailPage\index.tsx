import examManageApis from '@/api/exam';
import RenderHtml from '@/components/renderHtml';
import { convertToChinaNum } from '@/utils';
import { Checkbox, message, Radio } from 'antd';
import { FC, useEffect, useState } from 'react';
import { useLocation } from 'umi';
import './index.less';

const TestReportDetailPage: FC = () => {
  const location: any = useLocation<any>();
  //   选中的题目的id (使用格式化后的reportId)
  const [selectReportId, setSelectReportId] = useState<string>('');
  //   当前展示的题目信息
  const [showQuestionInfo, setShowQuestionInfo] = useState<any>({});
  // 当前的答案
  const [curAnswer, setCurAnswer] = useState<any[]>([]);
  //   初始化答案
  const handleInitAnswer = (value: any) => {
    if (typeof value === 'string') {
      setCurAnswer(value?.split(',') || []);
    } else {
      setCurAnswer(value || []);
    }
  };
  //   题目顺序格式化
  const getFormatOrderQuestion = (data: any[]) => {
    if (data?.length > 0) {
      let index = 1;
      return data.map((item: any) => {
        const res: any[] = [];
        item?.questions?.forEach((cell: any) => {
          if (cell?.question?.questions_type !== 5) {
            res.push({
              ...cell,
              isGroup: false,
              // 手动添加的id,扁平化题组后的标识
              reportId: cell.question.id,
              itemIndex: index++,
            });
            return;
          }
          const groupArr = cell?.question?.groupQuestions.map(
            (groupItem: any) => {
              return {
                ...groupItem,
                answerQuestion: cell.answerQuestion,
                isGroup: true,
                // 手动添加的id,扁平化题组后的标识
                reportId: cell.question.id + '_' + groupItem.id,
                itemIndex: index++,
                question: cell.question,
              };
            },
          );
          cell.question.groupQuestions = groupArr;
          res.push(...groupArr);
        });

        return {
          ...item,
          flatArr: res,
        };
      });
    }
    return [];
  };
  const [reportDetail, setReportDetail] = useState<any>([]);
  const [questionOrderList, setQuestionOrderList] = useState<any[]>([]);
  const getDetail = (id: string) => {
    examManageApis.fetchExamReportDetail(id).then((res: any) => {
      if (res?.status == 200) {
        setReportDetail(res?.data);
        const formatArr = getFormatOrderQuestion(res?.data || []);
        setQuestionOrderList(formatArr);
        console.log(formatArr, 'format');
        const flatList = formatArr?.reduce((prev: any[], cur: any) => {
          return prev.concat(cur?.flatArr);
        }, []);
        if (location?.query?.reportId) {
          setSelectReportId(location?.query?.reportId);
          const findItem: any = flatList.find(
            (item: any) => item?.reportId === location?.query?.reportId,
          );
          setShowQuestionInfo(findItem);
          //   setCurAnswer()
          handleInitAnswer(findItem?.answerQuestion?.[0]?.answer);
          return;
        }
        setSelectReportId(formatArr?.[0]?.flatArr?.[0]?.reportId);
        setShowQuestionInfo(flatList?.[0]);
        handleInitAnswer(flatList?.[0]?.answerQuestion?.[0]?.answer);
      }
    });
  };
  useEffect(() => {
    if (location?.query?.id) {
      getDetail(location.query?.id);
    }
  }, [location?.query?.id]);
  //   点击事件
  const handleSelectItem = (item: any) => {
    if (item?.reportId === selectReportId) {
      return;
    }
    setShowQuestionInfo(item);

    setSelectReportId(item?.reportId);
    if (item?.isGroup) {
      const answer = item?.answerQuestion?.find(
        (ans: any) => ans?.questionId === item?.id,
      );
      setCurAnswer(answer?.answer || []);
      return;
    }
    handleInitAnswer(item?.answerQuestion?.[0]?.answer);
  };

  // 收藏试题
  const handleCollectItem = () => {
    examManageApis
      .fetchCollectQuestion({
        id: showQuestionInfo?.question?.id,
        classifyId: location?.query?.classifyId,
      })
      .then((res: any) => {
        if (res?.status == 200) {
          message.success('收藏成功');
        }
      });
  };

  // 单选题
  const SingleChoice = (
    itemList: any[],
    itemValue: any,
    analysisContent: string,
    correctAns: any[],
  ) => (
    <>
      <Radio.Group value={itemValue}>
        {itemList?.map((item_0: any, index_0: number) => {
          return (
            <div
              className="answer-item"
              key={`single_${item_0.seq}_${item_0.uid}`}
            >
              <Radio value={String.fromCharCode(64 + Number(item_0.seq))}>
                {String.fromCharCode(64 + Number(item_0.seq))}
              </Radio>
              <RenderHtml
                cname="radio-content"
                value={item_0.content}
              // onClick={(e: any) => perviewimg(e)}
              ></RenderHtml>
            </div>
          );
        })}
      </Radio.Group>
      <div className="analysis_content">
        <div className="top_answer">
          <div className="analysis_answer">
            正确答案：
            <span className="answer_text">{correctAns?.join(',')}</span>
          </div>
          <div className="analysis_answer">
            你的答案：<span className="answer_text">{itemValue}</span>
          </div>
        </div>
        <div className="analysis_title">解析：</div>
        <RenderHtml cname="radio-content" value={analysisContent}></RenderHtml>
      </div>
    </>
  );

  // 多选题
  const MultipleChoice = (
    itemList: any[],
    itemValue: any,
    analysisContent: string,
    correctAns: any[],
  ) => (
    <>
      <Checkbox.Group value={itemValue}>
        {itemList?.map((item_1: any, index_1: number) => {
          return (
            <div
              className="answer-item"
              key={`mul_${item_1.seq}_${item_1.uid}`}
            >
              <Checkbox value={String.fromCharCode(64 + Number(item_1.seq))}>
                {String.fromCharCode(64 + Number(item_1.seq))}
              </Checkbox>
              <RenderHtml
                cname="radio-content"
                value={item_1.content}
              // dangerouslySetInnerHTML={{ __html: item_1.content }}
              ></RenderHtml>
            </div>
          );
        })}
      </Checkbox.Group>
      <div className="analysis_content">
        <div className="top_answer">
          <div className="analysis_answer">
            正确答案：
            <span className="answer_text">{correctAns?.join(',')}</span>
          </div>
          <div className="analysis_answer">
            你的答案：
            <span className="answer_text">{itemValue?.join(',')}</span>
          </div>
        </div>
        <div className="analysis_title">解析：</div>
        <RenderHtml cname="radio-content" value={analysisContent}></RenderHtml>
      </div>
    </>
  );

  // 填空题
  const FillInTheBlank = (
    itemList: any[],
    itemValue: any,
    analysisContent: string,
    correctAns: any[],
  ) => (
    <>
      <div>
        {itemValue?.map((item_2: any, index_2: number) => {
          return (
            <div className="answer-item blanks" key={`${index_2}`}>
              <span style={{ width: 60 }}>第{index_2 + 1}空</span>
              <RenderHtml
                cname="auto-img"
                // onClick={(e: any) => perviewimg(e)}
                value={item_2}
              ></RenderHtml>
            </div>
          );
        })}
      </div>
      <div className="analysis_content">
        <div className="top_answer" style={{ flexDirection: 'column' }}>
          <div className="analysis_answer">
            正确答案：
            <span className="answer_text">
              {correctAns?.map((value, index) => (
                <div style={{ display: 'flex' }} key={String(index)}>
                  <div>第{index + 1}空：</div>
                  <RenderHtml
                    cname="auto-img"
                    style={{ with: 'auto' }}
                    // onClick={(e: any) => perviewimg(e)}
                    value={value}
                  ></RenderHtml>
                </div>
              ))}
            </span>
          </div>
          <div className="analysis_answer">
            你的答案：
            <span className="answer_text">
              {itemValue?.map((item_2: any, index_2: number) => {
                return (
                  <div key={`blank_${index_2}`} style={{ display: 'flex' }}>
                    <span>第{index_2 + 1}空：</span>
                    <RenderHtml
                      cname="auto-img"
                      // onClick={(e: any) => perviewimg(e)}
                      value={item_2}
                    ></RenderHtml>
                  </div>
                );
              })}
            </span>
          </div>
        </div>
        <div className="analysis_title">解析：</div>
        <RenderHtml cname="radio-content" value={analysisContent}></RenderHtml>
      </div>
    </>
  );

  // 主观题
  const SubjectiveQuestion = (
    itemId: string,
    itemValue: any,
    analysisContent: string,
    correctAns: any[],
  ) => (
    <div key={itemId}>
      <div className="answer-item">
        <RenderHtml
          cname="auto-img"
          // onClick={(e: any) => perviewimg(e)}
          value={itemValue}
        ></RenderHtml>
      </div>
      <div className="analysis_content">
        <div className="top_answer">
          {/* <div className="analysis_answer">
            正确答案：<span className="answer_text">{ curAnswer }</span>
          </div>
          <div className="analysis_answer">
            你的答案：<span className="answer_text">{itemValue}</span>
          </div> */}
        </div>
        <div className="analysis_title">解析：</div>
        <RenderHtml cname="radio-content" value={analysisContent}></RenderHtml>
      </div>
    </div>
  );

  // 判断题
  const JudgementQuestion = (
    itemList: any[],
    itemValue: any,
    analysisContent: string,
    correctAns: any[],
  ) => (
    <>
      <Radio.Group value={itemValue}>
        {itemList?.map((item_4: any, index_4: number) => {
          return (
            <div
              className="answer-item"
              key={`jud_${item_4.seq}_${item_4.uid}`}
            >
              <Radio value={String.fromCharCode(64 + Number(item_4.seq))}>
                {String.fromCharCode(64 + Number(item_4.seq))}
              </Radio>
              <div className="radio-content">{item_4.content}</div>
            </div>
          );
        })}
      </Radio.Group>
      <div className="analysis_content">
        <div className="top_answer">
          <div className="analysis_answer">
            正确答案：
            <span className="answer_text">{correctAns?.join(',')}</span>
          </div>
          <div className="analysis_answer">
            你的答案：<span className="answer_text">{itemValue}</span>
          </div>
        </div>
        <div className="analysis_title">解析：</div>
        <RenderHtml cname="radio-content" value={analysisContent}></RenderHtml>
      </div>
    </>
  );

  // 题组
  const QuestionGroup = (itemList: any[], answerList: any) => {
    return (
      <div className="question-group">
        {itemList.map((item: any, index: number) => {
          // 获取题组的答案
          const answerItem = answerList?.find(
            (cell: any) => cell.id === item.id,
          );
          return (
            <div key={item.id}>
              <div className="group_content_container">
                <div className="group_item_order">{item.itemIndex}.</div>
                <RenderHtml
                  cname="auto-img"
                  value={item?.questions_content}
                // onClick={(e: any) => perviewimg(e)}
                ></RenderHtml>
              </div>
              <div>
                {item?.questions_type === 0 &&
                  SingleChoice(
                    item.questions_options,
                    answerList?.[index]?.answer || '',
                    item?.questions_analysis,
                    item?.questions_answers,
                  )}
                {item?.questions_type === 1 &&
                  MultipleChoice(
                    item.questions_options,
                    answerList?.[index]?.answer || '',
                    item?.questions_analysis,
                    item?.questions_answers,
                  )}
                {item?.questions_type === 2 &&
                  FillInTheBlank(
                    item.questions_options,
                    answerList?.[index]?.answer || '',
                    item?.questions_analysis,
                    item?.questions_answers,
                  )}
                {item?.questions_type === 3 &&
                  SubjectiveQuestion(
                    item.id,
                    answerList?.[index]?.answer || '',
                    item?.questions_analysis,
                    item?.questions_answers,
                  )}
                {item?.questions_type === 4 &&
                  JudgementQuestion(
                    item.questions_options,
                    answerList?.[index]?.answer || '',
                    item?.questions_analysis,
                    item?.questions_answers,
                    // item.id,
                  )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="report_detail_page_container">
      <div className="header">
        <div className="header_name">{location?.query?.name}</div>
        <div className="collect_btn">
          <div className="btn" onClick={handleCollectItem}>
            收藏试题
          </div>
        </div>
      </div>
      <div className="content_container">
        <div className="question_detail">
          <div className="question_content">
            <div className="test_title_desc">
              {/* <div>{convertToChinaNum(questionContent?.parentIndex + 1)}、</div> */}
              <RenderHtml
                cname="auto-img"
                value={showQuestionInfo?.question?.questions_content}
              // onClick={(e: any) => perviewimg(e)}
              ></RenderHtml>
            </div>
            <div className="answer_content">
              {/* 单选题 */}
              {showQuestionInfo?.question?.questions_type === 0 &&
                SingleChoice(
                  showQuestionInfo?.question.questions_options,
                  curAnswer[0],
                  showQuestionInfo?.question?.questions_analysis,
                  showQuestionInfo?.question?.questions_answers,
                )}
              {/* 多选题 */}
              {showQuestionInfo?.question?.questions_type === 1 &&
                MultipleChoice(
                  showQuestionInfo?.question.questions_options,
                  curAnswer,
                  showQuestionInfo?.question?.questions_analysis,
                  showQuestionInfo?.question?.questions_answers,
                )}

              {/* 填空题 */}
              {showQuestionInfo?.question?.questions_type === 2 &&
                FillInTheBlank(
                  showQuestionInfo?.question.questions_options,
                  curAnswer,
                  showQuestionInfo?.question?.questions_analysis,
                  showQuestionInfo?.question?.questions_answers,
                )}

              {/* 主观题 */}
              {showQuestionInfo?.question?.questions_type === 3 &&
                SubjectiveQuestion(
                  showQuestionInfo?.question.id,
                  curAnswer,
                  showQuestionInfo?.question?.questions_analysis,
                  showQuestionInfo?.question?.questions_answers,
                )}

              {/* 判断题 */}
              {showQuestionInfo?.question?.questions_type === 4 &&
                JudgementQuestion(
                  showQuestionInfo?.question.questions_options,
                  curAnswer[0],
                  showQuestionInfo?.question?.questions_analysis,
                  showQuestionInfo?.question?.questions_answers,
                )}

              {/* 题组 */}
              {showQuestionInfo?.question?.questions_type === 5 &&
                QuestionGroup(
                  showQuestionInfo?.question.groupQuestions,
                  showQuestionInfo?.answerQuestion,
                )}
            </div>
          </div>
        </div>
        <div className="question_order">
          {questionOrderList.map((item: any, index: number) => {
            return (
              <div className="part_container" key={item.id + '_' + index}>
                <div className="part_title">
                  第{convertToChinaNum(index + 1)}部分：{item.name}
                </div>
                <div className="line" />
                <div className="order_container">
                  {item.flatArr?.map((cell: any) => {
                    if (cell?.isGroup) {
                      const isCorrect = cell?.answerQuestion?.[0]?.isCorrect || false; // 强制转换为布尔类型

                      return (
                        <div
                          // className={`order_item ${cell?.reportId === selectReportId
                          //     ? 'checked_item'
                          //     : ''
                          //   }`}
                          className={`order_item ${isCorrect ? 'correct_item' : 'wrong_item'
                            } ${cell?.reportId === selectReportId
                              ? 'checked_item'
                              : ''
                            }`}
                          key={cell?.reportId}
                          onClick={() => handleSelectItem(cell)}
                        >
                          {cell.itemIndex}
                        </div>
                      );
                    }
                    const isCorrect = cell?.answerQuestion?.[0]?.isCorrect || false; // 强制转换为布尔类型
                    return (
                      <div
                        className={`order_item ${isCorrect ? 'correct_item' : 'wrong_item'
                          } ${cell?.reportId === selectReportId
                            ? 'checked_item'
                            : ''
                          }`}
                        key={cell?.reportId}
                        onClick={() => handleSelectItem(cell)}
                      >
                        {cell.itemIndex}
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}

          {/* <div className="part_container">
            <div className="part_title">第一部分：XXXX</div>
            <div className="line" />
            <div className="order_container">
              <div className="order_item correct_item">1</div>
              <div className="order_item wrong_item">1</div>
              <div className="order_item checked_item">1</div>
              <div className="order_item">1</div>
              <div className="order_item">1</div>
              <div className="order_item">1</div>
              <div className="order_item">1</div>
              <div className="order_item">1</div>
              <div className="order_item">1</div>
              <div className="order_item">1</div>
            </div>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default TestReportDetailPage;
