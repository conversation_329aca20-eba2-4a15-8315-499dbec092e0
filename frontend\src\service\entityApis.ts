import http from '../http/http';
import { IEntityRes, IEntityMetadata, IFormItem } from '@/types/entityTypes';

namespace entityApis {
  export const getEntity = (contentId: string) => {
    return http<IEntityRes>(`/entity/base/${contentId}`, {
      method: 'GET',
    });
  };

  export const getEntityMetadata = (contentId: string) => {
    return http<IFormItem[]>(`/entity/entitydata/${contentId}`, {
      method: 'GET',
    });
  };

  export const setEntityMetadata = (
    contentId: string,
    data: { oldValue: IFormItem[]; newValue: IFormItem[] },
    isPublic?: boolean,
  ) => {
    return http<boolean>(`/entity/entitydata/${contentId}`, {
      method: 'PATCH',
      params: {
        isPublic: isPublic,
      },
      data,
    });
  };

  export const getAllFieldsByType = (type: string) => {
    return http<IFormItem[]>(
      `/metadata/config/fields/upload?EntityType=${type}&Type=basic&ResourceType=model_sobey_object_entity`,
    );
  };

  export const getExif = (contentId: string) => {
    return http<IFormItem[]>(`/entity/picture/${contentId}/exif`);
  };
}

export default entityApis;
