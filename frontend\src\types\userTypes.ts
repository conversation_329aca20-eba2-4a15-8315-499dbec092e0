export interface ILoginReq{
    loginName: string
    password: string
}

export interface IUserOwnerPrivilegeGroup {
    canDelete: boolean
    canExecute: boolean
    canRead: boolean
    canWrite: boolean
    groupCode: string
    groupName: string
    level: number
}
export interface IUserOrganization {
    id: number
    operate: number
    organizationCode: string
    organizationName: string
    parentId: number
    siteCode: string
    siteName: string
}
export interface IUserRole {
    count: number
    disabled: number
    id: number
    privilegeAdmin: number
    roleCode: string
    roleName: string
    roleType: number
    siteCode: string
    siteName: string
}
export interface IUserAdditional {
    userCode: string
    score: number
    exp: number
    unreadMessage: number
}
export interface IUserDohaExtend {
    userRootTreeId: string
    code: string
}
export interface IUser {
    isBuiltInAdmin: boolean
    ownerPrivilegeGroup: IUserOwnerPrivilegeGroup
    organizations: IUserOrganization[]
    roles: IUserRole[]
    type: number
    disabled: number
    storageSize: number
    storageUsage: number
    email: string
    phone: string
    additional: IUserAdditional
    organizationCode: string
    sessionId: string
    id: number
    userCode: string
    loginName: string
    nickName: string
    avatarUrl: string
    isAdmin: boolean
    isSystemAdmin: boolean
    siteCode: string
    userToken: string
    appPermission: string[]
    ownerTemplateCode: string
    groups: any[]
    permissions: any[]
    dohaExtend: IUserDohaExtend
}

export interface IBaseUserInfo {
    userCode: string
    loginName: string
    nickName: string
}