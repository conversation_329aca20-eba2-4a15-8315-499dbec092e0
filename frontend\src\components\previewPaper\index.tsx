import examType from '@/types/examType';
import { noSupport } from '@/utils';
import { Button, Checkbox, Drawer, Empty, Radio } from 'antd';
import React, { useEffect } from 'react';
import { IConfig, useSelector } from 'umi';
import RenderHtml from '../renderHtml';
import './index.less';
interface previewprops {
  detail: any;
  editObject?: any;
  title?: string;
  visible: boolean;
  onClose: () => void;
  handlecopy?: () => void;
  handledit?: () => void;
}

const PreviewPaper: React.FC<previewprops> = (props) => {
  const { detail, title, visible, onClose, handlecopy, handledit, editObject } =
    props;
  const close = () => {
    onClose();
  };
  const copy = () => {
    if (configs.mobileFlag) {
      noSupport();
      return;
    }
    handlecopy && handlecopy();
  };
  const configs: IConfig = useSelector<{ config: any }, IConfig>(
    ({ config }) => config,
  );
  useEffect(() => {
    console.log(111111111111111111111111111111, detail);
    //这里处理数据是因为模板导入的选项是字幕 ABCD
    //  detail?.questions_details?.forEach((item:any) => {
    //  detail?.forEach((item:any) => {
    //   item?.questions_options.map((item_:any,index:number)=>{
    //     item_.seq = index+1;
    //     item_.content = item_.content;
    //   });
    //  });
  }, [detail]);
  const edit = () => {
    handledit && handledit();
  };
  return (
    <Drawer
      width={configs.mobileFlag ? '100%' : 580}
      className="paperPreview"
      extra={
        <>
          {editObject?.editable && (
            <Button type="primary" onClick={edit}>
              编辑
            </Button>
          )}
          {handlecopy && (
            <Button type="primary" onClick={copy}>
              复制
            </Button>
          )}
        </>
      }
      title={title ? title : '题目预览'}
      placement="right"
      onClose={close}
      visible={visible}
    >
      {detail?.questions_details?.length > 0 ? (
        detail.questions_details.map((item: any, index: number) => {
          return (
            <div className="content_row" key={index}>
              <div className="content">
                {/* <span>{`${index+1}.`}</span> */}
                <div className="type">
                  (
                  <span>{`*${examType.optionType_[item.questions_type]}题 ${
                    item.question_mark ?? 0
                  }分`}</span>
                  )
                </div>
                <RenderHtml
                  cname="spcialDom"
                  value={item.questions_content}
                ></RenderHtml>
              </div>
              {item.fileList?.length > 0 && (
                <div className="fileList_">
                  <span>题目附件：</span>
                  <div>
                    {item.fileList.map((item: any, index: number) => {
                      return (
                        <a
                          href={item.attachmentSource}
                          key={index}
                          target={item.attachmentSource}
                          title={item.attachmentName || ''}
                        >
                          {item.attachmentName || ''}
                        </a>
                      );
                    })}
                  </div>
                </div>
              )}
              {item.hasAttachment?.length > 0 && (
                <div className="fileList_upload">
                  <span>上传附件：</span>
                  <div>
                    {item.hasAttachment.map((item: any, index: number) => {
                      return (
                        <div>
                          <span>{item.name}</span>
                          <span>{item.required && `(必传*)`}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
              <div className="answers">
                {item.questions_type === 0 ? ( //单选
                  <Radio.Group value={item.questions_answers?.[0] || undefined}>
                    {item.questions_options?.map(
                      (item_0: any, index_0: number) => {
                        return (
                          <div className="answer_item" key={index_0}>
                            <Radio
                              value={String.fromCharCode(
                                64 + Number(index_0 + 1),
                              )}
                            >
                              {String.fromCharCode(64 + Number(index_0 + 1))}
                            </Radio>
                            <RenderHtml
                              cname="radio_content spcialDom"
                              value={item_0.content}
                            ></RenderHtml>
                          </div>
                        );
                      },
                    )}
                  </Radio.Group>
                ) : item.questions_type === 1 ? ( //多选
                  <Checkbox.Group value={item.questions_answers}>
                    {item.questions_options.map(
                      (item_1: any, index_1: number) => {
                        return (
                          <div className="answer_item" key={index_1}>
                            <Checkbox
                              value={String.fromCharCode(
                                64 + Number(index_1 + 1),
                              )}
                            >
                              {String.fromCharCode(64 + Number(index_1 + 1))}
                            </Checkbox>
                            <RenderHtml
                              cname="spcialDom"
                              value={item_1.content}
                            ></RenderHtml>
                          </div>
                        );
                      },
                    )}
                  </Checkbox.Group>
                ) : item.questions_type === 2 ? ( // 填空题
                  item.questions_options.map((item_2: any, index_2: number) => {
                    const blankAnswer =
                      item_2?.answerRange === 2
                        ? `${item_2.answerMin}~${item_2.answerMax}`
                        : item_2.content;

                    return (
                      <div className="answer_item blanks" key={index_2}>
                        <span>{`第${index_2 + 1}空：`}</span>
                        {/* <span>{`${item_2.content}`}</span> */}
                        <RenderHtml
                          cname="spcialDom"
                          value={blankAnswer}
                        ></RenderHtml>
                      </div>
                    );
                  })
                ) : item.questions_type === 3 ? ( // 主观题
                  <div className="answer_item" key={index}>
                    <span>解析：</span>
                    <RenderHtml
                      cname="spcialDom"
                      value={item.questions_analysis}
                    ></RenderHtml>
                  </div>
                ) : (
                  // 判断题
                  <Radio.Group value={item.questions_answers?.[0] || undefined}>
                    {item.questions_options?.map(
                      (item_4: any, index_4: number) => {
                        return (
                          <div className="answer_item" key={index_4}>
                            <Radio
                              value={String.fromCharCode(
                                64 + Number(index_4 + 1),
                              )}
                            >
                              {String.fromCharCode(64 + Number(index_4 + 1))}
                            </Radio>
                            <div className="radio_content">
                              {item_4.content}
                            </div>
                          </div>
                        );
                      },
                    )}
                  </Radio.Group>
                )}
              </div>
            </div>
          );
        })
      ) : (
        <Empty />
      )}
    </Drawer>
  );
};

export default PreviewPaper;
