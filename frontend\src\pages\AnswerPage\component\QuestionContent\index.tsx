import { Editor } from '@/components';
import RenderHtml from '@/components/renderHtml';
import { convertToChinaNum } from '@/utils';
import { Button, Checkbox, Input, message, Radio } from 'antd';
import {
  FC,
  forwardRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { PageInfoContext } from '../../context';
import useCountdown from '../../hook/useCountDown';
import './index.less';

const QuestionContent: FC<any> = forwardRef(
  (
    {
      partTime,
      // readTime,
      partTimesLeft,
      // answer,
      onAnswerChange,
      // questionContent,
      onNextQuestion,
      onPreQuestion,
      onCollectQuestion,
      onReadTimeEnd,
      handleChangeNextStatus,
      itemReadTime,
      itemAnswerTime,
      onNextPart,
    },
    ref,
  ) => {
    const {
      questionList,
      answer,
      curAnsweringItem: questionContent,
      curQuestionInfo,
    } = useContext(PageInfoContext);
    // 题目详情
    const questionDetail = questionContent?.question;
    // 读题已结束
    const [isReadEnd, setIsReadEnd] = useState(false);

    useImperativeHandle(ref, () => ({
      // leftPartTime: leftPartTimes,
      leftAnswerTime: leftAnswerTimes,
    }));

    useEffect(() => {
      // 设置小题得status 1 未答未读; 2 已答; 3 未答已读
      // 本题已经回答过，那么不用读题
      if (questionContent?.status === 1) {
        setIsReadEnd(false);
      } else {
        setIsReadEnd(true);
      }
      // 倒计时重置得条件 1、当前部分设置了读题时间 2、当前题目没有阅读过
    }, [questionContent?.mainId]);

    const [__, readCountDown] = useCountdown({
      leftTime: (itemReadTime?.time || 0) * 1000,
      id: itemReadTime.id,
      onEnd: () => {
        console.log('读题结束，开始答题');
        setIsReadEnd(true);
        onReadTimeEnd();
      },
    });

    // 读题结束后，开始答题的倒计时
    const answerTime = useMemo(() => {
      if (itemAnswerTime?.isSetting && isReadEnd) {
        return itemAnswerTime?.time as number;
      }
      return 0;
    }, [itemAnswerTime.id, isReadEnd]);
    const [leftAnswerTimes, answerCountDown] = useCountdown({
      leftTime: answerTime * 1000,
      id: itemAnswerTime.id,
      onEnd: () => {
        console.log('答题结束,切换下一题');
        handleChangeNextStatus();
      },
    });

    // 答案
    const valueArrRef = useRef<any>(answer);
    //获取富文本的值
    const getResultFromEditor = (events: any) => {
      if (
        events.level?.content === '<p><br></p>' ||
        events.level?.content === '<p><br data-mce-bogus="1"></p>'
      ) {
        return '';
      }
      return events.level?.content;
    };

    // 答案变化
    const onChangeValue = (prop: {
      e: any;
      type: number;
      index?: number;
      isBlankEditor?: boolean;
      itemId?: string;
      blankLen?: number;
    }) => {
      if (!isReadEnd) {
        message.warn('读题时间未结束，不能作答');
        return;
      }
      const { e, type, index, isBlankEditor, itemId, blankLen } = prop;
      const itemIdStr = itemId ? String(itemId) : undefined;
      if (type === 0 || type === 4) {
        onAnswerChange([e.target.value], itemIdStr);
      } else if (type === 1) {
        onAnswerChange(e, itemIdStr);
      } else if (type === 2) {
        let curResult = '';
        if (isBlankEditor) {
          curResult = getResultFromEditor(e);
        } else {
          curResult = e.target.value;
        }
        //添加为editor后，onchange函数会导致value的值不正确，可能是闭包

        if (itemIdStr) {
          const itemIndex = valueArrRef.current?.findIndex(
            (item: any) => item.questionId === itemIdStr,
          );
          let arr: any[] = [];
          if (itemIndex >= 0) {
            // 题组存的是一个对象数组，alueArrRef.current
            // 填空题的答案是一个字符串数组 ['','','']
            // 如果是填空题的话，需要取出之前存储的答案，按照下标进行修改，然后再传出去
            arr = [...valueArrRef.current];
            const fillItem = arr[itemIndex].answer;
            fillItem[index || 0] = curResult;
            arr = [...fillItem];
          } else {
            // 先初始化整个填空题的答案数组
            arr = new Array(blankLen || 0).fill('');
            arr[index || 0] = curResult;
          }
          onAnswerChange(arr, itemIdStr);
          return;
        }

        const arr = [...valueArrRef.current];
        arr[index || 0] = curResult;
        onAnswerChange(arr, itemIdStr);
      } else if (type === 3) {
        const type3Result = getResultFromEditor(e);
        onAnswerChange([type3Result], itemIdStr);
      }
    };

    useEffect(() => {
      valueArrRef.current = answer;
    }, [answer]);

    // 单选题
    const SingleChoice = (
      itemList: any[],
      itemValue: any,
      questionId?: string,
    ) => (
      <Radio.Group
        value={itemValue}
        onChange={(value) =>
          onChangeValue({ e: value, type: 0, itemId: questionId })
        }
        style={{ display: 'flex', alignItems: 'flex-start', gap: 40 }}
      >
        {itemList?.map((item_0: any, index_0: number) => {
          return (
            <div
              className="answer-item"
              key={`single_${item_0.seq}_${item_0.uid}`}
            >
              <Radio value={String.fromCharCode(64 + Number(item_0.seq))}>
                {String.fromCharCode(64 + Number(item_0.seq))}
              </Radio>
              <RenderHtml
                cname="radio-content"
                value={item_0.content}
                // onClick={(e: any) => perviewimg(e)}
              ></RenderHtml>
            </div>
          );
        })}
      </Radio.Group>
    );

    // 多选题
    const MultipleChoice = (
      itemList: any[],
      itemValue: any,
      itemId?: string,
    ) => (
      <Checkbox.Group
        value={itemValue}
        onChange={(value) => onChangeValue({ e: value, type: 1, itemId })}
        style={{
          display: 'flex',
          alignItems: 'center',
          columnGap: 40,
          flexWrap: 'wrap',
        }}
      >
        {itemList?.map((item_1: any, index_1: number) => {
          return (
            <div
              className="answer-item"
              key={`mul_${item_1.seq}_${item_1.uid}`}
            >
              <Checkbox value={String.fromCharCode(64 + Number(item_1.seq))}>
                {String.fromCharCode(64 + Number(item_1.seq))}
              </Checkbox>
              <RenderHtml
                cname="radio-content"
                value={item_1.content}
                // dangerouslySetInnerHTML={{ __html: item_1.content }}
              ></RenderHtml>
            </div>
          );
        })}
      </Checkbox.Group>
    );

    // 填空题
    const FillInTheBlank = (
      itemList: any[],
      itemValue: any,
      itemId?: string,
      blankLen?: number,
    ) =>
      itemList?.map((item_2: any, index_2: number) => {
        return (
          <div
            className="answer-item blanks"
            key={`blank_${item_2.seq}_${item_2.uid}`}
          >
            <span style={{ width: 60 }}>第{item_2.seq}空</span>
            {Number(item_2?.answerType) === 1 ? (
              <div style={{ width: 'calc(100% - 60px)' }}>
                <Editor
                  name={`answer_${item_2.uid}_${index_2}`}
                  height={160}
                  disabled={!isReadEnd}
                  value={itemValue[index_2]}
                  onChange={(e) =>
                    onChangeValue({
                      e,
                      type: 2,
                      index: index_2,
                      isBlankEditor: true,
                      itemId,
                      blankLen,
                    })
                  }
                  disabledImage
                  // hasAttachment={data.hasAttachment}
                />
              </div>
            ) : (
              <div style={{ marginBottom: 15 }}>
                <Input
                  value={itemValue?.[index_2]}
                  disabled={!isReadEnd}
                  onChange={(e) =>
                    onChangeValue({
                      e,
                      type: 2,
                      index: index_2,
                      itemId,
                      // isBlankEditor: true,
                      blankLen,
                    })
                  }
                />
              </div>
            )}
          </div>
        );
      });

    // 主观题
    const SubjectiveQuestion = (
      itemId: string,
      itemValue: any,
      isGroup?: boolean,
    ) => (
      <div key={itemId}>
        <div className="answer-item">
          <span style={{ width: 45 }}>解答：</span>
          {
            <Editor
              name={itemId}
              height={300}
              value={itemValue}
              disabled={!isReadEnd}
              onChange={(e) => {
                if (!isReadEnd) {
                  return;
                }
                onChangeValue({
                  e,
                  type: 3,
                  itemId: isGroup ? itemId : undefined,
                });
              }}
              // hasAttachment={data.hasAttachment}
            />
          }
        </div>
      </div>
    );

    // 判断题
    const JudgementQuestion = (
      itemList: any[],
      itemValue: any,
      itemId?: string,
    ) => (
      <Radio.Group
        value={itemValue}
        onChange={(e) => onChangeValue({ e, type: 4, itemId })}
      >
        {itemList?.map((item_4: any, index_4: number) => {
          return (
            <div
              className="answer-item"
              key={`jud_${item_4.seq}_${item_4.uid}`}
            >
              <Radio value={String.fromCharCode(64 + Number(item_4.seq))}>
                {String.fromCharCode(64 + Number(item_4.seq))}
              </Radio>
              <div className="radio-content">{item_4.content}</div>
            </div>
          );
        })}
      </Radio.Group>
    );

    // 题组
    const QuestionGroup = (itemList: any[], answerList: any) => {
      return (
        <div className="question-group">
          {itemList.map((item: any, index: number) => {
            // 获取题组的答案
            const answerItem = answerList?.find(
              (cell: any) => cell.questionId == item.id,
            );
            return (
              <div key={item.id}>
                <div className="group_content_container">
                  <div className="group_item_order">{item.itemIndex + 1}.</div>
                  <RenderHtml
                    cname="auto-img"
                    value={item?.questions_content}
                    // onClick={(e: any) => perviewimg(e)}
                  ></RenderHtml>
                </div>
                <div>
                  {item?.questions_type === 0 &&
                    SingleChoice(
                      item.questions_options,
                      answerItem?.answer?.[0] || '',
                      item.id,
                    )}
                  {item?.questions_type === 1 &&
                    MultipleChoice(
                      item.questions_options,
                      answerItem?.answer || [],
                      item.id,
                    )}
                  {item?.questions_type === 2 &&
                    FillInTheBlank(
                      item.questions_options,
                      answerItem?.answer || [],
                      item.id,
                      item.questions_options?.length || 0,
                    )}
                  {item?.questions_type === 3 &&
                    SubjectiveQuestion(item.id, answerItem?.answer || [], true)}
                  {item?.questions_type === 4 &&
                    JudgementQuestion(
                      item.questions_options,
                      answerItem?.answer?.[0] || [],
                      item.id,
                    )}
                </div>
              </div>
            );
          })}
        </div>
      );
    };

    return (
      <div className="exam_content_container">
        <div className="content_header">
          {/* ?.hasReadTime */}
          {questionContent ? (
            <div className="part_text">
              本部分剩余时间：
              <div className="part_time">{partTimesLeft.minutes}</div>:
              <div className="part_time">{partTimesLeft.seconds}</div>
            </div>
          ) : (
            <div />
          )}
          <div className="header_btns">
            {/* <div className="btn_item default">收藏题目</div> */}
            <Button
              style={{
                color: 'var(--primary-color)',
                border: '1px solid var(--primary-color)',
              }}
              onClick={onCollectQuestion}
            >
              收藏题目
            </Button>
            <Button
              disabled={curQuestionInfo?.isFirst || !isReadEnd}
              style={
                curQuestionInfo?.isFirst
                  ? {}
                  : {
                      color: 'var(--primary-color)',
                      border: '1px solid var(--primary-color)',
                    }
              }
              onClick={onPreQuestion}
            >
              上一题
            </Button>
            <Button
              type="primary"
              disabled={curQuestionInfo?.isLast || !isReadEnd}
              onClick={onNextQuestion}
            >
              下一题
            </Button>
          </div>
        </div>
        <div className="start_test">
          <div className="start_text">请开始答题：</div>
          {(readCountDown.minutes === '00' && readCountDown.seconds === '00') ||
          isReadEnd ? null : (
            <div className="read_time">
              读题倒计时{' '}
              <div className="read_left">{readCountDown.minutes}</div>:
              <div className="read_left">{readCountDown.seconds}</div>
            </div>
          )}
          {(answerCountDown.minutes === '00' &&
            answerCountDown.seconds === '00') ||
          !isReadEnd ? null : (
            <div className="read_time">
              答题倒计时{' '}
              <div className="read_left">{answerCountDown.minutes}</div>:
              <div className="read_left">{answerCountDown.seconds}</div>
            </div>
          )}
        </div>
        <div className="test_content">
          <div className="test_title_desc">
            <div>{convertToChinaNum(questionContent?.parentIndex + 1)}、</div>
            <RenderHtml
              cname="auto-img"
              value={questionDetail?.questions_content}
              // onClick={(e: any) => perviewimg(e)}
            ></RenderHtml>
          </div>
          <div className="answer_content">
            {/* 单选题 */}
            {questionDetail?.questions_type === 0 &&
              SingleChoice(questionDetail.questions_options, answer[0])}
            {/* 多选题 */}
            {questionDetail?.questions_type === 1 &&
              MultipleChoice(questionDetail.questions_options, answer)}

            {/* 填空题 */}
            {questionDetail?.questions_type === 2 &&
              FillInTheBlank(questionDetail.questions_options, answer)}

            {/* 主观题 */}
            {questionDetail?.questions_type === 3 &&
              SubjectiveQuestion(questionDetail.id, answer)}

            {/* 判断题 */}
            {questionDetail?.questions_type === 4 &&
              JudgementQuestion(questionDetail.questions_options, answer[0])}

            {/* 题组 */}
            {questionDetail?.questions_type === 5 &&
              QuestionGroup(questionDetail.groupQuestions, answer)}
          </div>
        </div>
      </div>
    );
  },
);

export default QuestionContent;
