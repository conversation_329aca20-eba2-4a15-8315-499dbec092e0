import HTTP from './index';
//登录
export function login() {
  return HTTP.get(`/rman/v1/account/login?loginName=admin&password=123456`);
}
export function rootOrganization() {
  return HTTP.get(`/unifiedplatform/v1/organization/root`)
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}
export function codeOrganization(organizationCode?: string) {
  return HTTP.get(
    organizationCode ? 
      `/unifiedplatform/v1/organization/user?code=${organizationCode}`
      :`/unifiedplatform/v1/organization/user?`
  )
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}
export function childOrganization(organizationCode: string) {
  return HTTP.get(`/unifiedplatform/v1/organization/child/${organizationCode}`)
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}
export function getAllUserByOrg(data: any) {
  return HTTP.post(`/unifiedplatform/v1/organization/origina/users`, data)
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}
