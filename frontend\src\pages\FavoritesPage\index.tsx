import React, { useState, useEffect } from "react";
import { Form, Image, Table, Input, Button, Select, Space, Menu, Layout, message, Modal, DatePicker, Radio, Checkbox } from "antd";
import { EyeOutlined, Bar<PERSON>hartOutlined, DeleteOutlined, DownloadOutlined, CopyFilled, CopyOutlined, Plus<PERSON>ircleOutlined, EditOutlined, QuestionCircleFilled, createFromIconfontCN } from "@ant-design/icons";
import "./index.less";
import Contract from '@/api/Contract';
const { Header, Sider, Content } = Layout;
const { Option } = Select;
const { SubMenu } = Menu;

import dayjs from 'dayjs';

import moment from "moment";
import RenderHtml from "@/components/renderHtml";
import examType from "@/types/examType";
import TestSystemHeader from "@/components/TestSystemHeader";
import { useSelector } from "umi";
const IconFont = createFromIconfontCN({
    scriptUrl: '//at.alicdn.com/t/c/font_2019002_dva91dyojmi.js',
});


const FavoritesPage: React.FC = () => {
    const [form] = Form.useForm();
    const { title, logoUrl, isShow } = useSelector<any, any>(
        state => state.themes,
    );
    const [data, setData] = useState([]);
    const [pagtotal, setPagtotal] = useState();

    const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]); // 当前选中的行
    const [menuData, setMenuData] = useState([]);
    const [pagination, setPagination] = useState({
        current: 1,  // 当前页码
        pageSize: 10, // 每页显示的数量
    });
    const [classifyId, setClassifyId] = useState<string | undefined>(); const [itemName, setitemName] = useState('')

    const [opendates, setOpenDates] = useState<[number | null, number | null]>([null, null]); // 开放时间
    const [open, setOpen] = useState(false);
    const [Details, setDetails] = useState<any>({})
    const [openKeys, setOpenKeys] = useState<string[]>([]); // 当前展开的菜单项
    const [selectedKeys, setSelectedKeys] = useState<string[]>([]); // 当前选中的菜单项
    const [dateRange, setDateRange] = useState(null);
    const removeMathML = (html: string): string => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        // 移除数学公式相关的元素
        const mathElements = doc.querySelectorAll('.math-tex, mjx-container');
        mathElements.forEach((el) => el.remove());
        return doc.body.textContent || '';  // 获取去除数学公式后的纯文本内容
    };

    // 表格列配置
    const columns = [
        {
            title: "序号",
            dataIndex: "index",
            key: "index",
            width: 40,
            align: 'center',
            render: (_: any, __: any, index: number) => {
                return (pagination.current - 1) * pagination.pageSize + index + 1; // 计算序号
            },
        },
        {
            title: "题目",
            dataIndex: "questions_content",
            key: "questions_content",
            width: 200,
            align: 'center',
            render: (value: any) => {
                // 移除 MathML 内容
                const extractedText = removeMathML(value);

                // 返回经过处理的内容，使用 RenderHtml 渲染
                return <RenderHtml cname="auto-img" value={extractedText} />;
            },
            // render: (text: any) => {
            //     return <RenderHtml cname="auto-img" value={text}></RenderHtml>
            // }
        },
        {
            title: "收藏时间",
            dataIndex: "createDate",
            key: "createDate",
            width: 100,
            align: 'center',
            render: (text: string | number | Date) => {
                return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        {
            title: "操作",
            key: "action",
            width: 90, // 调整列宽以适应内容
            align: "center",
            fixed: "right",
            render: (_: any, record: any, index: any) => (
                <Space className="clexs" size="middle">
                    <a
                        style={{ color: "#1890ff", marginRight: 8, fontSize: '13px' }}
                        onClick={() => actionBtun("查看详情", record, index)}
                    >查看详情</a>
                    <a
                        style={{ color: "#1890ff", fontSize: '13px' }}
                        onClick={() => actionBtun("取消收藏", record, index)}
                    >取消收藏</a>
                </Space >
            ),
        },


    ];

    useEffect(() => {
        fetchMenuData(1, 100000);
    }, []);

    // 更新分页、分类时获取数据
    useEffect(() => {
        if (classifyId) {
            const updatedDataSource = createDataSource();
            fetchTestData(updatedDataSource);
        }
    }, [pagination.current, pagination.pageSize, classifyId]);

    // 生成当前数据源
    const createDataSource = () => {
        return {
            page: pagination.current,
            size: pagination.pageSize,
            classifyId: classifyId || "",
            questions_content: "",
            startDate: "",
            endDate: "",
        };
    };

    // 获取测试数据
    const fetchTestData = async (dataSource: any) => {
        const res = await Contract.collectQuestionpage(dataSource);

        if (res.status === 200) {
            setData(res.data.data);
            setPagtotal(res.data.totalCount);
        }
    };

    // 取消收藏
    const collectQuestion = async (dataSource: any) => {
        const res = await Contract.collectQuestion(dataSource);
        if (res.status === 200) {
            fetchTestData(createDataSource());
            message.success("取消成功！");
        }
    };

    // 获取菜单数据
    const fetchMenuData = async (page: number, size: number) => {
        const res = await Contract.classification({ page, size });
        if (res.status === 200) {
            const formattedMenu = formatMenuData(res.data.data);
            setMenuData(formattedMenu);
            if (res.data.data[0]?.children && res.data.data[0].children.length > 0) {
                // 默认选中第一个父级的第一个子项
                setClassifyId(res.data.data[0].children[0].id);
                setitemName(res.data.data[0].children[0].name);
                setOpenKeys([res.data.data[0].id]);  // 展开第一个父项
                setSelectedKeys([res.data.data[0].children[0].id]); // 默认选中第一个子项
            }
        }
    };

    const formatMenuData = (data: any, parentName: string | null = null) => {
        return data.map((item: any) => {
            if (item.children && item.children.length > 0) {
                return (
                    <SubMenu
                        key={item.id}
                        title={
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                {/* 父级菜单图标 */}

                                {item.icon && <IconFont style={{ fontSize: '18px', marginRight: '8px' }} type={item.icon} />}
                                {item.name}
                            </div>
                        }
                        data-name={item.name}
                        data-parent={parentName || ''}
                    >
                        {formatMenuData(item.children, item.name)} {/* 传递当前层级的父级名称 */}
                    </SubMenu>

                );
            }

            return (
                <Menu.Item key={item.id} data-name={item.name} data-parent={parentName}>
                    {item.name}
                </Menu.Item>
            );
        });
    };


    // 点击菜单事件
    const handleMenuClick = (e: any) => {
        const menuItem = e.domEvent.currentTarget;
        const itemName = menuItem.getAttribute('data-name');
        const parentName = menuItem.getAttribute('data-parent');
        setClassifyId(e.key);
        if (parentName === '专题练习') {
            setitemName(itemName);
        } else {
            setitemName('');
        }
    };

    // 搜索
    const handleSearch = () => {
        console.log(opendates, 'opendates');
        form.validateFields().then(values => {
            const dataSource = {
                ...createDataSource(),
                questions_content: values.name,
                startDate: opendates[0] ? opendates[0] + '' : "",
                endDate: opendates[1] ? opendates[1] + '' : "",
            };
            fetchTestData(dataSource);
        });
    };

    // 重置
    const handleReset = () => {
        form.resetFields();
        setDateRange(null);
        fetchTestData(createDataSource());
    };

    // 分页变化
    const handleTableChange = (page: number, pageSize: number) => {
        setPagination({ current: page, pageSize });
    };

    // 确定是否取消收藏？
    const handlecollectClick = () => {
        Modal.confirm({
            content: '确定是否取消收藏？',
            title: '确认',
            icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
            onOk: () => {
                const dataToSend = {
                    "ids": selectedRowKeys
                };
                collectQuestion(dataToSend)
            },
        });

    };
    const [currentIndex, setCurrentIndex] = useState(0); // 用于跟踪当前显示的索引
    //删除操作
    const actionBtun = (value: string, Data: any, index: any) => {
        console.log(Data.id);

        if (value == '取消收藏') {
            Modal.confirm({
                content: '确定是否取消收藏？',
                title: '确认',
                icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
                onOk: () => {
                    const data = []
                    data.push(Data.id)
                    const dataToSend = {
                        "ids": data
                    };
                    collectQuestion(dataToSend)
                },
            });
        } else {
            setCurrentIndex(index)
            setOpen(true);
            setDetails(Data)
            console.log(Data, index);

        }
    };


    const handleDateChange = (data: any) => {
        setDateRange(data);
        const [startDate, endDate] = data;
        const startTimestamp = startDate ? startDate.valueOf() : ' ';
        const endTimestamp = endDate ? endDate.valueOf() : ' ';
        setOpenDates([startTimestamp, endTimestamp]);
    };


    // rowSelection 配置 行选择的回调
    const rowSelection = {
        selectedRowKeys,
        // onChange: onSelectChange,
        onChange: (newSelectedRowKeys: any[]) => {
            setSelectedRowKeys(newSelectedRowKeys);
        },
    };




    const handleCancelCollect = () => {
        Modal.confirm({
            content: '确定是否取消收藏？',
            title: '确认',
            icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
            onOk: () => {
                const data = []
                data.push(Details.id)
                const dataToSend = {
                    "ids": data
                };
                collectQuestion(dataToSend)
                setOpen(false);
            },
        });
    };

    const handleNext = () => {  // 下一题
        if (currentIndex < data.length - 1) {
            // 还有下一项时，更新索引和当前项
            const nextIndex = currentIndex + 1;
            setCurrentIndex(nextIndex);
            setDetails(data[nextIndex]);
            // console.log('当前项:', data[nextIndex]);
        } else {
            message.info('没有下一个了');
        }
    };

    const handleCancel = () => {
        setOpen(false);
    };

    return (
        <div className="favorites" >
            <div
                style={{
                    height:'70px',
                    display: 'flex',
                    alignItems: 'center',
                    background: '#ffffff',
                    boxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.07)'
                }}
                onClick={() => {
                    window.location.href = '/exam/#/testsystem';
                }}
                className="header_clickable"
            >
                <span className='icon_box'>
                    <img
                        src={logoUrl || require('@/images/login/default_logo.png')}
                        style={{
                            height: '32px'
                        }}
                    />
                </span>
                <TestSystemHeader title="招警联考测试系统" />
            </div>
            <div></div>
            <Layout className="" style={{ height: "calc(100vh - 52px)" }}>
                {/* 侧边栏 */}
                <Sider width={200}>
                    <Menu
                        onClick={handleMenuClick}
                        mode="inline"
                        style={{ height: '100%' }}
                        openKeys={openKeys} // 控制展开的菜单项
                        selectedKeys={classifyId || ''} // 控制选中的菜单项
                        onOpenChange={(keys) => setOpenKeys(keys)}
                    >
                        {menuData}
                    </Menu>
                </Sider>
                <Layout style={{ flex: 1 }}>
                    <Content style={{
                        background: "#fff",
                        padding: 24,
                        margin: 0,
                        minHeight: 280,
                        flex: 1,
                    }}
                        className="conts"
                    >
                        {/* 筛选和操作区域 */}
                        <Form
                            form={form}
                            layout="inline"
                            className="formyangs"
                            style={{ marginBottom: 16 }}
                        >
                            <Form.Item name="name" >
                                <Input placeholder="测试名称" style={{ width: '200px' }} />
                            </Form.Item>

                            <Form.Item label="收藏时间">
                                <DatePicker.RangePicker
                                    value={dateRange}
                                    onChange={(dates) => handleDateChange(dates)}
                                    format="YYYY-MM-DD"
                                />
                            </Form.Item>

                            <Button type="primary" onClick={handleSearch}>
                                搜索
                            </Button>

                            <Form.Item style={{ marginLeft: '20px' }} >
                                <Button onClick={handleReset}>重置</Button>
                            </Form.Item>

                            <Form.Item>
                                <Button type="primary" disabled={selectedRowKeys.length === 0} onClick={handlecollectClick}>取消收藏</Button>
                            </Form.Item>
                        </Form>
                        {/* 表格区域 */}
                        <Table
                            columns={columns}
                            dataSource={data}
                            // loading={loading}
                            pagination={{
                                position: ["bottomCenter"], // 确认位置

                                current: pagination.current,
                                pageSize: pagination.pageSize,
                                total: pagtotal,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                onChange: handleTableChange,
                            }}
                            rowKey="id"
                            rowSelection={rowSelection} // 添加选择框功能
                            scroll={{ y: 'calc(100vh - 320px)' }}
                            style={{ flex: 1 }}
                        />
                    </Content>
                </Layout>


                <Modal
                    title={
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <div>
                                <Button type="primary" style={{ marginRight: '8px' }} onClick={handleCancelCollect}>
                                    取消收藏
                                </Button>
                                <Button type="default" onClick={handleNext}>
                                    下一题
                                </Button>
                            </div>
                        </div>
                    }
                    className="modl"
                    open={open}
                    footer={null} // 隐藏底部按钮
                    onCancel={handleCancel}
                    width='50%'
                >
                    <div className="top" >
                        <div className="type">
                            <div className='fs'>
                                <RenderHtml cname="auto-img"
                                    value={Details.questions_content}>
                                </RenderHtml>
                            </div>
                        </div>
                        <div className="answers">
                            {Details?.questions_type === 0 ? ( //单选
                                <Radio.Group value={Details?.questions_answers[0]}>
                                    {Details?.questions_options.map(
                                        (item_0: any, index_0: number) => {
                                            return (
                                                <div className="answer_item" key={index_0}>
                                                    <Radio
                                                        value={String.fromCharCode(64 + Number(index_0 + 1))}
                                                    >
                                                        {String.fromCharCode(64 + Number(index_0 + 1))}
                                                    </Radio>
                                                    <RenderHtml
                                                        cname="radio_content auto-img"
                                                        value={item_0.content}
                                                        onClick={(e) => perviewimg(e)}
                                                    ></RenderHtml>
                                                </div>
                                            );
                                        },
                                    )}
                                </Radio.Group>
                            ) : Details?.questions_type === 1 ? ( //多选
                                <Checkbox.Group value={Details?.questions_answers}>
                                    {Details?.questions_options.map(
                                        (item_1: any, index_1: number) => {
                                            return (
                                                <div className="answer_item" key={index_1}>
                                                    <Checkbox
                                                        value={String.fromCharCode(64 + Number(index_1 + 1))}
                                                    >
                                                        {String.fromCharCode(64 + Number(index_1 + 1))}
                                                    </Checkbox>
                                                    <RenderHtml
                                                        cname="auto-img"
                                                        value={item_1.content}
                                                        onClick={(e) => perviewimg(e)}
                                                    ></RenderHtml>
                                                </div>
                                            );
                                        },
                                    )}
                                </Checkbox.Group>
                            ) : Details?.questions_type === 2 ? ( // 填空题
                                Details?.questions_options.map((item_2: any, index_2: number) => {
                                    const dataRange = `${item_2?.answerMin}~${item_2?.answerMax}`;
                                    return (
                                        <div className="answer_item blanks" key={index_2}>
                                            <span>{`第${index_2 + 1}空：`}</span>
                                            {/* <span>{`${item_2.content || dataRange}`}</span> */}
                                            <RenderHtml cname="auto-img" onClick={(e: any) => perviewimg(e)} value={item_2.content || dataRange}></RenderHtml>
                                        </div>
                                    );
                                })
                            ) : Details?.questions_type === 3 ? ( // 主观题
                                <div className="answer_item">
                                    <span>解析：</span>
                                    <RenderHtml
                                        cname="auto-img"
                                        value={Details?.questions_analysis}
                                        onClick={(e) => perviewimg(e)}
                                    ></RenderHtml>
                                </div>
                            ) : Details?.questions_type === 5 ? ( // 主观题
                                <div className="subject">
                                    {Details?.groupQuestions?.map((queItem: any, index: any) => {
                                        return (
                                            <div className="content_row" key={queItem.id}>
                                                <div className="content">
                                                    <div className="type" style={{ display: 'flex', alignItems: ' baseline' }}>
                                                        {/* (
                                                        <span>{`${examType.optionType_[queItem.questions_type]
                                                            }题`}</span>
                                                        ) */}
                                                        <span className="awsS" >{index + 1},</span>

                                                        <RenderHtml
                                                            cname="auto-img "
                                                            value={queItem.questions_content}
                                                        // onClick={(e: any) => perviewimg(e)}
                                                        ></RenderHtml>
                                                    </div>

                                                </div>
                                                {queItem.fileList?.length > 0 && (
                                                    <div className="fileList_">
                                                        <span>题目附件：</span>
                                                        <div>
                                                            {queItem.fileList.map((item: any, index: number) => {
                                                                return (
                                                                    <a
                                                                        href={item.attachmentSource}
                                                                        key={index}
                                                                        target={item.attachmentSource}
                                                                        title={item.attachmentName || ''}
                                                                    >
                                                                        {item.attachmentName || ''}
                                                                    </a>
                                                                );
                                                            })}
                                                        </div>
                                                    </div>
                                                )}
                                                {queItem.hasAttachment?.length > 0 && (
                                                    <div className="fileList_upload">
                                                        <span>上传附件：</span>
                                                        <div>
                                                            {queItem.hasAttachment.map((item: any, index: number) => {
                                                                return (
                                                                    <div key={index}>
                                                                        <span>{item.name}</span>
                                                                        <span>{item.required && `(必传*)`}</span>
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    </div>
                                                )}
                                                <div className="subject_answers">
                                                    {queItem.questions_type === 0 ? ( //单选
                                                        <Radio.Group value={queItem.questions_answers[0]}>
                                                            {queItem.questions_options.map(
                                                                (item_0: any, index_0: number) => {
                                                                    return (
                                                                        <div className="answer_item" key={index_0}>
                                                                            <Radio
                                                                                value={String.fromCharCode(
                                                                                    64 + Number(index_0 + 1),
                                                                                )}
                                                                            >
                                                                                {String.fromCharCode(64 + Number(index_0 + 1))}
                                                                            </Radio>
                                                                            <RenderHtml
                                                                                style={{ display: 'flex' }}
                                                                                cname="radio_content auto-imgs"
                                                                                value={item_0.content}
                                                                                onClick={(e: any) => perviewimg(e)}
                                                                            ></RenderHtml>
                                                                        </div>
                                                                    );
                                                                },
                                                            )}
                                                        </Radio.Group>
                                                    ) : queItem.questions_type === 1 ? ( //多选
                                                        <Checkbox.Group value={queItem.questions_answers}>
                                                            {queItem.questions_options.map(
                                                                (item_1: any, index_1: number) => {
                                                                    return (
                                                                        <div className="answer_item" key={index_1}>
                                                                            <Checkbox
                                                                                value={String.fromCharCode(
                                                                                    64 + Number(index_1 + 1),
                                                                                )}
                                                                            >
                                                                                {String.fromCharCode(64 + Number(index_1 + 1))}
                                                                            </Checkbox>
                                                                            <RenderHtml
                                                                                cname="auto-img"
                                                                                value={item_1.content}
                                                                            ></RenderHtml>
                                                                        </div>
                                                                    );
                                                                },
                                                            )}
                                                        </Checkbox.Group>
                                                    ) : queItem.questions_type === 2 ? ( // 填空题
                                                        queItem.questions_options.map(
                                                            (item_2: any, index_2: number) => {
                                                                const dataRange = `${item_2?.answerMin}~${item_2?.answerMax}`;
                                                                return (
                                                                    <div className="answer_item blanks" key={index_2}>
                                                                        <span>{`第${index_2 + 1}空：`}</span>
                                                                        {/* <span>{`${item_2.content || dataRange}`}</span> */}
                                                                        <RenderHtml
                                                                            cname="auto-img"
                                                                            onClick={(e: any) => perviewimg(e)}
                                                                            value={item_2.content || dataRange}
                                                                        ></RenderHtml>
                                                                    </div>
                                                                );
                                                            },
                                                        )
                                                    ) : queItem.questions_type === 3 ? ( // 主观题
                                                        <div className="answer_item">
                                                            <span>解析：</span>
                                                            <RenderHtml
                                                                cname="auto-img"
                                                                value={queItem.questions_analysis}
                                                            ></RenderHtml>
                                                        </div>
                                                    ) : (
                                                        // 判断题
                                                        <Radio.Group value={queItem.questions_answers[0]}>
                                                            {queItem.questions_options.map(
                                                                (item_4: any, index_4: number) => {
                                                                    return (
                                                                        <div className="answer_item" key={index_4}>
                                                                            <Radio
                                                                                value={String.fromCharCode(
                                                                                    64 + Number(index_4 + 1),
                                                                                )}
                                                                            >
                                                                                {String.fromCharCode(64 + Number(index_4 + 1))}
                                                                            </Radio>
                                                                            <div className="radio_content">
                                                                                {item_4.content}
                                                                            </div>
                                                                        </div>
                                                                    );
                                                                },
                                                            )}
                                                        </Radio.Group>
                                                    )}
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                // 判断题
                                <div>
                                    11
                                </div>
                            )
                            }
                        </div>
                        <div className="footer">
                            <div style={{ display: 'flex' }} >
                                <span style={{ display: 'flex' }} className="aws">正确答案：<span className="blus" >
                                    <RenderHtml
                                        cname="auto-img"
                                        value={Details?.questions_answers ? Details?.questions_answers : '无'}
                                    ></RenderHtml>
                                </span></span>
                                <span className="aws" style={{ marginLeft: '40px' }} >你的答案：<span className="blus" >{Details?.answer ? Details?.answer : '无'}</span></span>
                            </div>
                            <div className="analysis">
                                解析:
                                <RenderHtml
                                    value={Details?.questions_analysis}
                                ></RenderHtml>
                            </div>
                        </div>
                    </div>

                </Modal>

            </Layout >
        </div>
    );
};

export default FavoritesPage;
