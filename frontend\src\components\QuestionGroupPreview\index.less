.preview{
  .ant-drawer-content-wrapper{
    width: 100%;
    .ant-drawer-content{
      // width: 580px;
      .ant-drawer-header{
        display: flex;
        .ant-drawer-header-title{
          flex: 1 1;
        }
        .ant-drawer-extra{
          display: flex;
          .ant-btn{
            margin-left: 10px;
          }
        }
      }
      .ant-drawer-wrapper-body{
        .ant-drawer-body{
          .content_row{
            display: flex;
            flex-direction: column;
            .content{
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              flex-wrap: wrap;
              margin-bottom: 20px;
              >span{
                margin-right: 10px;
              }
              .type{
                margin-left: 5px;
                >span{
                  opacity: 0.5;
                }
              }
              .spcialDom {
                max-width: 100%;
                p{
                  margin-bottom: 0 !important;
                  width: 100%;
                  overflow: hidden;
                }
                img{
                  max-width: 50px;
                  height: auto;
                  vertical-align:bottom;
                }
              }
            }
            .fileList_{
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              margin-bottom: 20px;
              >span{
                width: 75px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
              >div{
                width: calc(100% - 75px);
                display: flex;
                flex-direction: column;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
            .fileList_upload{
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              margin-bottom: 20px;
              >span{
                width: 75px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
              >div{
                width: calc(100% - 75px);
                display: flex;
                flex-direction: column;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                >div{
                  >span:first-child{
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                  }
                  >span:last-child{
                    margin-left: 5px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    color: #cbcbcb;
                  }
                }
              }
            }
            .answers{
              margin-left: 20px;
              .answer_item{
                display: flex;
                align-items: flex-start;
                flex-direction: row;
                margin-bottom: 10px;
                .radio_content{
                  font-size: 14px;
                }
                .spcialDom {
                  flex:1 1;
                  img{
                    max-width: 50px;
                    height: auto;
                    vertical-align:bottom;
                  }
                }
              }
              .blanks{
                >span{
                  margin-right: 10px;
                }
              }
            }
            .spcialDom {
              img{
                height: auto;
                max-width: 50px;
                vertical-align:bottom;
              }
            }
            .auto-img {
              img {
                // max-width: 50px;
                height: auto;
                vertical-align: bottom;
              }
            }
            >p:first-child{
              width: 119px;
              background: #F7F8FA;
              border-right: solid 1px #EEEEEE;
            }
            >p:last-child{
              flex: 1;
            }
          }
          .see_jiexi{
            width: 100%;            
            height: 35px;
            background-color: #f4f4f4;
            display: flex;
            align-items: center;
            padding-left: 10px;
            cursor: pointer;
            color: var(--primary-color);
          }

          .xiangjie{
            width: 100%;
            height: auto;
            padding-left: 10px;
            line-height: 35px;
            background-color: #f4f4f4;
            margin-bottom: 20px;

            img{
              max-width: 100%;
              height: auto;
            }
          }

          .other_view{
            width: 100%;
            height: auto;
            display: flex;
            align-items: center;
            margin-top: 10px;
            color: #8E8E8E;

            .left_view{
              width: 25%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: flex-end;
              padding-right: 20px;
            }

            .right_view{
              width: 75%;
              height: 100%;
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              
              .circle_view{
                padding: 5px 10px;
                background-color: rgba(221,236,255,0.5);
                margin-bottom: 5px;
                border-radius: 20px;
                margin-right: 10px;
                color: var(--primary-color);
              }
            }
          }
        }
      }
    }
  }
  
}