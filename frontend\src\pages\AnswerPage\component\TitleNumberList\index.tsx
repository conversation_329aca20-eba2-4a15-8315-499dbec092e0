import { FC, useContext } from 'react';
import { PageInfoContext } from '../../context';
import './index.less';

interface ITitleListProp {
  // testTitleList: any[];
  // curQuestionId: string;
  // groupItemId?: string;
  // answer: any[];
  onChangeItem: (
    item: any,
    index: number,
    titleId: string,
    titleIndex: number,
    groupItemid?: string,
  ) => void;
}

const TitleNumberList: FC<ITitleListProp> = ({
  // testTitleList,
  // curQuestionId,
  // groupItemId,
  // answer,
  onChangeItem,
}) => {
  const { answer, questionList, curQuestionInfo } = useContext(PageInfoContext);
  const { questionId: curQuestionId, groupItemId } = curQuestionInfo;
  // 点击序号
  const handleClickItem = (item: any, index: number, groupItemId?: string) => {
    onChangeItem(
      item,
      item?.parentIndex,
      item?.question?.partId,
      index,
      groupItemId,
    );
  };
  return (
    <div className="exam_title_container">
      {questionList?.length > 0 &&
        questionList.map((item: any, itemIndex: number) => {
          return (
            <div key={item.id} className="title_item_box_container">
              <div className="item_box_title">{item.name}</div>
              <div className="split_line" />
              <div className="checkboxs">
                {item.questions.map((cell: any, index: number) => {
                  // 题组的处理
                  if (cell?.question?.questions_type === 5) {
                    const groupQuestions = cell?.question?.groupQuestions;
                    return (
                      <>
                        {groupQuestions?.map((groupItem: any) => {
                          const isFill =
                            answer?.find(
                              (ans: any) => ans?.questionId == groupItem?.id,
                            )?.answer?.length > 0;
                          return (
                            <div
                              key={cell?.question?.id + '_' + groupItem?.id}
                              className={`checkbox_item ${
                                cell?.question?.id == curQuestionId &&
                                groupItemId == groupItem?.id
                                  ? 'checked'
                                  : isFill || groupItem.status === 2
                                  ? 'filled'
                                  : ''
                              }`}
                              onClick={() =>
                                handleClickItem(cell, itemIndex, groupItem?.id)
                              }
                            >
                              {groupItem.itemIndex + 1}
                            </div>
                          );
                        })}
                      </>
                    );
                  }
                  // 非题组的处理
                  return (
                    <div
                      key={cell?.question?.id}
                      className={`checkbox_item ${
                        cell?.question?.id === curQuestionId
                          ? 'checked'
                          : cell.status === 2
                          ? 'filled'
                          : ''
                      }`}
                      onClick={() => handleClickItem(cell, itemIndex)}
                    >
                      {cell.itemIndex + 1}
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
    </div>
  );
};

export default TitleNumberList;
