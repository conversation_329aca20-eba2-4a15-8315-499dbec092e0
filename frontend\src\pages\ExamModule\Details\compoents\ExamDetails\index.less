.exampage {
  background: #F7F9FA;
  min-height: 100vh;
  height: 100%;

  .wave-line {
    text-decoration: underline wavy red;
  }

  .auto-img {

    // max-height: 100px;
    overflow: hidden;

    img {
      max-width: 900px;
      height: auto;
      vertical-align: bottom;
    }
  }

  .ant-card {
    border-radius: 10px;
  }

  .scflow {
    overflow-y: scroll;
    height: 86vh;
  }

  .yul_scflow {
    overflow-y: auto;
    position: relative;

    /* 隐藏滚动条 */
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
  }

  .highlight {
    text-decoration: underline wavy red;
    /* 波浪线 */
    text-decoration-thickness: 2px;
    /* 线的厚度 */
    color: inherit;
    /* 保持文字颜色一致 */
  }

  .card {
    background: #FFFFFF;
    border-radius: 10px;
    padding: 20px 10px;
    position: relative;
    // max-height: 100%;
    max-height: calc(90vh - 20px);
    min-height: calc(90vh - 20px);
    overflow-y: auto;
    cursor: pointer;


    .card_name {
      display: flex;
      justify-content: space-between;
      padding-bottom: 5px;
      border-bottom: 1px dashed #C5C5C5;
      margin-bottom: 20px;
      padding-bottom: 10px;

      .timu_name {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #2A2A2A;

        .name_padding {
          padding-right: 8px;
        }

        .blues {
          color: #549CFF;
        }
      }

      .name_cr {
        display: flex;

        .cr {
          display: flex;
          align-items: center;
          padding: 4px;
        }

        .name_bor {
          width: 16px;
          height: 16px;
          margin-left: 4px;
          border-radius: 4px;
          border: 1px solid #549CFF;
        }

        .reds {
          border: 1px solid #DD0F0F;
        }
      }
    }

    .bt_card {
      .select_title {
        border-bottom: 1px dashed #C5C5C5;
        // margin: 20px 0;
        padding-bottom: 10px;
        margin-top: 20px;

        .title_sz {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #2A2A2A;
        }
      }

      .bt_flex {
        display: flex;
        flex-wrap: wrap;
        max-width: 100%;
        margin: 19px auto;

        .border-blue {
          color: #549CFF;
          border: 1px solid #549CFF;
          /* 蓝色边框 */
        }

        .border-red {
          color: #DD0F0F;
          border: 1px solid #DD0F0F;
          /* 红色边框 */
        }

        .bts {
          width: 36px;
          height: 36px;
          line-height: 40px;
          margin: 7px;
          border-radius: 6px;
          text-align: center;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }


    }

    .flexs {
      position: sticky;
      top: -20px;
      z-index: 999;
      background-color: #fff;
      padding: 10px 20px 21px 20px;
    }

    .middle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px dashed #C5C5C5;
      padding-bottom: 10px;

      .middle_name {
        .fw {
          font-weight: 500;
          font-size: 16px;
          color: #2A2A2A;
        }

        .fw400 {
          font-weight: 400;
          font-size: 14px;
        }

        .dlename {
          font-family: PingFangSC, PingFang SC;

        }

        .mato {
          margin: 10px 0;
        }
      }

      .name_point {
        font-family: PingFangSC, PingFang SC;
        font-size: 14px;
        color: #549CFF;
      }

      .fs {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 20px;
        color: #549CFF;
        line-height: 26px;
        text-align: left;
      }
    }

    .question {
      margin-top: 20px;
    }

    .questions_list {
      .answers {
        margin-top: 22px;

        .type_other {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #549CFF;
          margin-top: 20px;
        }

        .type {
          // display: flex;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #919191;
          margin-top: 20px;

          .fs {
            font-size: 13px;
            padding-left: 10px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #303030;
          }

          .color30 {
            color: #303030;
          }

          .color52 {
            color: #525252;
          }
        }

        .type_xz {
          // margin-left: 30px;
          margin: 20px 15px;

          .answer_item {
            display: flex;
            align-items: flex-start;
            flex-direction: row;
            flex-wrap: wrap;
            margin: 20px 0;

            .radio_content {
              font-size: 14px;
            }

            .spcialDom {
              flex: 1 1;

              img {
                max-width: 50px;
                height: auto;
                vertical-align: bottom;
              }
            }
          }

          .blanks {
            >span {
              margin-right: 10px;
            }
          }
        }
      }

      .form_item_header {
        position: relative;
        display: flex;
        align-items: stretch;
        margin-top: 20px;

        .tag {
          margin-right: 10px;
          display: inline-block;
          width: 4px;
          height: 23px;
          background: var(--primary-color);
        }

        .span_left {
          display: flex;
          align-items: center;
        }
      }
    }


    .Record {
      margin: 10px 0;
      background: #F7F9FA;
      border-radius: 6px;
      padding: 10px;

      .scyangs {
        display: flex;
        justify-content: flex-end;
      }



      .Record_box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .box_title {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .ant-image .ant-image-img {
            width: 38px !important;
            height: 38px !important;
            border-radius: 50%;
          }

          .title {
            margin-left: 10px;
            display: flex;
            flex-direction: column;
          }
        }

        .box_icon {
          display: flex;
          align-items: center;
          justify-content: space-between;
          //   background: #525252;
        }
      }
    }

    .box_nr {
      margin-top: 6px;
    }

    .Answer {
      .Answer_item {
        margin: 20px 0;
      }
    }

  }

  .enclosure {
    &.hidden {
      height: 0;
      overflow: hidden;
    }

    // padding: 0 0 0 66px;
    display: flex;
    flex-direction: row;
    align-items: flex-start;

    .ant-upload {
      width: 0;
      overflow: hidden;
    }

    >.label {
      white-space: nowrap;
    }

    >span {
      display: flex;

      .ant-upload-list {
        margin-left: 10px;
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;

        .ant-upload-list-item {
          margin: 0 !important;
        }
      }
    }
  }

  .ant-image-preview-img {
    z-index: 99;
    width: 200px !important;
    height: 200px !important;
  }

  /* index.less */
  .rich {
    position: relative;
    top: 50%;
    width: 90%;
  }

  .review-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 17px;
    border: 1px solid #549CFF;
    border-radius: 17px;
    cursor: pointer;
    z-index: 1;
    padding: 6px 50px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #549CFF;
  }

  .add_button {
    border-radius: 17px;
    padding: 0 26px;
    font-family: PingFangSC, PingFang SC;
    font-size: 14px;
    color: #FFFFFF;
  }

  /* 重置焦点样式 */
  .rich-text-editor {
    border: 1px solid #D9D9D9;
    border-radius: 4px;
    cursor: text;
    height: 155px;
    padding: 10px;
    overflow-y: scroll;

  }

  /* 自定义滚动条 */
  .rich-text-editor::-webkit-scrollbar {
    width: 4px;
    /* 设置滚动条宽度 */
  }



  /* 滚动条滑块 */
  .rich-text-editor::-webkit-scrollbar-thumb {
    background: #D8D8D8;
    border-radius: 6px;
  }
}
