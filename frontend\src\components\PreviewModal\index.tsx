import { getstudentinfo } from '@/api/student';
import examType from '@/types/examType';
import { noSupport } from '@/utils';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Button, Checkbox, Drawer, Image, Radio, Table } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { IConfig, useSelector } from 'umi';
import RenderHtml from '../renderHtml';
import './index.less';
import { number } from 'echarts';
interface previewprops {
    detail: any;
    visible: boolean;
    onClose: () => void;
    handlecopy?: () => void;
    handledit?: () => void;
}

const Preview: React.FC<previewprops> = (props) => {
    const { detail, visible, onClose, handlecopy, handledit } = props;
    const [showdetail, setshowdetail] = React.useState(false);
    const [userlist, setuserlist] = React.useState<any[]>([]);
    const [perviewSrc, setPerviewSrc] = React.useState<any>('');
    const [imgvisible, setImgVisible] = React.useState<any>(false);
    const close = () => {
        onClose();
    };
    const copy = () => {
        if (configs.mobileFlag) {
            noSupport();
            return;
        }
        handlecopy && handlecopy();
    };
    const edit = () => {
        if (configs.mobileFlag) {
            noSupport();
            return;
        }
        handledit && handledit();
    };
    const configs: IConfig = useSelector<{ config: any }, IConfig>(
        ({ config }) => config,
    );

    useEffect(() => {
        if (detail && detail.share_users) {
            getshareuserinfo(detail.share_users);
        }
        if (detail) {
            setCloneDetail(detail);
        }
    }, [detail]);
    // 获取用户信息
    const getshareuserinfo = (share_users: any) => {
        if (share_users.length) {
            getstudentinfo({ ids: share_users }).then((res: any) => {
                if (res && res.length) {
                    setuserlist(res);
                } else {
                    setuserlist([]);
                }
            });
        } else {
            setuserlist([]);
        }
    };

    // 预览图片
    const perviewimg = (e: any) => {
        if (e.target.nodeName === 'IMG') {
            setImgVisible(true);
            setPerviewSrc(e.target.currentSrc);
        }
    };

    const [cloneDetail, setCloneDetail] = React.useState<any>(detail);
    // 查看解析
    const handleShowAnalyze = (itemId: string) => {
        const groupList = cloneDetail.groupQuestions.map((item: any) => {
            if (item.id === itemId) {
                item.showDetail = !item.showDetail;
            }
            return item;
        })
        setCloneDetail({ ...cloneDetail, groupQuestions: groupList })
    }
    const getTotalScore = (questions) => {
        return questions?.reduce((total, item) => total + item.groupQuestionScore, 0) || 0;
    };
    return (
        <Drawer
            width={configs.mobileFlag ? '100%' : 700}
            className="preview"
            title={'题目预览'}
            placement="right"
            onClose={close}
            visible={visible}
        >
            {detail && (
                <div className="content_row">
                    <div>
                    </div>
                    <div className="content">
                        <div className="type">
                            {/* (
                            <span>{`${examType.optionType_[detail?.questions_type]}题  ${detail?.score ?? 0
                                }分`} </span>
                            ) */}
                            (
                            <span>
                                {`${examType.optionType_[detail?.questions_type]}题  
            ${detail?.questions_type === 5
                                        ? getTotalScore(detail?.groupQuestions) || detail?.score
                                        : detail?.score ?? 0} 分`
                                }
                            </span>
                            )
                        </div>
                        <RenderHtml
                            cname="auto-img"
                            value={detail.questions_content}
                            onClick={(e) => perviewimg(e)}
                        ></RenderHtml>
                    </div>
                    {detail.fileList?.length > 0 && (
                        <div className="fileList_">
                            <span>题目附件：</span>
                            <div>
                                {detail.fileList.map((item: any, index: number) => {
                                    return (
                                        <a
                                            href={item.attachmentSource}
                                            key={index}
                                            target={item.attachmentSource}
                                            title={item.attachmentName || ''}
                                        >
                                            {item.attachmentName || ''}
                                        </a>
                                    );
                                })}
                            </div>
                        </div>
                    )}
                    {detail.hasAttachment?.length > 0 && (
                        <div className="fileList_upload">
                            <span>上传附件：</span>
                            <div>
                                {detail.hasAttachment.map((item: any, index: number) => {
                                    return (
                                        <div key={index}>
                                            <span>{item.name}</span>
                                            <span>{item.required && `(必传*)`}</span>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    )}
                    <div className="answers">
                        {detail.questions_type === 0 ? ( //单选
                            <Radio.Group value={detail?.questions_answers?.[0] || null}>
                                {detail.questions_options.map(
                                    (item_0: any, index_0: number) => {
                                        return (
                                            <div className="answer_item" key={index_0}>
                                                <Radio
                                                    value={String.fromCharCode(64 + Number(index_0 + 1))}
                                                >
                                                    {String.fromCharCode(64 + Number(index_0 + 1))}
                                                </Radio>

                                                <RenderHtml
                                                    cname="radio_content auto-img"
                                                    value={item_0.content}
                                                    onClick={(e) => perviewimg(e)}
                                                ></RenderHtml>
                                            </div>
                                        );
                                    },
                                )}
                            </Radio.Group>
                        ) : detail.questions_type === 1 ? ( //多选
                            <Checkbox.Group value={detail.questions_answers}>
                                {detail.questions_options.map(
                                    (item_1: any, index_1: number) => {
                                        return (
                                            <div className="answer_item" key={index_1}>
                                                <Checkbox
                                                    value={String.fromCharCode(64 + Number(index_1 + 1))}
                                                >
                                                    {String.fromCharCode(64 + Number(index_1 + 1))}
                                                </Checkbox>
                                                <RenderHtml
                                                    cname="auto-img"
                                                    value={item_1.content}
                                                    onClick={(e) => perviewimg(e)}
                                                ></RenderHtml>
                                            </div>
                                        );
                                    },
                                )}
                            </Checkbox.Group>
                        ) : detail.questions_type === 2 ? ( // 填空题
                            detail.questions_options.map((item_2: any, index_2: number) => {
                                const dataRange = `${item_2?.answerMin}~${item_2?.answerMax}`;
                                return (
                                    <div className="answer_item blanks" key={index_2}>
                                        <span>{`第${index_2 + 1}空：`}</span>
                                        {/* <span>{`${item_2.content || dataRange}`}</span> */}
                                        <RenderHtml cname="auto-img" onClick={(e: any) => perviewimg(e)} value={item_2.content || dataRange}></RenderHtml>
                                        {/* <div
                      className="spcialDom"
                      dangerouslySetInnerHTML={createMarkup(
                        item_2.content || dataRange,
                      )}
                    ></div> */}
                                    </div>
                                );
                            })
                        ) : detail.questions_type === 3 ? ( // 主观题
                            <div className="answer_item">
                                <span>解析：</span>
                                <RenderHtml
                                    cname="auto-img"
                                    value={detail.questions_analysis}
                                    onClick={(e) => perviewimg(e)}
                                ></RenderHtml>
                            </div>
                        ) : detail.questions_type === 5 ? ( // 题组
                            <div className="" >

                                {detail?.groupQuestions?.map((queItem: any) => {
                                    return (
                                        <div className="content_row" key={queItem.id}>
                                            <div className="content">
                                                <div className="type" style={{ display: 'flex', alignItems: ' baseline' }}>
                                                    {/* (
                                                    <span>{`${examType.optionType_[queItem.questions_type]
                                                        }题`}</span>
                                                    ) */}
                                                    (
                                                    <span>{`${examType.optionType_[queItem?.questions_type]}题  ${queItem?.groupQuestionScore ?? 0
                                                        }分`}   </span>
                                                    )

                                                    <RenderHtml
                                                        cname="auto-img"
                                                        value={queItem.questions_content}
                                                    // onClick={(e: any) => perviewimg(e)}
                                                    ></RenderHtml>
                                                </div>

                                            </div>
                                            {queItem.fileList?.length > 0 && (
                                                <div className="fileList_">
                                                    <span>题目附件：</span>
                                                    <div>
                                                        {queItem.fileList.map((item: any, index: number) => {
                                                            return (
                                                                <a
                                                                    href={item.attachmentSource}
                                                                    key={index}
                                                                    target={item.attachmentSource}
                                                                    title={item.attachmentName || ''}
                                                                >
                                                                    {item.attachmentName || ''}
                                                                </a>
                                                            );
                                                        })}
                                                    </div>
                                                </div>
                                            )}
                                            {queItem.hasAttachment?.length > 0 && (
                                                <div className="fileList_upload">
                                                    <span>上传附件：</span>
                                                    <div>
                                                        {queItem.hasAttachment.map((item: any, index: number) => {
                                                            return (
                                                                <div key={index}>
                                                                    <span>{item.name}</span>
                                                                    <span>{item.required && `(必传*)`}</span>
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                </div>
                                            )}
                                            <div className="answers">
                                                {queItem.questions_type === 0 ? ( //单选
                                                    <Radio.Group value={queItem?.questions_answers?.[0] || null}>
                                                        {queItem.questions_options.map(
                                                            (item_0: any, index_0: number) => {
                                                                return (
                                                                    <div className="answer_item" key={index_0}>
                                                                        <Radio
                                                                            value={String.fromCharCode(
                                                                                64 + Number(index_0 + 1),
                                                                            )}
                                                                        >
                                                                            {String.fromCharCode(64 + Number(index_0 + 1))}
                                                                        </Radio>
                                                                        <RenderHtml
                                                                            cname="radio_content auto-img"
                                                                            value={item_0.content}
                                                                            onClick={(e: any) => perviewimg(e)}
                                                                        ></RenderHtml>
                                                                    </div>
                                                                );
                                                            },
                                                        )}
                                                    </Radio.Group>
                                                ) : queItem.questions_type === 1 ? ( //多选
                                                    <Checkbox.Group value={queItem.questions_answers}>
                                                        {queItem.questions_options.map(
                                                            (item_1: any, index_1: number) => {
                                                                return (
                                                                    <div className="answer_item" key={index_1}>
                                                                        <Checkbox
                                                                            value={String.fromCharCode(
                                                                                64 + Number(index_1 + 1),
                                                                            )}
                                                                        >
                                                                            {String.fromCharCode(64 + Number(index_1 + 1))}
                                                                        </Checkbox>
                                                                        <RenderHtml
                                                                            cname="auto-img"
                                                                            value={item_1.content}
                                                                            onClick={(e: any) => perviewimg(e)}
                                                                        ></RenderHtml>
                                                                    </div>
                                                                );
                                                            },
                                                        )}
                                                    </Checkbox.Group>
                                                ) : queItem.questions_type === 2 ? ( // 填空题
                                                    queItem.questions_options.map(
                                                        (item_2: any, index_2: number) => {
                                                            const dataRange = `${item_2?.answerMin}~${item_2?.answerMax}`;
                                                            return (
                                                                <div className="answer_item blanks" key={index_2}>
                                                                    <span>{`第${index_2 + 1}空：`}</span>
                                                                    {/* <span>{`${item_2.content || dataRange}`}</span> */}
                                                                    <RenderHtml
                                                                        cname="auto-img"
                                                                        onClick={(e: any) => perviewimg(e)}
                                                                        value={item_2.content || dataRange}
                                                                    ></RenderHtml>
                                                                </div>
                                                            );
                                                        },
                                                    )
                                                ) : queItem.questions_type === 3 ? ( // 主观题
                                                    <div className="answer_item">
                                                        <span>解析：</span>
                                                        {/* <RenderHtml
                                                        cname="auto-img"
                                                        value={queItem.questions_analysis}
                                                        onClick={(e: any) => perviewimg(e)}
                                                    ></RenderHtml> */}
                                                    </div>
                                                ) : (
                                                    // 判断题
                                                    <Radio.Group value={queItem?.questions_answers?.[0] || null}>
                                                        {queItem.questions_options.map(
                                                            (item_4: any, index_4: number) => {
                                                                return (
                                                                    <div className="answer_item" key={index_4}>
                                                                        <Radio
                                                                            value={String.fromCharCode(
                                                                                64 + Number(index_4 + 1),
                                                                            )}
                                                                        >
                                                                            {String.fromCharCode(64 + Number(index_4 + 1))}
                                                                        </Radio>
                                                                        <div className="radio_content">
                                                                            {item_4.content}
                                                                        </div>
                                                                    </div>
                                                                );
                                                            },
                                                        )}
                                                    </Radio.Group>
                                                )}
                                            </div>
                                            <div className="see_jiexi">
                                                <span
                                                    onClick={() => handleShowAnalyze(queItem.id)}
                                                    style={{ marginRight: '5px' }}
                                                >
                                                    查看解析
                                                </span>
                                                {queItem.showDetail ? <UpOutlined /> : <DownOutlined />}
                                            </div>
                                            {queItem.showDetail && (
                                                <div className="xiangjie">
                                                    <RenderHtml
                                                        value={queItem?.questions_answers}
                                                    // onClick={(e: any) => perviewimg(e)}
                                                    ></RenderHtml>
                                                </div>
                                            )}
                                        </div>
                                    );
                                })}
                            </div>) : (
                            // 判断题
                            <Radio.Group value={detail?.questions_answers?.[0] || null}>
                                {detail.questions_options.map(
                                    (item_4: any, index_4: number) => {
                                        return (
                                            <div className="answer_item" key={index_4}>
                                                <Radio
                                                    value={String.fromCharCode(64 + Number(index_4 + 1))}
                                                >
                                                    {String.fromCharCode(64 + Number(index_4 + 1))}
                                                </Radio>
                                                <div className="radio_content">{item_4.content}</div>
                                            </div>
                                        );
                                    },
                                )}
                            </Radio.Group>
                        )}
                    </div>
                    {detail.questions_type !== 5 && (
                        <div>
                            <div className="see_jiexi">
                                <span
                                    onClick={() => setshowdetail(!showdetail)}
                                    style={{ marginRight: '5px' }}
                                >
                                    查看解析
                                </span>
                                {showdetail ? <UpOutlined /> : <DownOutlined />}
                            </div>
                            <div>
                                {showdetail && (
                                    <div className="xiangjie">
                                        <RenderHtml
                                            value={detail?.questions_analysis}
                                            onClick={(e) => perviewimg(e)}
                                        ></RenderHtml>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            )}
            {/* <div className="other_view">
                <div className="left_view">
                    <span>知识点：</span>
                </div>
                <div className="right_view">
                    {detail?.knowledge_points?.map((item_: any, index: number) => {
                        if (item_.entity) {
                            return (
                                <span className="circle_view" key={index}>
                                    {item_.entity}
                                </span>
                            );
                        }
                    })}
                </div>
            </div>
            <div className="other_view">
                <div className="left_view">
                    <span>认知层次：</span>
                </div>
                {detail?.cognitive_level && (
                    <div className="right_view">
                        <span className="circle_view">{detail?.cognitive_level}</span>
                    </div>
                )}
            </div>
            <div className="other_view">
                <div className="left_view">
                    <span>标签：</span>
                </div>
                <div className="right_view">
                    {detail?.labels?.map((item_: any, index: number) => {
                        if (item_) {
                            return (
                                <span className="circle_view" key={index}>
                                    {item_}
                                </span>
                            );
                        }
                    })}
                </div>
            </div>
            <div className="other_view">
                <div className="left_view">
                    <span>难度：</span>
                </div>
                <div className="right_view">
                    <span>{detail?.questions_difficulty}</span>
                </div>
            </div>
            <div className="other_view">
                <div className="left_view">
                    <span>适用层次：</span>
                </div>
                <div className="right_view">
                    {detail?.questions_level?.map((item_: any, index: number) => {
                        let arr = ['本科生', '研究生'];
                        return (
                            <span style={{ marginRight: '10px' }} key={index}>
                                {arr[item_]}
                            </span>
                        );
                    })}
                </div>
            </div>
            <div className="other_view">
                <div className="left_view">
                    <span>适用院系/部门：</span>
                </div>
                <div className="right_view">
                    {detail?.questions_major?.map((item_: any, index: number) => {
                        return (
                            <span key={index} style={{ marginRight: '10px' }}>
                                {item_.split(',')[1]}
                            </span>
                        );
                    })}
                </div>
            </div>
            <div className="other_view">
                <div className="left_view">
                    <span>适用课程：</span>
                </div>
                <div className="right_view">
                    <span style={{ marginRight: '10px' }}>
                        {detail?.questions_lesson}
                    </span>
                </div>
            </div>
            <div className="other_view">
                <div className="left_view">
                    <span>创建人：</span>
                </div>
                <div className="right_view">
                    <span style={{ marginRight: '10px' }}>{detail?.add_username}</span>
                </div>
            </div>
            <div className="other_view">
                <div className="left_view">
                    <span>创建时间：</span>
                </div>
                <div className="right_view">
                    <span style={{ marginRight: '10px' }}>
                        {dayjs(detail?.creator_time).format('YYYY-MM-DD HH:mm:ss')}
                    </span>
                </div>
            </div>
            <div className="other_view">
                <div className="left_view">
                    <span>分享给：</span>
                </div>
                <div className="right_view">
                    {userlist.map((item_: any, index: number) => {
                        return (
                            <span key={index} style={{ marginRight: '10px' }}>
                                {item_.name}
                            </span>
                        );
                    })}
                </div>
            </div> */}
            {/* 预览图片 */}
            {/* <Image
                width={200}
                style={{ display: 'none' }}
                preview={{
                    visible: imgvisible,
                    src: perviewSrc,
                    onVisibleChange: (value) => {
                        setImgVisible(value);
                        if (!value) {
                            setPerviewSrc('');
                        }
                    },
                }}
            /> */}
        </Drawer>
    );
};

export default Preview;
