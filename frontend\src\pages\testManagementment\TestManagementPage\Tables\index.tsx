import React, { useEffect, useRef, useState } from "react";
import { Table, Button, Image, Space, message, Typography, Modal, List, Input, Popconfirm } from "antd";
import { EditOutlined, DeleteOutlined, PlusOutlined, MinusOutlined, MenuOutlined, CheckOutlined, QuestionCircleFilled } from "@ant-design/icons";
import "./index.less";
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { ListItemProps } from "antd/lib/list";
import { JSX } from "react/jsx-runtime";
import Contract from "@/api/Contract";
import { useLocation, history } from 'umi'
import examType from '@/types/examType';
import SelecttopicModal from '@/components/SelecttopicModal';
import CustomModal from '@/components/CustomModal';
import ScoreSettingModal from '@/components/ScoreSettingModal';
import ScoreSettingModaltwo from '@/components/ScoreSettingModaltwo';
import RenderHtml from "@/components/renderHtml";
import { IconFont } from "@/components";
import PreviewModal from "@/components/PreviewModal";

import ExamPreview from "@/components/ExamPreview";

interface params {
    classifyID: string,
    userTable: any,
    userDatas: any,
    updateUserData: any,
    itemName: string,
    userID: string,
    tataol: string,
    ppsucAction: Boolean,
    toggleTopicVisibility: (isVisible: boolean, partIds: string, selectedValue: any) => void;
}

const Tables: React.FC<params> = (params) => {
    const { userTable, userDatas, updateUserData, classifyID, userID, tataol, itemName, ppsucAction, toggleTopicVisibility } = params
    const location: any = useLocation();
    // 组成部分 弹框
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [sectionName, setSectionName] = useState(""); // 新增部分的名称
    const [sections, setSections] = useState<{ name: string; isEditing: boolean }[]>([
    ]);

    const [newbjList, setbjList] = useState();
    // 保存
    const testSaveAPI = async (data: any) => {
        const res = await Contract.testSave(data);
        if (res.status === 200) {
            updateUserData(res.data);  // 通知父组件更新数据
            localStorage.setItem("userData", JSON.stringify(res.data));  // 将对象转化为字符串
            updateUserData(res.data);  // 通知父组件更新数据
        }
        message.success(res?.message);
    };

    // 删除题目
    const deleteQuestions = async (id: any) => {
        const res = await Contract.deleteQuestion(id);

        if (res.status === 200) {
            getVoByIdAPI(userDatas.id)
        }
        // message.success(res?.message);
    };

    //删除部分
    const deletePartAPI = async (id: any) => {
        const res = await Contract.deletePart(id);

        if (res.status === 200) {
            getVoByIdAPI(userDatas.id)
        }
        // message.success(res?.message);
    };

    const getVoByIdAPI = async (id: any) => {
        const res = await Contract.getVoById(id);
        if (res.status === 200) {
            updateUserData(res.data);  // 通知父组件更新数据
            localStorage.setItem("userData", JSON.stringify(res.data));  // 将对象转化为字符串
            testSaveAPI(res.data)
        }
    };

    // 组成成部分 弹框 
    const showModal = () => {
        setIsModalVisible(true);
    };

    const handleDelete = (item: any) => {
        Modal.confirm({
            content: '确定是否删除？',
            title: '删除确认',
            icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
            onOk: () => {
                deletePartAPI(item.id)
            },
        });
    };


    // 确认操作
    const handleOk = () => {
        // part JSON.parse(storedData)
        
        
        const storedDatas = localStorage.getItem("userData");
        // 确保 storedDatas 存在并且是有效的 JSON 字符串
        console.log(storedDatas,'storedDatas');
        let parsedData = storedDatas ? JSON.parse(storedDatas) : null;
        if (parsedData) {
            const updatedFormDatas = {
                ...parsedData,
                part: [
                    ...parsedData.part,
                    { name: sectionName }
                ]
            };
            if (sectionName) {
                updateUserData(updatedFormDatas);  // 通知父组件更新数据
                testSaveAPI(updatedFormDatas)
                setTopicVisible(true)
                setReplace(sectionName)
                // sectionName
                setSectionName('')
            }
            
            setIsModalVisible(false);
        }
    };

    // 取消操作
    const handleCancel = () => {
        setIsModalVisible(false);
    };

    // 添加新部分
    const handleAddSection = () => {
        if (sectionName.trim() !== "") {
            setSections([...sections, { name: sectionName, isEditing: false }]);
            setSectionName(""); // 清空输入框
        }
    };
    // const [newbjList, setbjList] = useState();
    // 处理编辑输入框内容变更
    const handleEditChange = (index: number, value: string) => {
        const updatedParts = [...userDatas.part];
        updatedParts[index].name = value;
        setbjList({ ...userDatas, part: updatedParts });
    };

    // 保存编辑内容
    const handleSaveEdit = (index: number, value: string) => {
        const updatedParts = [...userDatas.part];
        updatedParts[index].isEditing = false;
        updatedParts[index].name = value;
        setbjList({ ...userDatas, part: updatedParts });
        testSaveAPI(newbjList)
        updateUserData(newbjList);
    };

    // 切换编辑状态
    const handleEdit = (index: number) => {
        const updatedParts = [...userDatas.part];
        updatedParts[index].isEditing = true;
        setbjList({ ...userDatas, part: updatedParts });
    };

    // 拖拽结束时调用，用于更新列表顺序
    const handleDragEnd = (result: any) => {
        if (!result.destination) return;

        const items = Array.from(userDatas.part);
        const [reorderedItem] = items.splice(result.source.index, 1);
        items.splice(result.destination.index, 0, reorderedItem);

        const updatedUserData = { ...userDatas, part: items };
        testSaveAPI(updatedUserData);
        updateUserData(updatedUserData);  // 通知父组件更新数据

    };
    // 组成成部分 end

    // 管理每个题组展开/收起的状态
    const [expandedRows, setExpandedRows] = useState<string[]>([]);

    // 合并单元格方法
    const mergeCells = (text: string, index: number) => {

        const groupData = userTable.map((item: any) => item.name);
        const rowSpan = groupData.indexOf(text) === index ? groupData.filter((x: any) => x === text).length : 0;
        return { children: text, props: { rowSpan } };
    };

    // 展开/收起题组的控制函数
    // const handleExpandToggle = (key: string) => {
    //     setExpandedRows((prev) => {
    //         if (prev.includes(key)) {
    //             return prev.filter((item) => item !== key); // 收起
    //         } else {
    //             return [...prev, key]; // 展开
    //         }
    //     });
    // };
    const handleExpandToggle = (key: string) => {
        setExpandedRows((prev) => {
            if (prev.includes(key)) {
                return prev.filter((item) => item !== key); // 收起
            } else {
                return [...prev, key]; // 展开
            }
        });
    };
    // 数据预处理：为每个部分生成序号
    const processedData = (() => {
        const partIndex: Record<string, number> = {}; // 存储每个部分的当前序号
        return userTable?.map((item) => {
            const part = item.name;
            if (!partIndex[part]) {
                partIndex[part] = 1; // 初始化序号
            } else {
                partIndex[part] += 1; // 递增序号
            }
            return {
                ...item,
                partIndex: partIndex[part], // 将序号添加到数据项中
            };
        });
    })();

    const removeMathML = (html: string): string => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        // 移除数学公式相关的元素
        const mathElements = doc.querySelectorAll('.math-tex, mjx-container');
        mathElements.forEach((el) => el.remove());
        return doc.body.textContent || '';  // 获取去除数学公式后的纯文本内容
    };
    // 表头配置
    const columns = [
        // Table.SELECTION_COLUMN,
        // Table.EXPAND_COLUMN,
        {
            title: "组成部分",
            dataIndex: "name",
            key: "name",
            width: '20%',
            align: 'center',
            render: (text: string, _: any, index: number) => mergeCells(text, index),
        },
        {
            title: "序号",
            dataIndex: "partIndex", // 使用预处理后的序号
            key: "partIndex",
            width: '12%',
            align: "center",
        },
        {
            title: "题目类型",
            dataIndex: "questions_type",
            key: "questions_type",
            width: '20%',
            align: 'center',
            render: (type: string, record: any) => (
                <div style={{ display: 'flex', alignItems: 'center' }} >
                    {Array.isArray(record?.groupQuestions) && record.groupQuestions.length > 0 && (
                        <div style={{ marginBottom: '2px' }} onClick={() => handleExpandToggle(record.id)}>
                            {expandedRows?.includes(record.id) ? (
                                <Image
                                    width={14}
                                    height={14}
                                    src={require('@/images/icons/zk.png')}
                                    title="预览"
                                    preview={false}
                                />
                            ) : (
                                <Image
                                    width={14}
                                    height={14}
                                    src={require('@/images/icons/sq.png')}
                                    title="预览"
                                    preview={false}
                                />
                            )}
                        </div>
                    )}
                    <div style={{ paddingLeft: '10px' }} >{examType.optionType_[Number(type)]}</div>
                </div>
            ),
        },
        {
            title: "题目内容",
            dataIndex: "questions_content",
            key: "questions_content",
            width: '50%',
            align: 'center',
            ellipsis: true,
            render: (text: string, record: any) => {
                // const extractedText = removeMathML(text);
                const extractedText = text ? removeMathML(text) : ''; // 确保 text 有值时才移除 MathML
                if (record?.questions_type == 5) {
                    // 检查 groupQuestions 是否存在且是一个数组
                    const groupQuestions = Array.isArray(record?.groupQuestions) ? record.groupQuestions : [];
                    const extractedText = removeMathML(text);
                    return (
                        <div>
                            {/* 显示第一行的内容，即 text */}
                            <div style={{}}>
                                <RenderHtml cname="auto-img" value={extractedText ? extractedText : '\u200B'}></RenderHtml>
                            </div>

                            {/* 只有在展开时才显示 groupQuestions 的内容 */}
                            {expandedRows.includes(record.id) && groupQuestions.map((detail: any, index: number) => (
                                <div style={{ padding: '15px 0' }} key={index}>
                                    <RenderHtml cname="auto-img" value={detail.questions_content}></RenderHtml>
                                </div>
                            ))}
                        </div>
                    );
                }
                return <RenderHtml cname="auto-img" value={extractedText ? extractedText : '\u200B'}></RenderHtml>;
            },
        },
        {
            title: "答案",
            dataIndex: "questions_answers",
            key: "questions_answers",
            width: '40%',
            align: 'center',
            render: (text: string, record: any) => {
                if (record?.questions_type == 5) {
                    // 确保 groupQuestions 是有效数组且长度大于0
                    const groupQuestions = Array.isArray(record?.groupQuestions) ? record.groupQuestions : [];

                    return (
                        <div>
                            {/* 始终显示第一行内容 text */}
                            <div style={{}}>
                                <RenderHtml cname="auto-img" value={text ? text : '无'}></RenderHtml>
                            </div>

                            {/* 展开时显示 groupQuestions 的内容 */}
                            {expandedRows.includes(record.id) && groupQuestions.map((detail: any, index: number) => (
                                <div style={{ padding: '15px 0' }} key={index}>
                                    <RenderHtml cname="auto-img" value={detail.questions_answers ? detail.questions_answers : '无'}></RenderHtml>
                                </div>
                            ))}
                        </div>
                    );
                }
                return <RenderHtml cname="auto-img" value={text ? text : '无'}></RenderHtml>;
            },


            // render: (value: any) =>
            //     value?.map((item: any) => (
            //         <RenderHtml cname="auto-img" value={item}></RenderHtml>
            //     )),
        },
        {
            title: "分值",
            dataIndex: "score",
            key: "score",
            width: '14%',
            align: 'center',
            // render: (text: string, record: any) => {
            //     if (record?.questions_type == 5) {
            //         // 确保 groupQuestions 是有效数组且长度大于0
            //         const groupQuestions = Array.isArray(record?.groupQuestions) ? record.groupQuestions : [];

            //         return (
            //             <div>
            //                 {/* 始终显示第一行内容 text */}
            //                 <div style={{}}>
            //                     <RenderHtml cname="auto-img" value={text ? text : '0'}></RenderHtml> 哇哇哇哇
            //                 </div>

            //                 {/* 只有在展开时才显示 groupQuestions 的内容 */}
            //                 {expandedRows.includes(record.id) && groupQuestions.map((detail: any, index: number) => (
            //                     <div style={{ padding: '15px 0' }} key={index}>
            //                         <RenderHtml cname="auto-img" value={detail.groupQuestionScore ? detail.groupQuestionScore : '0'}></RenderHtml>
            //                     </div>
            //                 ))}
            //             </div>
            //         );
            //     }

            //     // 对于非 questions_type == 5 的记录，直接渲染 text
            //     return <RenderHtml cname="auto-img" value={text ? text : '0'}></RenderHtml>;
            // },
            render: (text: string, record: any) => {
                if (record?.questions_type == 5) {
                    // 确保 groupQuestions 是有效数组且长度大于0
                    const groupQuestions = Array.isArray(record?.groupQuestions) ? record.groupQuestions : [];

                    // 计算 groupQuestionScore 的总分
                    const totalScore = groupQuestions.reduce((acc, detail) => {
                        return acc + (detail.groupQuestionScore || 0);
                    }, 0);

                    return (
                        <div>
                            {/* 显示总分或原始文本 */}
                            <div style={{}}>
                                <RenderHtml cname="auto-img" value={totalScore > 0 ? totalScore.toFixed(2) : text ? parseFloat(text).toFixed(2) : '0'}></RenderHtml>
                            </div>

                            {/* 只有在展开时才显示 groupQuestions 的内容 */}
                            {expandedRows.includes(record.id) && groupQuestions.map((detail: any, index: number) => (
                                <div style={{ padding: '15px 0' }} key={index}>
                                    <RenderHtml cname="auto-img" value={detail.groupQuestionScore ? parseFloat(detail.groupQuestionScore).toFixed(2) : '0'}></RenderHtml>
                                </div>
                            ))}
                        </div>
                    );
                }

                // 对于非 questions_type == 5 的记录，直接渲染 text
                return <RenderHtml cname="auto-img" value={(text ? parseFloat(text).toFixed(2) : '0')}></RenderHtml>;
            },
        },
        {
            title: "操作",
            key: "action",
            width: '30%',
            align: 'center',
            render: (_: any, record: any, index: number) => (
                // <div className="table_opt">

                // </div >
                <Space className="clexs" size="middle">
                    {record.id && (
                        <Image
                            width={14}
                            height={14}
                            src={require('@/images/icons/ss.png')}
                            title="预览"
                            preview={false}
                            style={{ margin: '0 6px' }}
                            onClick={() => onButton('预览', record, index)}
                        />)
                    }
                    {
                        record.id && (
                            <Image
                                width={14}
                                height={14}
                                src={require('@/images/icons/zf.png')}
                                title="替换"
                                preview={false}
                                style={{ margin: '0 6px' }}
                                onClick={() => onButton('替换', record, index)}
                            />)
                    }

                    {
                        record.id && (
                            <div style={{ margin: '0 6px' }} >
                                < Popconfirm
                                    title="确定要删除吗?"
                                    onConfirm={() => onButton('删除', record, index)}
                                    okText="确定"
                                    cancelText="取消"
                                >
                                    <Image
                                        width={14}
                                        height={14}
                                        src={require('@/images/icons/sc.png')}
                                        title="删除"
                                        preview={false}
                                    />
                                </Popconfirm >
                            </div>
                        )

                    }
                </Space >
            ),
        },
    ];

    const [Switchitem, setSwitchitem] = useState(); //切换题库项
    const [Replace, setReplace] = useState(''); //题库项状态
    const [Examviewsvisible, seExamviewsvisible] = useState<boolean>(false);
    const [Previews, setPreviews] = useState();
    const [previsible, setPrevisible] = useState<boolean>(false);


    const onButton = (name: string, record: any, index: any,) => {
        if (name == '删除') {
            if (record.id) {
                deleteQuestions(record.id)  // 项删除
            } else {
                const matchingItems = userDatas.part.filter((item: { name: any; }) => item.name === record.name);
                deletePartAPI(matchingItems[0].id)  // 单元格删除
            }
        } else if (name == '替换') {
            setSwitchitem(record)
            setReplace('替换题目')
            setTopicVisible(true)
        } else if (name == '查看') {
            setTopCustom(true)
        } else if (name == '预览') {
            setPreviews(record)
            setPrevisible(true)
        }
    };
    const paperClose = () => {
        setPrevisible(false)
        seExamviewsvisible(false);
    };

    //选择题目
    const [topicVisible, setTopicVisible] = useState<boolean>(false); //试题弹框
    const [CustomVisible, setTopCustom] = useState<boolean>(false); //设置答题时长
    const [pointVisible, setPointVisible] = useState<boolean>(false); //分值设置
    const [pointVisible2, setPointVisible2] = useState<boolean>(false); //得分设置
    const [addType, setAddType] = useState<string>('checkbox');
    const currentReplace = useRef<any>(null);
    const [list, setList] = useState<any>([]); //试卷题目列表

    const onOk = (data: any) => {
        const newData = data.map((item: any) => ({ ...item, editable: false }));

        if (addType === 'radio') {
            //替换题目
            const tmep = JSON.parse(JSON.stringify(list));
            const index = tmep.findIndex(
                (x: any) => x.id === currentReplace.current.id,
            );
            tmep[index] = newData[0];
            setList(tmep);
        } else {
            //保留原数据分值
            const tmep = JSON.parse(JSON.stringify(list));
            newData.forEach((item_: any, index: number) => {
                //已保存过的数据的parent_id与之对应 新勾选的id与之对应
                if (
                    tmep.findIndex(
                        (x: any) => x.id === item_.id || x.parent_id === item_.id,
                    ) === -1
                ) {
                    tmep.push(item_);
                }
            });
            setList(tmep);
        }
        setTopicVisible(false);
        setSwitchitem(null);
    };

    // 当弹框数据修改时，更新表格和父组件数据
    const handleUpdateData = (newUserData: any) => {
        localStorage.setItem('userData', JSON.stringify(newUserData));
        updateUserData(newUserData);  // 通知父组件更新数据
    };

    const flattenChildren = (children: any[]): any[] => {
        let result: any[] = [];

        children.forEach(item => {
            result.push(item);  // 先加入当前项
            if (item.groupQuestions) {
                // 如果有 groupQuestions, 则递归展开 groupQuestions
                result = result.concat(flattenChildren(item.groupQuestions));
            }
            if (item.children) {
                // 如果有子节点 children, 递归展开 children
                result = result.concat(flattenChildren(item.children));
            }
        });

        return result;
    };

    const pointFinish = (data: any) => {
        // 递归展开所有题目项（包括多级 children 和 groupQuestions）
        const allQuestionLists = flattenChildren(data).map((item: { id: string; points: number }) => ({
            id: item.id,
            points: item.points
        }));


        const updatedPart = userDatas.part?.map((partItem: any) => {
            const updatedQuestionList = partItem.questionList?.map((questionItem: any) => {
                if (questionItem?.questions_type === 5) {
                    if (questionItem.groupQuestions) {
                        questionItem.groupQuestions.forEach((groupQuestion: any) => {
                            const matchingQuestion = allQuestionLists.find((question: any) => question.id === groupQuestion.id);
                            if (matchingQuestion) {
                                groupQuestion.groupQuestionScore = matchingQuestion.points;
                            }
                        });
                    }
                }

                const matchingQuestion = allQuestionLists.find((question: any) => question.id === questionItem.id);
                if (matchingQuestion) {
                    return { ...questionItem, score: matchingQuestion.points };
                }

                return questionItem;
            });

            // 计算每部分的总分
            const totalScore = updatedQuestionList?.reduce((sum: number, question: any) => sum + (question.score || 0), 0);

            return { ...partItem, questionList: updatedQuestionList, totalScore };
        });

        // 计算所有 part 的总分
        const totalScoreForAllParts = updatedPart.reduce((total: number, part: any) => total + (part.totalScore || 0), 0);

        const updatedFormDatas = {
            ...userDatas,
            part: updatedPart,
            totalScore: totalScoreForAllParts
        };
        testSaveAPI(updatedFormDatas);
        updateUserData(updatedFormDatas);
        setPointVisible(false);
    };

    const pointFinishdef = (data: any) => {
        // let totalScoreForAllParts = 0;  // 初始化总分变量
        // userDatas.part.forEach((item: any) => {
        //     const key = item.id;
        //     if (data[key]) {
        //         const scoreData = data[key];
        //         const totalScore = parseFloat(scoreData.totalScore !== undefined ? scoreData.totalScore : item.totalScore);
        //         const questionQuantity = parseFloat(scoreData.questionQuantity);
        //         const averageScore = totalScore / questionQuantity;
        //         // 累加总分
        //         totalScoreForAllParts += totalScore;
        //         Object.assign(item, scoreData);
        //         if (item.questionList && Array.isArray(item.questionList)) {
        //             item.questionList.forEach((question: any) => {
        //                 question.score = averageScore;
        //             });
        //         }
        //     }
        // });
        // const updatedFormDatas = {
        //     ...userDatas,
        //     part: userDatas.part,
        //     totalScore: totalScoreForAllParts
        // };
        // // console.log(userDatas.part, 'updatedFormDatas');

        // testSaveAPI(updatedFormDatas);
        // updateUserData(updatedFormDatas);
        // userDatas.part.forEach((item: any) => {
        //     if (item.questionGroupScoreConfig === 2) {
        //         toggleTopicVisibility(true, '', []);
        //     }
        // });
    };

    const setups = (partIds: any, bos: boolean, selectedValue: any) => {
        toggleTopicVisibility(bos, partIds, selectedValue);
    }

    const Addtopic = () => {
        if (userDatas?.part.length != 0) {
            setReplace(userDatas?.part[0].name)
            setSwitchitem(userDatas?.part[0])
            setTopicVisible(true)
        }
    }

    const ScoreSettingBtn = () => {
        // 不等于就是产品 等于就是ppsuc (现在写反)
        if (ppsucAction) {
            setPointVisible(true)
        } else {
            setPointVisible2(true)
        }
    }

    const actionBtun = () => {
        if (userDatas.id) {
            var data = {
                listVoByAnswerId: userDatas.id,
                titleName: userDatas.name,
                SelectId: userDatas.id,
                detailIndex: 1,
                property: '预览',
            }
            history.push({
                pathname: `/ExamModule/Answer`,
                state: data
            });
        } else {

        }

    }



    return (
        <div className="Tables" >
            <div className="flexs" >
                <div className="boxs">
                    <div className="jux"></div>
                    <div>测验题目</div>
                </div>
                <Space size="middle" >
                    <Typography.Text className="cszf">测验总分：{(tataol || 0).toFixed(2)}</Typography.Text>
                    {!itemName && (
                        <Button
                            onClick={showModal}
                            type="primary"
                            icon={<span style={{ marginRight: '5px' }}>+</span>}
                        >
                            添加组成
                        </Button>
                    )}
                    {/* <Button onClick={showModal} type="primary" icon={<span style={{ marginRight: '5px' }}>+</span>}>添加组成</Button> */}
                    <Button onClick={() => Addtopic()} type="primary" icon={<span style={{ marginRight: '5px' }}>+</span>}>添加题目</Button>
                    <Button onClick={() => setTopCustom(true)} type="primary" icon={<span style={{ marginRight: '5px' }}>+</span>}>设置答题时长</Button>
                    <Button onClick={() => ScoreSettingBtn()} type="primary" >分值设置</Button>
                    {/* <Button onClick={() => seExamviewsvisible(true)} >预览</Button> */}
                    <Button onClick={() => actionBtun()} >预览</Button>

                </Space>
            </div>
            <div>
                <Table
                    dataSource={processedData}
                    columns={columns}
                    pagination={false}
                    rowKey={(record) => record.key}
                    tableLayout="fixed"
                    scroll={{ x: 1000 }}
                />
            </div>
            {/* 组成 */}
            <Modal
                title="新增组成部分"
                visible={isModalVisible}
                onOk={handleOk}
                onCancel={handleCancel}
                width={490}
                footer={[
                    <Button key="cancel" onClick={handleCancel}>
                        取消
                    </Button>,
                    <Button key="submit" type="primary" onClick={handleOk}>
                        确定
                    </Button>,
                ]}
            >
                <Input
                    placeholder="请输入新增部分的名称"
                    value={sectionName}
                    onChange={(e) => setSectionName(e.target.value)}
                    onPressEnter={handleAddSection}
                    style={{ marginBottom: '20px' }}
                />
                <DragDropContext onDragEnd={handleDragEnd}>
                    <Droppable droppableId="sections">
                        {(provided: any) => (
                            <div {...provided.droppableProps} ref={provided.innerRef}>
                                {userDatas?.part?.map((item: any, index: any) => (
                                    <Draggable key={index} draggableId={String(index)} index={index}>
                                        {(provided: { innerRef: React.LegacyRef<HTMLElement> | undefined; draggableProps: JSX.IntrinsicAttributes & ListItemProps & React.RefAttributes<HTMLElement>; dragHandleProps: JSX.IntrinsicAttributes & ListItemProps & React.RefAttributes<HTMLElement>; }) => (
                                            <List.Item
                                                ref={provided.innerRef}
                                                {...provided.draggableProps}
                                                {...provided.dragHandleProps}
                                                className="lists"
                                                style={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    padding: '10px 0',
                                                    ...provided.draggableProps.style,
                                                }}
                                            >
                                                <Space size="middle">
                                                    <MenuOutlined style={{ cursor: 'pointer' }} />
                                                    {item.isEditing ? (
                                                        <Input
                                                            value={item.name}
                                                            onChange={(e) => handleEditChange(index, e.target.value)}
                                                            onPressEnter={() => handleSaveEdit(index, item.name)}
                                                            style={{ width: '200px' }}
                                                        />
                                                    ) : (
                                                        <span>{item.name}</span>
                                                    )}
                                                </Space>
                                                {item.isEditing ? (
                                                    <Button
                                                        type="link"
                                                        icon={<CheckOutlined />}
                                                        onClick={() => handleSaveEdit(index, item.name)}
                                                        style={{ marginLeft: 'auto' }}
                                                    />
                                                ) : (
                                                    <div>
                                                        <Button
                                                            type="link"
                                                            icon={<DeleteOutlined />}
                                                            onClick={() => handleDelete(item)}
                                                            style={{ marginLeft: 'auto' }}
                                                        />

                                                        <Button
                                                            type="link"
                                                            icon={<EditOutlined />}
                                                            onClick={() => handleEdit(index)}
                                                            style={{ marginLeft: 'auto' }}
                                                        />
                                                    </div>

                                                )}
                                            </List.Item>
                                        )}
                                    </Draggable>
                                ))}
                                {provided.placeholder}
                            </div>
                        )}
                    </Droppable>
                </DragDropContext>
            </Modal>

            <SelecttopicModal
                visible={topicVisible}
                title={'选择题目'}
                selectkeys={addType === 'radio' ? [currentReplace.current] : list}
                type={addType}
                itemName={itemName}
                userData={userDatas}
                callback={onOk}
                Switchitem={Switchitem}
                Replace={Replace}
                classifyID={classifyID}
                userID={userID}
                updateData={handleUpdateData}
                onclose={() => {
                    setTopicVisible(false)
                    setSwitchitem(null)
                }}
            />
            <CustomModal
                visible={CustomVisible}
                title={'答题时间设置(单位:分钟)'}
                userData={userDatas}
                // callback={onOk}
                callback={() => setTopCustom(false)}
                updateData={handleUpdateData}
                onclose={() => setTopCustom(false)}
            />


            <ScoreSettingModal
                visible={pointVisible}
                title={'分值设置'}
                tempData={userDatas}
                callback={pointFinish}
                onclose={() => setPointVisible(false)}
            />

            <ScoreSettingModaltwo
                visibles={pointVisible2}
                title={'得分设置 (设置总分后,同一部分的每道试题分值相同)'}
                userID={classifyID}
                userData={userDatas}
                updateData={handleUpdateData}
                callback={pointFinishdef}
                setup={setups}
                onclose={() => setPointVisible2(false)}
            />
            {/* 题目预览 */}
            <PreviewModal
                visible={previsible}
                detail={Previews}
                onClose={paperClose}
            />

            <ExamPreview
                visible={Examviewsvisible}
                title='试卷预览'
                detail={userDatas}
                onClose={paperClose}
            />
        </div>
    );
};

export default Tables;
