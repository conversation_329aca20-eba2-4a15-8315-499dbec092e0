.special_training_report_container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: auto;
  .tab_container {
    display: flex;
    width: 100%;
    border-radius: 6px;
    overflow: hidden;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #2a2a2a;
    background-color: #fff;
    cursor: pointer;
    // antd的tab
    .ant-tabs-tab-btn {
      width: 240px;
      text-align: center;
    }
    .ant-tabs-tab-active {
      background: var(--primary-color);
      .ant-tabs-tab-btn {
        color: #fff;
      }
    }
    .ant-tabs-ink-bar {
      visibility: hidden;
    }
    .ant-tabs-top > .ant-tabs-nav::before {
      border: none;
    }
    .ant-tabs-tab + .ant-tabs-tab {
      margin: 0;
    }
    .tab_item {
      width: 240px;
      height: 48px;
      line-height: 48px;
      text-align: center;
    }
    .tab_item_active {
      background: var(--primary-color);
      //   border-radius: 6px 0px 0px 6px;
      color: #fff;
    }
  }

  .list_container {
    width: 100%;
    // min-height: 912px;
    overflow: auto;
    height: calc(100vh - 108px);
    background: #ffffff;
    border-radius: 10px;
    margin-top: 20px;
    padding: 30px;

    .pagination_container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
