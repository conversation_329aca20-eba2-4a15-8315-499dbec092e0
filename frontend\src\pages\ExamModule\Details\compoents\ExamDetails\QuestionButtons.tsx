import React from 'react';
import { Button } from 'antd';

interface QuestionItem {
    name: string | number | boolean | React.ReactElement | Iterable<React.ReactNode> | React.ReactPortal | null | undefined;
    details: Array<{
        question: {
            questions_type: number;
            groupQuestions?: any[];
        };
        answerQuestion?: Array<{
            isRead: boolean;
        }>;
    }>;
}

interface QuestionButtonsProps {
    data: QuestionItem[];
    globalIndex: number;
    butncard: (detail: any, type: string) => void;
    butnQuestionsB: (detail: any, groupQuestion: any, index: number) => void;
}

const QuestionButtons = ({ data, globalIndex, butncard, butnQuestionsB }: QuestionButtonsProps) => {
    return (
        <>
            {data?.map((item, itemIndex) => (
                <div key={itemIndex}>
                    <div className='select_title'>
                        <div className='title_sz'>{item?.name}</div>
                    </div>
                    <div className='bt_flex'>
                        {item?.details?.flatMap((detail: any, detailIndex: any) => {
                            let currentIndex = globalIndex;

                            if (detail.question.questions_type !== 5) {
                                currentIndex = globalIndex++; // 类型不为 5 时，全局索引自增
                            }

                            const buttons = [];

                            // 类型不为 5 时生成按钮
                            if (detail.question.questions_type !== 5) {
                                buttons.push(
                                    <Button
                                        onClick={() => butncard(detail, 'read')}
                                        key={currentIndex}
                                        className={`bts ${!detail?.answerQuestion
                                            ? ""
                                            : detail?.answerQuestion?.[0]?.isRead
                                                ? "border-blue"
                                                : "border-red"
                                            }`}
                                    >
                                        <span className={detailIndex === currentIndex ? "blues" : ""}>
                                            {currentIndex}
                                        </span>
                                    </Button>
                                );
                            }

                            // 类型为 5 时处理 groupQuestions
                            if (detail.question.questions_type === 5) {
                                detail.question.groupQuestions?.forEach((groupQuestion: any, index: any) => {
                                    const groupIndex = globalIndex++; // groupQuestions 内部索引
                                    buttons.push(
                                        <Button
                                            onClick={() => butnQuestionsB(detail, groupQuestion, index)}
                                            key={groupIndex}
                                            className={`bts ${!detail?.answerQuestion ? '' : detail?.answerQuestion[0]?.isRead ? 'border-blue' : 'border-red'}`}
                                        >
                                            <span className={detailIndex === groupIndex ? "blues" : ""}>
                                                {groupIndex}
                                            </span>
                                        </Button>
                                    );
                                });
                            }

                            return buttons;
                        })}
                    </div>
                </div>
            ))}
        </>
    );
};

export default QuestionButtons;