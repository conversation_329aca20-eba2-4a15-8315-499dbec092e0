.Revise {


  .header {
    display: flex;
    width: 100%;
    padding: 10px 0;
    border-bottom: 1px solid #E3E3E3;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    z-index: 999;
    background-color: #ffffff;

    .comeback {
      padding-left: 34px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .fs {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #4A4F64;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }

  .pad {
    padding: 34px;
  }

  .boxs {

    display: flex;
    justify-content: space-between;
    margin-top: 40px;

    .boxLeft {
      display: flex;
      width: 50%;
      // justify-content: space-evenly;
      align-items: center;
      border-radius: 10px;
      border: 1px solid #D9D9D9;

      .CardsS {
        margin: 0 30px;
        background: rgba(84, 156, 255, 0.05);
        border-radius: 10px;
        // width: 50%;
        // height: 25%;
        position: relative;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;


        .lines {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 64px;
          height: 4px;
          border-radius: 2px;
          z-index: 2;
          background: #549cff;
        }
      }

      .ma20 {
        margin: 0 20px;
        width: 60%;
      }

      .left_pd {
        padding: 103px 20px;
        display: flex;
        width: 100%;
        /* 父容器宽度设置为100% */
        height: 100%;
        align-items: center;
      }

      .left_pd div {
        width: 33%;
        /* 每个 div 占据 33.33% 的宽度 */
        margin: 0 10px;
        text-align: center;
        // border: 1px solid #ccc;
      }

      .Cards {
        background: rgba(84, 156, 255, 0.05);
        border-radius: 10px;
        position: relative;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 140px;

        .line {
          position: absolute;
          top: 0;
          left: 43%;
          transform: translateX(-43%);
          width: 30%;
          height: 4px;
          border-radius: 2px;
          z-index: 2;
          background: #549cff;
        }
      }
    }

    .BarChart {
      // margin-left: 20px;
      width: 49%;
      border-radius: 10px;
      border: 1px solid #D9D9D9;
    }

    .Cards_zql {
      padding: 20px;
      font-size: 17px;
    }
  }
}
