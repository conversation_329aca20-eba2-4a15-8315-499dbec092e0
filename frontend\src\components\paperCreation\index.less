.paper_manage {
  .paper_create {
    .header {
      padding: 0 40px;
      width: 100%;
      height: 60px;
      background: #549cff10;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      .header_left {
        a {
          width: 32px;
          height: 22px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #919191;
          line-height: 22px;
          span:first-child {
            margin-right: 10px;
          }
          span:last-child {
            margin-right: 60px;
          }
        }
        > span {
          width: 60px;
          height: 28px;
          font-size: 20px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: var(--primary-color);
          line-height: 28px;
        }
      }
      .header_right {
        .ant-btn {
          border-radius: 4px;
        }
        // .ant-btn:first-child{
        //   margin-right: 20px;
        // }
      }
    }
    .content_ {
      height: calc(100vh - 60px);
      overflow-y: auto;
      .ant-form {
        padding: 40px 12%;
        .form_item {
          .form_item_header {
            position: relative;
            display: flex;
            align-items: center;
            .tag {
              margin-right: 10px;
              display: inline-block;
              width: 4px;
              height: 23px;
              background: var(--primary-color);
            }
            .span_left {
              display: flex;
              align-items: center;
            }
            .opt_box {
              position: absolute;
              right: 0;
              width: 30%;
              display: flex;
              justify-content: space-evenly;
              align-items: center;
              .addBlanks {
                margin-left: 20px;
              }
            }
          }
          .table_header {
            margin-top: 2%;
          }
          .form_item_body {
            display: flex;
            justify-content: space-between;
            padding: 20px 6px 0 16px;
            .left {
              flex: 1;
            }
            .right {
              width: 49%;
              margin-left: 1%;
              .img-box {
                width: 330px;
                height: 186px;
                background: #f7f9fa;
                border-radius: 4px;
                border: 1px solid #cbcbcb;
                margin-bottom: 1%;
                .ant-image {
                  width: 100%;
                  height: 100%;
                  .ant-image-img {
                    display: block;
                    width: 100%;
                    max-height: 100%;
                  }
                }
              }
              .tips {
                color: rgba(0, 0, 0, 0.45);
                font-size: 14px;
                line-height: 1.5715;
                transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
                padding-top: 0px;
                display: inline-block;
                margin-bottom: 1%;
              }
              .upload-buttons {
                > span {
                  margin-right: 5%;
                }
              }
            }
            .form_item_content {
              display: flex;
              justify-content: space-between;
            }
            .ant-row {
              display: flex;
              justify-content: space-between;
              .ant-form-item-label {
                width: 22%;
                text-align: right;
              }
            }
            .answer_container {
              width: 100%;
              .answer_list {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                .ant-input {
                  flex: 1;
                  margin: 0 10px;
                }
                .judge {
                  font-size: 14px;
                }
              }
            }
          }
          .form_item_body_table {
            margin-top: 2%;
            .ant-table-wrapper {
              .ant-table {
                .ant-table-tbody {
                  .ant-table-cell {
                    p {
                      margin-bottom: 0;
                    }
                    .spcialDom img {
                      max-width: 50px;
                      height: auto;
                      vertical-align: bottom;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.row-dragging {
  background: #fafafa;
  border: 1px solid #ccc;
}

.row-dragging td {
  padding: 16px;
}

.row-dragging .drag-visible {
  visibility: visible;
}
