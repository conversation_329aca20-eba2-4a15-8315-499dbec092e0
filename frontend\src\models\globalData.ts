// import { Effect, ImmerReducer, Reducer, Subscription } from 'umi';

import examManageApis from '@/api/exam';
export interface IGlobal {
  userInfo: {
    avatar: string;
    disabled: boolean;
    email: string;
    organizations: any;
    loginName: string;
    nickName: string;
    phone: string;
    userCode: string;
    roles: any[];
    extend: {
      [propsName: string]: string;
    };
  };
  showLoading: boolean;
  platform: string;
  menuShow: boolean;
  mobileFlag: boolean;
  rmanGlobalParameter: any[];
}
export default {
  namespace: 'globalData',
  subscriptions: {
    setup({ dispatch }: any) {
      dispatch({
        type: 'initMajor',
      });
    },
  },
  state: {
    major: [],
  },
  effects: {
    *initMajor({ payload }: { payload: any },{ call, put }: any) {
      //先初始化配置文件
      const {
        status,
        data,
      } = yield call(
        examManageApis.fetchMajorLists,
      );
      if (status === 200) {
        yield put({
          type: 'setGlobal',
          payload: data,
        });
      }
    },
  },
  reducers: {
    setGlobal: (
      state: any,
      { payload: data }: any,
    ) => {
      return {
        ...state,
        major: data,
      };
    },
  },
};
