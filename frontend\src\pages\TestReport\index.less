.test_report_container {
  width: 100vw;
  height: 100vh;
  background: #f7f9fa;

  .content_container {
    width: 100%;
    height: calc(100vh - 60px);
    display: flex;
    .left_menu_container {
      margin-top: 2px;
      width: 70px;
      height: calc(100% - 2px);
      background: #fff;
      padding-top: 13px;
      display: flex;
      flex-direction: column;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #2a2a2a;
      .menu_item {
        width: 70px;
        height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }

      .menu_item_active {
        background: var(--primary-color);
        // background-color: rgba(84, 156, 255, 0.1);
        color: #fff;
      }
    }

    .right_content_container {
      flex: 1;
      padding: 20px 205px;
      overflow: hidden;
    }
  }
}
