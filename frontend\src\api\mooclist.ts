import HTTP from './index';

// 新建mooc课程
export function nweMoocCourse(data: any) {
  return HTTP.post(`/learn/v1/teaching/course/create`, data)
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}

// 通过tpl创建课程
export function createCourseByTpl(data: any, templateId: string) {
  return HTTP.post(
    `/learn/v1/teaching/course/create/course/by/template?templateId=${templateId}`,
    data,
  )
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}

// 获取课程列表
export function getMoocList(data: string) {
  return HTTP.get(`/learn/v1/teaching/course/get/courses/list?${data}`)
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}

// 发布课程
export function releaseCourse(data: string[], courseType?: number) {
  return HTTP.post(
    `/learn/v1/teaching/course/publish?courseType=${courseType}`,
    data,
  )
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}

// 下架课程
export function offShelfCourse(data: string[]) {
  return HTTP.post(`/learn/v1/teaching/course/off/shelf`, data)
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}

// 删除
export function deleteTemplateCourse(data: string[]) {
  return HTTP(`/learn/v1/curriculum/center/delete/template`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}

export function deleteCourse(data: string[]) {
  return HTTP(`/learn/v1/teaching/course/delete`, {
    method: 'POST',
    data: JSON.stringify(data),
  })
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}

export function deleteResult(processid: string) {
  return HTTP.get(`/recycle/delete/process?processid=${processid}`)
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}

// 批量更新课程章节发布状态
export function updataCourse(courseId: string) {
  return HTTP.post(
    `/learn/v1/teaching/course/batch/update/publish/status/${courseId}/1`,
  )
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}

/**
 * 查询线下课程
 * @param params
 */
// export function getOfflineCourse(params: {
//   pageIndex: number;
//   pageSize: number;
// }) {
//   return HTTP(`/cvod/v1/teaching/course/get/offline/course`, {
//     method: 'POST',
//     data: JSON.stringify(params),
//   })
//     .then((res) => {
//       if (res.status === 200) {
//         return res.data;
//       }
//     })
//     .catch((error) => {
//       console.error(error);
//     });
// }

/**
  * 查询当前用户详情信息
  * @param cookie
  */
export function queryUserDetailInfo() {
  return HTTP(`/learn/v1/teaching/course/get/original/user/info`, {
    method: 'GET'
  })
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}
// export const queryUserDetailInfo = (cookie: string) =>
//   request<API.Response<any>>(
//     `${config.cvodService}/v1/teaching/course/get/original/user/info`,
//     {
//       method: 'GET',
//       headers: {
//         cookie,
//       },
//     }
//   );
// 查询课程门次
export function getCourse_floor(params: any) {
  return HTTP(`/ipingestman/schedule/course_floor/`, {
    method: 'GET',
    params,
  })
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}
// 查询当前学期
export function fetchSemeter() {
  return HTTP(`/unifiedplatform/v1/base/data/database/get/semester/current`, {
    method: 'GET'
  })
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}


export function getSpocCourse(params: any) {
  return HTTP(`/learn/v1/teaching/course/get/courses/list`, {
    method: 'GET',
    params,
  })
    .then((res) => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch((error) => {
      console.error(error);
    });
}
/**
   * 获取第三方课堂
   * @param params
   */
export function fetchOtherCourse(name: string, params: any) {
  return HTTP(
    `/learn/v1/course/dock/thridcourse/${name}`,
    {
      method: 'GET',
      params,
    },
  ).then((res) => {
    if (res.status === 200) {
      return res.data;
    }
  })
    .catch((error) => {
      console.error(error);
    });
}

// export const deleteResult = (processid: string) => {
//     return http<deleteTypes.IdeleteResult>(`/recycle/delete/process?processid=${processid}`)
// }
