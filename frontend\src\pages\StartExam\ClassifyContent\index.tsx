import examManageApis from '@/api/exam';
import { Pagination, Tabs } from 'antd';
import { FC, useEffect, useState } from 'react';
import { history } from 'umi';
import './index.less';

interface IContentProp {
  /** tablist */
  tabList: any[];
}

const ClassifyContent: FC<IContentProp> = ({ tabList }) => {
  // 选中的tab
  const [selectTab, setSelectTab] = useState<string>(tabList?.[0]?.id);
  // 分页
  const [page, setPage] = useState<number>(1);
  const [size, setSize] = useState<number>(10);
  const [total, setTotal] = useState<number>(0);
  //  测验列表
  const [testList, setTestList] = useState<any[]>([]);

  useEffect(() => {
    if (tabList?.length > 0) {
      setSelectTab(tabList?.[0]?.id);
      getTabExamList(tabList?.[0]?.id, page, size);
    }
  }, [tabList]);

  // 获取对应tab的list
  const getTabExamList = (id: string, page: number, size: number) => {
    examManageApis
      .fetchSpecialTraining({ page, size, classifyId: id })
      .then((res: any) => {
        if (res?.status == 200) {
          setTestList(res?.data?.data || []);
          setTotal(res?.data?.totalCount || 0);
        }
      });
  };

  // tab切换
  const handleChangeTab = (id: string) => {
    if (id !== selectTab) {
      setSelectTab(id);
      getTabExamList(id, 1, 10);
      setPage(1);
      setSize(10);
    }
  };
  // 分页变化
  const handlePageSizeChange = (page: number, size: number) => {
    getTabExamList(selectTab, page, size);
    setPage(page);
    setSize(size);
  };

  // 获取考试须知配置的JSON内容是否为空
  const getTestJSONContent = (value: string) => {
    const settingObj = JSON.parse(value);
    return !!settingObj?.address || !!settingObj?.stringValue;
  };

  // 去答题
  const handleToAnswer = (item: any, answerId?: string) => {
    if (answerId) {
      history.push(`/answerPage?testId=${item.id}&answerId=${answerId}`);
      return;
    }
    const hasGuid = getTestJSONContent(item?.guide);
    const hasNotice = getTestJSONContent(item?.notice);
    const hasRule = getTestJSONContent(item?.rule);
    // 没有配置考试须知的时候跳转
    if (hasGuid || hasNotice || hasRule) {
      history.push(`/examinstructions?testId=${item.id}`);
      return;
    }
    // 如果配置了考试说明
    if (item?.explainFile) {
      history.push(`/examrules?testId=${item.id}`);
      return
    }
    history.push(`/answerPage?testId=${item.id}`);
  };
  return (
    <div className="special_training_container">
      <div className="content">
        <div className="tab_container">
          <div style={{ overflow: 'hidden' }}>
            <Tabs
              activeKey={selectTab}
              tabPosition="top"
              style={{ height: 48 }}
              items={tabList.map((item, i) => {
                return {
                  label: item.name,
                  key: item.id,
                };
              })}
              onChange={handleChangeTab}
            />
          </div>
        </div>
        <div className="list_container">
          {testList.map((item) => {
            return (
              <div className="list_item" key={item.id}>
                <div className="info">
                  <div className="test_name">{item.name}</div>
                  <div className="test_desc">
                    <div className="test_diff">试题难度：{item.difficulty}</div>
                    <div className="test_count">
                      作答人数：
                      <span className="test_num">{item.submitQuantity}</span>
                    </div>
                  </div>
                </div>

                <div className="btns">
                  {item?.answerId ? (
                    <div style={{ display: 'flex' }}  >
                      <div
                        style={{ marginRight: '20px' }}
                        className="btn_item primary"
                        onClick={() => handleToAnswer(item)}
                      >
                        进入测验
                      </div>
                      {!!item?.answerId ? (
                        <div
                          className="btn_item default"
                          onClick={() => handleToAnswer(item, item?.answerId)}
                        >
                          继续答题
                        </div>
                      ) : (
                        <div
                          className="btn_item primary"
                          onClick={() => handleToAnswer(item)}
                        >
                          开始答题
                        </div>
                      )}
                    </div>
                  ) : (
                    <div
                      className="btn_item primary"
                      onClick={() => handleToAnswer(item)}
                    >
                      进入测验
                    </div>
                  )}


                </div>
              </div>
            );
          })}

          <div className="pagination_container">
            <Pagination
              current={page}
              pageSize={size}
              total={total}
              pageSizeOptions={[10, 20, 50, 100]}
              onChange={handlePageSizeChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClassifyContent;
