import dayjs from 'dayjs';
import { useEffect, useMemo, useRef, useState } from 'react';

export type TDate = dayjs.ConfigType;

export interface Options {
  leftTime?: number;
  //   targetDate?: TDate;
  interval?: number;
  id?: string;
  targetTime?: number;
  onEnd?: () => void;
  onTarget?: () => void;
}

export interface FormattedRes {
  days: number;
  hours: number;
  minutes: number | string;
  seconds: number | string;
  milliseconds: number;
}

const calcLeft = (target?: TDate) => {
  if (!target) {
    return 0;
  }
  // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
  const left = dayjs(target).valueOf() - Date.now();
  return left < 0 ? 0 : left;
};

const parseMs = (milliseconds: number): FormattedRes => {
  const minutes = Math.floor(milliseconds / 60000) % 60;
  const seconds = Math.floor(milliseconds / 1000) % 60;
  return {
    days: Math.floor(milliseconds / 86400000),
    hours: Math.floor(milliseconds / 3600000) % 24,
    minutes: minutes > 9 ? minutes : '0' + minutes,
    seconds: seconds > 9 ? seconds : '0' + seconds,
    milliseconds: Math.floor(milliseconds) % 1000,
  };
};

// 比较是否相等
const isEqual = (a: TDate, b: TDate) => {
  return dayjs(a).isSame(dayjs(b), 's');
};

const useCountdown = (options: Options = {}) => {
  const {
    leftTime,
    interval = 1000,
    id,
    targetTime,
    onTarget,
    onEnd,
  } = options || {};

  const memoLeftTime = useMemo<TDate>(() => {
    return typeof leftTime === 'number' && leftTime > 0
      ? Date.now() + leftTime
      : undefined;
  }, [leftTime, id]);

  const memoTargetTime = useMemo(() => {
    if (typeof targetTime === 'number' && targetTime > 0) {
      return targetTime;
    }
    return 0;
  }, [targetTime]);

  const target = memoLeftTime;

  const [timeLeft, setTimeLeft] = useState(() => calcLeft(target));

  const onEndRef = useRef(onEnd);

  const onTargetRef = useRef(onTarget);

  useEffect(() => {
    if (!target) {
      // for stop
      setTimeLeft(0);
      return;
    }

    // 立即执行一次
    setTimeLeft(calcLeft(target));

    const timer = setInterval(() => {
      const targetLeft = calcLeft(target);
      setTimeLeft(targetLeft);
      if (targetLeft === 0) {
        clearInterval(timer);
        onEndRef.current?.();
      }
      if (memoTargetTime > 0) {
        if (isEqual(targetLeft, memoTargetTime)) {
          // clearInterval(timer);
          onTargetRef.current?.();
        }
      }
    }, interval);

    return () => clearInterval(timer);
  }, [target, interval, memoTargetTime]);

  const formattedRes = useMemo(() => parseMs(timeLeft), [timeLeft]);

  return [timeLeft, formattedRes] as const;
};

export default useCountdown;
