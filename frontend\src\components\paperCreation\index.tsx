import paperManageApis from '@/api/paper';
import { IconFont } from '@/components';
import examType from '@/types/examType';
import { getSensitiveWordPost } from '@/utils';
import {
  LoadingOutlined,
  MenuOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Form,
  Image,
  Image as Img,
  Input,
  Popconfirm,
  Radio,
  Select,
  Space,
  Table,
  Upload,
  message,
} from 'antd';
import { arrayMoveImmutable } from 'array-move';
import React, { useEffect, useRef, useState } from 'react';
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
} from 'react-sortable-hoc';
import PointSettingModal from '../pointSetting';
import Preview from '../preview';
import PreviewPaper from '../previewPaper';
import RenderHtml from '../renderHtml';
import TeacherItem from '../select/teacherItem';
import TopicModal from '../topicModal';
import './index.less';
const { Option } = Select;
const { TextArea } = Input;

interface props {
  opt_type?: any;
  type?: any; //试题类型
  detail?: any; //试题详情
  selectKeys?: any; //初始分享人
}
const SortableItem = SortableElement((props: any) => <tr {...props} />);
const SortableBody = SortableContainer((props: any) => <tbody {...props} />);
const DragHandle = SortableHandle(() => (
  <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />
));
const PaperCreation: React.FC<props> = (props) => {
  // const location:any = useLocation();
  const {
    opt_type,
    type, //试题类型
    detail, //试卷号
    selectKeys, //
  } = props;
  const paperEum = examType.paperType; //题目类型
  const defaultCover = '/exam/static/images/papercover.png';
  const [form] = Form.useForm();
  const [current_, setCurrent_] = useState<any>(undefined); //题目类型
  const [options, setOptions] = useState<any[]>([]); //选项内容
  const [keyword, setKeyword] = useState<any>([]);
  const [list, setList] = useState<any>([]); //试卷题目列表
  const [page, setPage] = useState<number>(1);
  const [totalPage, setTotalPage] = useState<number>(1);
  const [score, setScore] = useState<any>(0);
  const [topicVisible, setTopicVisible] = useState<boolean>(false); //试题弹框
  const [pointVisible, setPointVisible] = useState<boolean>(false); //分值设置
  const [cover, setCover] = useState(defaultCover);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [previsible, setPrevisible] = useState<boolean>(false);
  const [paperPrevisible, setPaperPreviewVisible] = useState<boolean>(false);
  const [preObject, setPreObject] = useState<any>(undefined);
  const [currentId, setCurrentId] = useState<any>(undefined);
  const [addType, setAddType] = useState<string>('checkbox');
  const currentReplace = useRef<any>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const restoreDefault = () => {
    setCover(defaultCover);
  };
  useEffect(() => {
    form.setFieldsValue({
      paper_cover: cover,
    });
  }, [cover]);
  useEffect(() => {
    // 统计分数
    countPoints(list);
    setCurrent_({
      ...current_,
      questions_details: list,
    });
  }, [list]);
  useEffect(() => {
    //查询试卷详情
    if (detail) {
      fetchDetail();
    }
  }, [detail]);
  useEffect(() => {
    //组卷
    if (opt_type === 'grouppaper') {
      const list: any = JSON.parse(
        window.sessionStorage.getItem('tempTopics') as any,
      );
      console.log(list,'listlistlist');

      setList(list);
    }
  }, [opt_type]);
  const type_: any = examType.optionType_;

  const fetchDetail = async () => {
    const res = await paperManageApis.paperDetail(detail);
    if (res.status === 200) {
      form.setFieldsValue({
        ...res.data,
      });
      setScore(res.data.paper_marks);
      setCover(res.data.paper_cover);
      setList(res.data.questions_details);
      setCurrent_(res.data);
    }
  };
  const replace = (item: any) => {
    setAddType('radio');
    currentReplace.current = item; //暂存需要替换的题
    setTopicVisible(true);
  };
  const countPoints = (item: any) => {
    if (item.length > 0) {
      item.forEach((element: any, index: number) => { });
    } else {
    }
  };
  const batchdelete = (item: any) => {
    const temp = JSON.parse(JSON.stringify(list));
    temp.forEach((item_: any, index: number) => {
      if (item.parent_id) {
        if (item.parent_id === item_.parent_id) {
          temp.splice(index, 1);
        }
      } else {
        if (item.id === item_.id) {
          temp.splice(index, 1);
        }
      }
    });
    setList(temp);
  };
  const save = async (flag: number) => {
    setLoading(true);
    //名字必填
    form.validateFields().then(async (value: any) => {
      let flag_ = true;
      let param = {
        ...value,
        questions: list?.map((item: any) => {
          return {
            questions_id: item.parent_id || item.id,
            // parent_id: item.parent_id,
            questions_type: item.questions_type,
            question_mark: item.question_mark ?? 0,
          };
        }),
        state: flag,
        random: 0,
      };
      //暂时不对分值校验
      // if (!flag_) {
      //   message.info('请设置有效的分值');
      //   return
      // }
      if (opt_type === 'new' || opt_type === 'grouppaper') {
        if (!currentId) {
          const res: any = await getSensitiveWordPost(
            param.paper_name,
            '内容',
            () => paperManageApis.addPaper(param),
            () => {
              setLoading(false);
            },
          );
          if (!res) return;
          if (res.status === 200) {
            setCurrentId(res.data.id);
            message.success('新建成功');
          }
          setLoading(false);
        } else {
          const res: any = await getSensitiveWordPost(
            param.paper_name,
            '内容',
            () => paperManageApis.paperUpdate(currentId, param),
            () => {
              setLoading(false);
            },
          );
          if (!res) return;
          if (res.status === 200) {
            message.success('保存成功');
          }
          setLoading(false);
        }
      } else if (opt_type === 'edit') {
        const res: any = await getSensitiveWordPost(
          param.paper_name,
          '内容',
          () => paperManageApis.paperUpdate(detail, param),
          () => {
            setLoading(false);
          },
        );
        if (!res) return;
        if (res.status === 200) {
          message.success('保存成功');
        }
        setLoading(false);
      } else {
        //复制
      }
    });
  };
  const copy = (item: any) => {
    console.log(item);
  };
  const columns: any = [
    {
      title: '',
      width: '5%',
      dataIndex: 'sort',
      className: 'drag-visible',
      render: () => <DragHandle />,
    },
    {
      title: '序号',
      width: '8%',
      ellipsis: true,
      render: (value: any, record: any, index: number) => index + 1,
    },
    {
      title: '类型',
      width: '8%',
      dataIndex: 'questions_type',
      key: 'questions_type',
      ellipsis: true,
      render: (item: any, record: any) => (
        <div>{examType.optionType_[Number(item)]}</div>
      ),
    },
    {
      title: '题目',
      // width: '15%',
      dataIndex: 'questions_content',
      key: 'questions_content',
      ellipsis: true,
      render: (value: any) => (
        <RenderHtml cname="spcialDom" value={value}></RenderHtml>
      ),
    },
    {
      title: '答案',
      // width: '5%',
      dataIndex: 'questions_answers',
      key: 'questions_answers',
      ellipsis: true,
      render: (value: any, record: any) => (
        <div>
          {value?.map((cell: any) => (
            <RenderHtml cname="spcialDom" value={cell}></RenderHtml>
          ))}
        </div>
      ),
    },
    {
      title: '分值',
      width: '8%',
      dataIndex: 'question_mark',
      key: 'question_mark',
      ellipsis: true,
      render: (value: any, record: any) => <div>{value}</div>,
    },
    {
      title: '操作',
      key: '操作',
      dataIndex: 'editable',
      // width: '8%',
      align: 'center',
      render: (disable: any, record: any) => (
        <div className="table_opt">
          <Button type="link" onClick={() => preview(record)} title="预览">
            <IconFont type="iconyulan" />
          </Button>
          {opt_type !== 'copy' && (
            <>
              <Button type="link" onClick={() => replace(record)} title="替换">
                <IconFont type="iconshuaxin" />
              </Button>
            </>
          )}
          {
            <Popconfirm
              title="确定要删除吗?"
              onConfirm={() => batchdelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" title="删除">
                <IconFont type="iconshanchu-heise-copy" />
              </Button>
            </Popconfirm>
          }
        </div>
      ),
    },
  ];
  const onSortEnd = ({ oldIndex, newIndex }: any) => {
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable(
        list.slice(),
        oldIndex,
        newIndex,
      ).filter((el: any) => !!el);

      setList(newData);
    }
  };
  const DraggableContainer = (props: any) => (
    <SortableBody
      useDragHandle
      disableAutoscroll
      helperClass="row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  );

  const DraggableBodyRow: React.FC<any> = ({
    className,
    style,
    ...restProps
  }) => {
    // function findIndex base on Table rowKey props and should always be a right array index
    const index = list?.findIndex(
      (x: any) => x.id === restProps['data-row-key'],
    );
    return <SortableItem index={index} {...restProps} />;
    //  return new SortableItem(restProps);
  };
  //答案选项
  const answerChange = (e: any, item?: any) => {
    console.log('答案选项', e);
  };
  const compressImage = (file: any, success: any) => {
    // 图片小于2M不压缩
    if (file.size < Math.pow(1024, 2) * 2) {
      return success(file);
    }
    const name = file.name; //文件名
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (e: any) => {
      const src = e.target.result;
      const img = new Image();
      img.src = src;
      img.onload = (e: any) => {
        const w = img.width;
        const h = img.height;
        const quality = ((Math.pow(1024, 2) * 2) / file.size).toFixed(1); // 默认图片质量为0.92
        // 生成canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        // 创建属性节点
        const anw = document.createAttribute('width');
        anw.nodeValue = w.toString();
        const anh = document.createAttribute('height');
        anh.nodeValue = h.toString();
        canvas.setAttributeNode(anw);
        canvas.setAttributeNode(anh);

        // 铺底色 PNG转JPEG时透明区域会变黑色
        (ctx as CanvasRenderingContext2D).fillStyle = '#fff';
        (ctx as CanvasRenderingContext2D).fillRect(0, 0, w, h);

        (ctx as CanvasRenderingContext2D).drawImage(img, 0, 0, w, h);
        // quality值越小，所绘制出的图像越模糊
        const base64 = canvas.toDataURL('image/jpeg', quality); // 图片格式jpeg或webp可以选0-1质量区间

        // 返回base64转blob的值
        // 去掉url的头，并转换为byte
        const bytes = window.atob(base64.split(',')[1]);
        // 处理异常,将ascii码小于0的转换为大于0
        const ab = new ArrayBuffer(bytes.length);
        const ia = new Uint8Array(ab);
        for (let i = 0; i < bytes.length; i++) {
          ia[i] = bytes.charCodeAt(i);
        }
        file = new Blob([ab], { type: 'image/jpeg' });
        file.name = name;
        success(file);
      };
    };
  };
  const beforeUpload = (file: any) => {
    return new Promise((resolve) => {
      compressImage(file, (newFile: any) => {
        resolve(newFile);
      });
    });
  };
  const onChange = (info: any) => {
    setUploadLoading(true);
    if (info.file.status !== 'uploading') {
    }
    if (info.file.status === 'done') {
      setUploadLoading(false);
      if (info.file.response.message === 'OK') {
        setCover(info.file.response.data.httpPath);
        message.success(`封面上传成功！`);
      } else {
        message.error(info.file.response.message);
      }
    } else if (info.file.status === 'error') {
      setUploadLoading(false);
      message.error(`封面上传失败！`);
    }
  };

  const addTopic_ = (info: any) => {
    setAddType('checkbox');
    setTopicVisible(true);
  };
  const pointSetting = (info: any) => {
    setPointVisible(true);
  };
  const pointFinish = (data: any) => {
    let count = 0;
    const temp = JSON.parse(JSON.stringify(list));
    data?.forEach((item: any) => {
      item.children.forEach((item_: any) => {
        count += Number(item_.points);
        for (let i = 0; i < temp.length; i++) {
          if (item_.id === temp[i].id) {
            temp[i].question_mark = Number(item_.points);
            break;
          }
        }
      });
    });

    setScore(count);
    setList(temp);
    setPointVisible(false);
  };
  const onOk = (data: any) => {
    const newData = data.map((item: any) => ({ ...item, editable: false }));

    if (addType === 'radio') {
      //替换题目
      const tmep = JSON.parse(JSON.stringify(list));
      const index = tmep.findIndex(
        (x: any) => x.id === currentReplace.current.id,
      );
      tmep[index] = newData[0];
      setList(tmep);
    } else {
      //保留原数据分值
      const tmep = JSON.parse(JSON.stringify(list));
      newData.forEach((item_: any, index: number) => {
        //已保存过的数据的parent_id与之对应 新勾选的id与之对应
        if (
          tmep.findIndex(
            (x: any) => x.id === item_.id || x.parent_id === item_.id,
          ) === -1
        ) {
          tmep.push(item_);
        }
      });
      setList(tmep);
    }
    setTopicVisible(false);
  };
  const preview = (item: any) => {
    setPrevisible(true);
    setPreObject(item);
  };
  const previewpaper = () => {
    setPaperPreviewVisible(true);
  };
  const onClose = () => {
    setPrevisible(false);
  };
  const paperClose = () => {
    setPaperPreviewVisible(false);
  };

  const handleBack = () => {
    history.back();
  };
  return (
    <div className="paper_create">
      <div className="header">
        <div className="header_left">
          <a onClick={handleBack}>
            <span>{'<'}</span>
            <span>返回</span>
          </a>
          <span>
            {opt_type === 'new' || opt_type === 'grouppaper'
              ? '新建试卷'
              : current_?.paper_name}
          </span>
        </div>
        <div className="header_right">
          {opt_type === 'new' || opt_type === 'grouppaper' ? (
            <Space>
              <Button type="primary" onClick={() => save(1)} loading={loading}>
                保存
              </Button>
              <Button onClick={handleBack}>关闭</Button>
            </Space>
          ) : opt_type === 'edit' ? (
            <Space>
              <Button type="primary" onClick={() => save(1)} loading={loading}>
                保存
              </Button>
              <Button onClick={handleBack}>关闭</Button>
            </Space>
          ) : (
            //复制
            <Space>
              <Button type="primary" onClick={copy}>
                复制
              </Button>
              <Button onClick={handleBack}>关闭</Button>
            </Space>
          )}
        </div>
      </div>
      <div className="content_">
        <Form
          name="paper_form"
          form={form}
          style={{ width: '100%', height: '100%' }}
          labelAlign="left"
        >
          <div className="form_item">
            <div className="form_item_header">
              <span className="tag"></span>
              <span>试卷信息</span>
            </div>
            <div className="form_item_body">
              <div className="left">
                <Form.Item
                  name={'paper_name'}
                  label={'试卷名称'}
                  rules={[{ required: true, message: '请输入试卷名称' }]}
                >
                  <Input />
                </Form.Item>
                <Form.Item name={'paper_type'} label="试卷类型">
                  <Select>
                    {paperEum.map((item: any, index: number) => (
                      <Select.Option value={index} key={item + index}>
                        {item}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item name={'paper_difficulty'} label="试卷难度">
                  <Select>
                    <Option value={1}>1</Option>
                    <Option value={2}>2</Option>
                    <Option value={3}>3</Option>
                    <Option value={4}>4</Option>
                    <Option value={5}>5</Option>
                  </Select>
                </Form.Item>
                <Form.Item name={'paper_option'} label="多选题得分设置:">
                  <Radio.Group onChange={answerChange} defaultValue={0}>
                    <Radio value={0}>全部得分</Radio>
                    <Radio value={1}>部分正确得分</Radio>
                  </Radio.Group>
                </Form.Item>
                <TeacherItem
                  multiple={true}
                  required={false}
                  message={''}
                  label={'分享给'}
                  name={'share_users'}
                  key="teacher1"
                />
              </div>
              <div className="right">
                <Form.Item name={'paper_cover'} label="试卷封面">
                  <div className="img-box">
                    <Img src={cover} />
                  </div>
                  <span className="tips">
                    建议图片比例16:9,支持jpg、jpeg、png，图片大小不超过2M
                  </span>
                  <div className="upload-buttons">
                    <Upload
                      accept=".png,.jpg,.jpeg"
                      action="/learn/v1/upload/fileupload"
                      showUploadList={false}
                      beforeUpload={beforeUpload as any}
                      onChange={onChange}
                    >
                      <Button icon={uploadLoading ? <LoadingOutlined /> : ''}>
                        选择图片
                      </Button>
                    </Upload>
                    <Button type="primary" onClick={restoreDefault}>
                      恢复默认
                    </Button>
                  </div>
                </Form.Item>
              </div>
            </div>
          </div>
          <div className="form_item">
            <div className="form_item_header table_header">
              <div className="span_left">
                <span className="tag"></span>
                <span>试卷题目</span>
              </div>
              <div className="opt_box">
                <span className="score">{`总分：${score}`}</span>
                <Button type="primary" onClick={addTopic_}>
                  <PlusCircleOutlined />
                  添加题目
                </Button>
                <Button onClick={pointSetting} disabled={list?.length === 0}>
                  分值设置
                </Button>
                <Button onClick={previewpaper} disabled={list?.length === 0}>
                  预览
                </Button>
              </div>
            </div>
            <div className="form_item_body_table">
              <Table
                rowKey="id"
                columns={columns}
                dataSource={list}
                pagination={false}
                components={{
                  body: {
                    wrapper: DraggableContainer,
                    row: DraggableBodyRow,
                  },
                }}
              ></Table>
            </div>
          </div>
        </Form>
      </div>
      <TopicModal
        visible={topicVisible}
        title={'选择题目'}
        selectkeys={addType === 'radio' ? [currentReplace.current] : list}
        type={addType}
        callback={onOk}
        onclose={() => setTopicVisible(false)}
      />
      <PointSettingModal
        visible={pointVisible}
        title={'分值设置'}
        tempData={list}
        callback={pointFinish}
        onclose={() => setPointVisible(false)}
      />
      <Preview visible={previsible} detail={preObject} onClose={onClose} />
      <PreviewPaper
        visible={paperPrevisible}
        detail={current_}
        onClose={paperClose}
      />
    </div>
  );
};
export default PaperCreation;
