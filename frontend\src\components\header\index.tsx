import themeService from "@/api/theme";
import MessageBox from "@/components/Message";
import { IconFont } from "@/components/iconFont/iconFont";
import { IConfig } from '@/models/config';
import { IPermission } from '@/models/permission';
import { ModuleCfg } from '@/permission/globalParams';
import { useDispatch, useSelector } from '@@/plugin-dva/exports';
import { Badge, Dropdown, Image, Menu, Popover } from 'antd';
import { FC, useEffect, useState } from 'react';
import './index.less';
interface HeaderProps {
  subtitle?: string;
  // navList?: LinkItem[];
  showNav: boolean;
  user_Info:any;
  navActive?: string;
  ifBack?: boolean;
  showOther?: boolean;
}
interface UserAvatarProps {
  username: string;
  avatar: string;
  work: boolean;
  // teacher: boolean;
  student: boolean;
  admin: boolean;
  // rman: boolean;
  // joveone: boolean;
  workUrl: string;
  personal: boolean;
  onLogout: () => void;
}

const UserAvatar: FC<UserAvatarProps> = ({
  username,
  avatar,
  work,
  // teacher,
  student,
  admin,
  personal,
  // rman,
  // joveone,
  workUrl,
  onLogout,
}) => {
  // let workUrl = ""
  // let target = ""
  // if (teacher) {
  //   workUrl = "/learn/workbench/#/course"
  //   target = "my_teaching"
  // } else if (rman) {
  //   workUrl = "/rman/#/basic/rmanCenterList"
  //   target = "source_manage"
  // } else if (admin) {
  //   workUrl = "/unifiedplatform/#/basic"
  //   target = "sys_manage"
  // } else if (joveone) {
  //   workUrl = "/joveone"
  //   target = "joveone"
  // }
  const menu = (
    <Menu>
      {work && (
        <Menu.Item>
          <a href={workUrl} target="work">工作台</a>
        </Menu.Item>
      )}
      {student && (
        <Menu.Item>
          <a href="/unifiedplatform/#/learn/mycourse" target="my_study">我的学习</a>
        </Menu.Item>
      )}
      {personal && (
        <Menu.Item>
          {/* <a href="/unifiedplatform/#/personal/info" target="personal_center">账号管理</a> */}
          <a href="/unifiedplatform/#/personal/home" target="personal_center">个人中心</a>
        </Menu.Item>
      )}
      {admin && (
        <Menu.Item>
          {/* <a href="/unifiedplatform/#/personal/info" target="personal_center">账号管理</a> */}
          <a href="/unifiedplatform/#/management" target="source_manage">管理中心</a>
        </Menu.Item>
      )}
      <Menu.Item onClick={onLogout}>退出登录</Menu.Item>
    </Menu>
  );

  const microMajorMenu = <Menu><Menu.Item onClick={onLogout}>退出登录</Menu.Item></Menu>

  const getMenu = () => {
    if (location.hash.includes('micromajor')) {
      return microMajorMenu
    }
    return menu
  }

  return (
    <Dropdown overlay={()=>getMenu()} className="user-avatar-wrapper">
      <div>
        <Image
          src={avatar || require('@/images/login/default-avatar.png')}
          fallback={require('@/images/login/default-avatar.png')}
          preview={false}
        />
        <p className="user-name">{username}</p>
      </div>
    </Dropdown>
  );
};

const Header: FC<HeaderProps> = ({
  subtitle,
  // navList,
  showNav,
  user_Info,
  navActive,
  ifBack = true,
  showOther = true
}) => {
  const { title, logoUrl, isShow } = useSelector<any, any>(
    state => state.themes,
  );
  const [headerList, setHeaderList] = useState<any[]>([]);
  const { modules, permissions,rmanGlobalParameter } = useSelector<{ permission: any }, IPermission>(
    ({ permission }) => permission,
  );
  const  configs:any  = useSelector<{ config: any }, IConfig>(
    ({config})=>config
 );
  const [unReadCount, setUnreadCount] = useState<number>(0);
  const handleLogout = async () => {
    window.location.href = `/unifiedlogin/v1/loginmanage/loginout/go`;
    // let res = await api.user.logout();
    // if (res && res.errorCode === 'success') {
    //   message.success('注销成功');
    //   // history.push('/basic?action=login');
    //   // window.location.replace(`/cvodweb`);
    // }
  };
  const dispatch = useDispatch();
  useEffect(() => {
    getUnreadCount()
    const timer = setInterval(getUnreadCount, 10000)
    return () => {
      clearInterval(timer)
    }
  }, [])

  const getUnreadCount = () => {
    themeService.reqUnreadMessageCount().then((res: any) => {
      if (res?.errorCode === 'success') {
        setUnreadCount(res.extendMessage);
      }
    });
  };
  const leftMenuChange = ()=>{
    dispatch({
      type:'config/updateState',
      payload:{
        leftMenuExpand:!configs.leftMenuExpand
      }
    })
  }

  const getLogHef = () => {
    let href = ""
    if (location.hash.includes('micromajor')) {
      href = '/learn/search/microMajor'
    }
    else {
      href =(process.env.NODE_ENV === 'development'
      ? 'http://172.16.151.202'
      : '') + '/unifiedplatform/v1/app/home/<USER>'
    }
    return href
  }

  const getLogTitle = () => {
    let name: any = ''
    if (location.hash.includes('micromajor')) {
      name = '微专业平台'
    }
    else {
      name = isShow ? "工作台" : null
    }
    return name
  }

  return (
    <div className="uf-header-wrapper">
      <div className="uf-header-left-part">
      {configs.mobileFlag && <IconFont onClick={leftMenuChange} type='iconwenzhangpailie2-221'/>}
        <a
          href={getLogHef()}
          className="home-part"
        >
          <img className="icon-home" src={require('@/images/icons/home.png')} />
          {/* {ifBack && (
            <div className="go-back-btn">
              <IconFont type="iconjiantouda" />
            </div>
          )} */}
          <div className='icon_box'>
            <img
              src={logoUrl || require('@/images/login/default_logo.png')}
              className="uf-header-icon"
            />
          </div>
          <h2>{getLogTitle()}</h2>
        </a>
        {subtitle && (
          <>
            {/* <Divider className="vertical-line" type="vertical" /> */}
            <h4 style={{marginLeft:'26px'}}>|</h4>
            <h4>{subtitle}</h4>
          </>
        )}
      </div>
      <div className="uf-header-right-part">
        {/* {showNav && <Nav list={headerList} active={navActive} />} */}
        {/* {showOther ? <OtherNav list={[
          {
            key: 'joveone',
            name: '在线剪辑',
            href: '/joveone',
            target: "joveone",
            disabled:
              !modules.includes(ModuleCfg.jove),
          },
          {
            key: 'textclip',
            name: '语音剪辑',
            href: '/textclip/#/clip/myTextClip',
            target: "textclip",
            disabled:!modules.includes(ModuleCfg.textclip),
          }
        ]} /> : ''} */}
        <Popover overlayClassName='message-popover' destroyTooltipOnHide={true} content={<MessageBox readChange={getUnreadCount} />}>
          <Badge count={unReadCount} offset={[5, 0]}>
            <div className='message-container'><IconFont type="iconxiaoxitongzhi" /></div>
          </Badge>
        </Popover>
        <UserAvatar
          username={user_Info?.nickName}
          avatar={user_Info?.avatar}
          workUrl={'/learn/workbench/#/home'}
          work={modules.includes(ModuleCfg.work)}
          // joveone={modules.includes(ModuleCfg.jove) && permissions.includes(perCfg.jove_use)}
          // teacher={modules.includes(ModuleCfg.teacher)}
          student={modules.includes(ModuleCfg.student)}
          admin={modules.includes(ModuleCfg.manager)}
          // rman={
          //   modules.includes(ModuleCfg.rman) &&
          //   permissions.includes(perCfg.show_resource_management)
          // }
          personal={modules.includes(ModuleCfg.personal)}
          onLogout={handleLogout}
        />
      </div>
    </div>
  );
};
export default Header;
