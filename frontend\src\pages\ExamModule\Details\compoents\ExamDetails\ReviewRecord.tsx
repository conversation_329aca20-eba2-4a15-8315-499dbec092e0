import React from 'react';
import { Image, Empty } from 'antd';
import { PictureOutlined, NotificationOutlined, DeleteOutlined } from '@ant-design/icons';
import { formatDate } from '@/utils';

interface ReviewRecordProps {
    Cicismlist: {
        id: string;
        teacherHeadUri: string;
        teacherName: string;
        updateDate: string;
        createUserId: string;
        text: string;
        content: string;
    }[];
    RecordSelection: string;
    ondelete: (type: 'Picture' | 'Audio' | 'Delete', item: any) => void;
    butncard: (item: any, type: string) => void;
    userinfo: {
        userCode: string;
    };
}

const ReviewRecord = ({ Cicismlist, RecordSelection, ondelete, butncard, userinfo }: ReviewRecordProps) => {
    return (
        <div className='card' style={{ height: '86vh' }}>
            <div style={{ margin: '7px' }} >
                批阅记录
            </div>
            {Cicismlist.length > 0 ? (
                Cicismlist?.map((item) => (
                    <div style={{
                        border: RecordSelection === item?.id ? '1px solid #549CFF' : '1px solid transparent',
                        borderRadius: '4px', // 可选，确保边框圆角更美观
                        padding: '8px', // 可选，增加内容与边框的间距
                    }}
                        onClick={() => butncard(item, 'record')} className="Record" key={item.id}>

                        <div>
                            <div className="Record_box">
                                <div className="box_title">
                                    <Image preview={false} src={item.teacherHeadUri} />
                                    <div className="title">
                                        <div>{item.teacherName}</div>
                                        <div>
                                            {formatDate(item.updateDate)}
                                        </div>
                                    </div>
                                </div>
                                <div className="box_icon">
                                    {item?.fileUri?.some((uri: string) => /\.(jpg|jpeg|png|gif|bmp)$/i.test(uri)) && (
                                        <PictureOutlined onClick={() => ondelete('Picture', item)} style={{ marginRight: "10px", color: "#525252" }} />
                                    )}
                                    {item?.fileUri?.some((uri: string) => /\.(mp3|wav|ogg|flac)$/i.test(uri)) && (
                                        <NotificationOutlined onClick={() => ondelete('Audio', item)} style={{ marginRight: "10px", color: "#525252" }} />
                                    )}
                                    {
                                        item.createUserId === userinfo.userCode && (
                                            <DeleteOutlined onClick={() => ondelete('Delete', item)} />
                                        )
                                    }
                                </div>
                            </div>
                            <div className="box_nr">{item.text}</div>
                            <div className="box_nr">{item.content}</div>
                            {/* fileUri 是给数组里面是图片  */}
                            <div className="box_nr" >
                                {item?.fileUri?.map((uri: string) => {
                                    const fileName = uri.split('/').pop();
                                    // 检查是否为音频文件
                                    const isAudioFile = fileName?.toLowerCase().endsWith('.mp3') ||
                                        fileName?.toLowerCase().endsWith('.wav') ||
                                        fileName?.toLowerCase().endsWith('.ogg');

                                    // 如果是音频文件则不显示
                                    if (isAudioFile) {
                                        return null;
                                    }

                                    return (
                                        <div key={uri} style={{ cursor: 'pointer' }} onClick={() => window.open(uri)}>
                                            文件名称: <span style={{ color: '#559cff' }}>{fileName}</span>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                ))
            ) : (
                <Empty />
            )}
        </div>
    );
};

export default ReviewRecord;