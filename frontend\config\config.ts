import { defineConfig } from 'umi';
import routes from './config.routes';
import proxy from './config.proxy';
// const CompressionPlugin = require("compression-webpack-plugin");
// const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i;

export default defineConfig({
  nodeModulesTransform: {
    // node_modules 编译模式
    type: 'none',
    exclude: [],
  },
  publicPath: process.env.NODE_ENV == 'development' ? './' :'/exam/',
  history: {
    // 路由模式
    type: 'hash',
  },
  // layout: {},
  antd: {
    dark: false,
    compact: false,
  },
  routes: routes, // 路由文件
  locale: {
    // 国际化配置
    default: 'zh-CN',
    antd: true,
    title: true,
    baseNavigator: false, // 默认true会去localstorage读umi_locale
  },
  dva: {
    // dva配置
    immer: true,
    hmr: false,
  },
  // mfsu:{
  //   production: { output: '.mfsu-production' } 
  // },
  proxy: proxy,
  // hash: true, // 打包文件生成hash
  // publicPath: './', // 打包路径
  // forkTSChecker: {}, // ts语法检查
  // devtool: "eval", // 生成map文件
  // theme: defaultTheme,
  dynamicImport: {
    loading: '@/components/loading/loading',
  },
  // favicon: '/rman/static/favicon.ico',
  links: [
    // 样式库
    {
      rel: 'stylesheet',
      type: 'text/css',
      href: '//at.alicdn.com/t/font_2019002_uq6va50rnq.css',
    },
  ],
  scripts: [
    // {
    //   type: 'text/javascript',
    //   src: '/rman/libs/mam-timecode-convert/dist/mam-timecode-convert.js',
    // },
  ],
  targets: {
    ie: 10,
  },
  define: {
    SAFE_MODE: true,
  },
  chunks: process.env.NODE_ENV === 'production' ? ['vendors', 'umi']:false,
  chainWebpack(config:any) {
    if (process.env.NODE_ENV === 'production') {
      config.output.filename('[name].[hash].js'); //添加hash值解决缓存
      config.output.chunkFilename('[name].[hash].js');
      config.merge({
        optimization: {
          minimize: true,
          splitChunks: {
            chunks: 'async',
            minSize: 30000,
            minChunks: 2,
            automaticNameDelimiter: '.',
            cacheGroups: {
              vendor: {
                name: 'vendors',
                test: /^.*node_modules[\\/](?!lodash|react-virtualized|antd).*$/,
                chunks: "all",
                priority: 10,
              },
              antd: {
                name: "antd",
                test: /[\\/]node_modules[\\/]antd[\\/]/,
                chunks: "all",
                priority: 9
              },
              lodash: {
                  name: "lodash",
                  test: /[\\/]node_modules[\\/]lodash[\\/]/,
                  chunks: "all",
                  priority: -2
              },
            }
          }
        }
      });
      //过滤掉momnet的那些不使用的国际化文件
      config.plugin("replace")?.use(require("webpack")?.ContextReplacementPlugin)?.tap(() => {
        return [/moment[/\\]locale$/, /zh-cn/];
      });
    } else {
      // 为开发环境修改配置...
    }
  }
});
