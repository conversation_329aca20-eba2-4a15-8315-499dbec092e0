import HTTP from './index'

namespace bannerApis {
    export function getBanner() {
        return HTTP.get(`/learn/v1/configure/data/get/banner/config`).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function insertBanner(data: Banner.IInsertBanner) {
        return HTTP.post(`/learn/v1/configure/data/insert/banner/config`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function updateBanner(data: Banner.IInsertBanner) {
        return HTTP.post(`/learn/v1/configure/data/update/banner/config`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function deleteBanner(data: string[]) {
        return HTTP.post(`/learn/v1/configure/data/batch/delete/banner/config`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }
    export function updateReleaseBanner(release_status: number, data: string[]) {
        return HTTP.post(`/learn/v1/configure/data/batch/update/banner/release/status?release_status=${release_status}`, data).then(res => {
            if (res.status === 200) {
                return res.data
            }
        }).catch(error => {
            return error
        })
    }

}

export default bannerApis;
