namespace deleteTypes {
    export interface IdeleteResource {
        process_id:string
    }
    export interface Ifailedlist {
        name: string
        reason: string
        resourceId: string
        type: string
        key?:string
    }
    export interface IdeleteResult {
        check_use_time: number
        failed_count: number
        failed_list: Ifailedlist[]
        finish_time: string
        id: string
        running_use_time: number
        start_time: string
        status: string
        success_count: number
        total_count: number
    }
}

export default deleteTypes