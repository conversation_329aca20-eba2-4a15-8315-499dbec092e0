.Detailspage {


  .header {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #E3E3E3;
    justify-content: space-between;
    align-items: center;

    .comeback {
      padding-left: 34px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .fs {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #4A4F64;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }

  .car {
    background: #F6FBFF;
    border-radius: 10px;
    display: flex;
    height: 128px;
    justify-content: space-around;
    align-items: center;
    margin: 20px 0;
    padding: 0 20px;

    .carName {
      display: flex;
      align-items: center;

      .Names {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 23px;
        color: #333333;
        padding: 0 4px;
      }

      .xhao {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #818385;
      }

    }

    .timu {
      display: flex;
      flex-direction: column;
      align-items: center;

      .mus {
        display: flex;
        align-items: center;
        .title {
          padding-left: 4px;
        }

      }

      .etx {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 23px;
        color: #333333;
      }
    }


  }

  .tables {
    height: 435px;
    background: #FFFFFF;
    border-radius: 14px;
    border: 1px solid #D9D9D9;
  }
}
