// import { Effect, ImmerReducer, Reducer, Subscription } from 'umi';

import loginApis from '@/service/loginApis';
import loginTypes from '@/types/loginTypes';
import { IReducers } from '@/types/modelsTypes';

// export interface IUserInfo {
//   showLoading: boolean;
//   leftMenuExpand: boolean;
//   mobileFlag: boolean;
// }

export default {
  namespace: 'userInfo',
  state: {},
  subscriptions: {
    setup({ dispatch, history }: any) {
      return history.listen(({ pathname }: any) => {
        dispatch({
          type: 'fetchUserInfo',
        });
      });
    },
  },
  effects: {
    *fetchUserInfo(_: any, { call, put }: any) {
      const {
        errorCode,
        extendMessage,
      }: API.OsResponse<loginTypes.IPermission[]> = yield call(
        loginApis.fetchUserInfo,
      );
      if (errorCode ==='success') {
        // const rmanPermission = extendMessage.find(
        //   (per: loginTypes.IPermission) => per.code === _rmanCode,
        // );
        yield put({
          type: 'updateUserInfoState',
          payload: extendMessage,
        });
      }
    },
  },
  reducers: {
    updateUserInfoState: (
      state: any,
      { payload }: IReducers<any>,
    ) => {
      return {
        ...state,
        ...payload
      };
    },
  },
};
