import React from 'react'
import { connect, Dispatch, useSelector, IConfig } from 'umi'
import { Spin } from 'antd'
import { LoadingOutlined } from '@ant-design/icons';
import "./style.less"

const Loading = () => {
    const { showLoading }:IConfig = useSelector(({ config }: any) => {
        return config
    })
    if (showLoading) {
        const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;
        return (
            <div className="loading-container">
                <Spin indicator={antIcon} size="large" />
            </div>
        )
    } else {
        return null
    }
}

export default Loading