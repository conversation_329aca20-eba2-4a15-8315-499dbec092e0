import React, { FC, useState, useRef, useEffect } from 'react';
import { Button, message, InputNumber, Select, Form } from 'antd';
import './index.less';
import './reset.less';
import PlanContent from './components/planContent'
import Empty from './components/empty'
import LoadingPage from './components/loadingPage'
import RequireSet from './components/requireSet'
import { getWrite } from './AiGenerate/common';
import { generateByBrief, enerateByVideoId, generateByFile, generateByPoint, generateByChapter, examinationAdd } from '@/api/questionset'
import { CloseCircleOutlined, PlusCircleFilled } from '@ant-design/icons'
import { getUuid } from '@/utils'

export interface TeachingPlanProps {
	height: number;
}

import { options, languageOptions, cognitive_levelOptions, questions_difficultyOptions, questions_levelOptions } from '@/utils'

const QuestionSetting: FC<TeachingPlanProps> = () => {
	const [form] = Form.useForm();
	const [selectOptions, setSelectOptions] = useState<any>(options.slice(0, 1));
	const [modeData] = useState<any>([
		{ id: 3, type: 3, name: '文本出题', img: require('@/assets/img/teachingPlan/wenben01.png'), active_img: require('@/assets/img/teachingPlan/wenben02.png') },
		{ id: 4, type: 4, name: '材料出题', img: require('@/assets/img/teachingPlan/cailiao01.png'), active_img: require('@/assets/img/teachingPlan/cailiao02.png') },
		{ id: 2, type: 2, name: '知识点出题', img: require('@/assets/img/teachingPlan/zhishi01.png'), active_img: require('@/assets/img/teachingPlan/zhishi02.png') },
		{ id: 1, type: 1, name: '章节出题', img: require('@/assets/img/teachingPlan/zhangjie01.png'), active_img: require('@/assets/img/teachingPlan/zhangjie02.png') },
	])
	const [activeMode, setActiveMode] = useState<string | number>(3) // 当前active
	const [list, setList] = useState<any[]>([]); // 右侧生成的内容
	const [inputValue, setInputValue] = useState<string>('') // 富文本
	const [showEmpty, setShowEmpty] = useState<boolean>(true) // 是否展示空提示
	const [showContent, setShowContent] = useState<boolean>(false) // 是否展示右侧生成的内容
	const [showLoading, setShowLoading] = useState<boolean>(false) // 正在生成时左侧的loading效果
	const [btnLoading, setBtnLoading] = useState<boolean>(false) // 生成按钮loading效果
	const [isScrollBottom, setIsScrollBottom] = useState<boolean>(false)// 判断是否需要滚动到底部
	// 题型数量值
	const [selectInputValue, setSelectInputValue] = useState<any>(options.slice(0, 1));

	const planContentRef = useRef<any>(null)
	const mapRef = useRef<any>(null)

	useEffect(() => {
		// 试题难度为必填
		form.setFieldValue('questions_difficulty', 3)
		form.setFieldValue('language', 1)
	}, [])

	// 处理生成的方式
	const getParams = () => {
		const bottomValues = form.getFieldsValue()
		const questions = selectInputValue.map((item: any) => {
			return {
				questions_type: item.value, // 试题类型
				num: item.num, // 生成个数
			}
		})
		let params: any = {
			brief: inputValue, // 文本内容
			questions,
			...bottomValues,
			questions_level: bottomValues?.questions_level?.join(',') || null, // 适用层次
			questions_difficulty: bottomValues?.questions_difficulty || 3, // 试题难度
		}
		if (activeMode == 3) {
			// 按文本
			if (!inputValue) {
				message.error('请输入文本内容!')
				return false
			}
		} else if (activeMode == 2) {
			// 按知识点
			const mapInfo = mapRef.current?.mapInfo
			if (!mapInfo.length) {
				message.error('请选择一个图谱!')
				return false
			}
			const knowledgePoint = mapInfo?.map((item: any) => {
				return item.knowledge.map((ele: any) => {
					return ele.name
				})
			})
			params['knowledgePoint'] = knowledgePoint.flat()
		} else if (activeMode == 4) {
			// 按材料
			const file = mapRef.current?.fileList
			if (!file || file.length == 0) {
				message.error('请选择一个文件!')
				return false
			}
		} else if (activeMode == 1) {
			// 按章节
			const chapterInfo = mapRef.current?.chapterInfo
			if (!chapterInfo || chapterInfo.length == 0) {
				message.error('请添加章节!')
				return false
			}
			params['chapterParams'] = chapterInfo?.map((item: any) => {
				return {
					firstLevelName: item.name,
					children: item.children.map((it2: any) => it2.name)
				}
			})
		}
		return params
	}

	// 消息函数返回体结构
	const chatMessageFunction = (fun: any, params: any) => {
		return new Promise((resolve) => {
			fun.then((response: any) => {
				setShowLoading(false)
				setShowContent(true)
				const reader = response.body.getReader();
				if (response.headers.get('Content-Type') === 'application/json') throw ('服务器错误！');
				if (response.status !== 200) throw (response);
				// 处理流数据
				const write = getWrite(
					reader,
					response.headers.get('Content-Type') !== 'application/json',
					(content: any, config: any, finish: boolean, firstChatInfo) => {
						let text = content.content + '\n' + '\n'
						content.options?.forEach((ele: string) => {
							text += ele + '\n'
						})

						switch (content.questions_type) {
							case 0: // 单选
								text += '\n' + '正确答案：' + content.answer
								break;
							case 1: // 多选
								text += '\n' + '正确答案：' + content.answer?.join('')
								break;
							case 2: // 填空
								text += '正确答案：' + content.answer?.join(', ') || content.answer
								break;
							case 3: // 主观
								text += '正确答案：' + content.answer
								break;
							case 4: // 判断
								text += '正确答案：' + ([false, 'false'].includes(content.answer) ? '错误' : '正确')
								break;
						}
						text += '\n' + '\n' + '答案解析：' + content.analysis
						setList((pre) => {
							return [...pre, {
								content: text, id: getUuid(), readOnly: true, questions_type: content.questions_type,
								params, questions_answers: content.answer, questions_analysis: content.analysis, questions_content: content.content, questions_options: content.options
							}]
						});
					}
				)
				reader.read().then(write)
			}).catch((e: any) => {
				resolve(false);
				setBtnLoading(false)
				setIsScrollBottom(false)
				setShowEmpty(true)
				setShowContent(false)
				message.error(e?.statusText || '服务器异常！');
			}).finally(() => { });
		})
	}

	useEffect(() => {
		if (list.length) { // 滚动到底部
			let length = 0
			selectInputValue.map((item: any) => {
				length += item.num
			})
			if (list.length == length) { // 滚动到底部
				setBtnLoading(false)
				setIsScrollBottom(false)
			}
		}
	}, [list])

	// 左侧开始生成
	const handleGenerate = async () => {
		const parmas: any = getParams()
		if (!parmas) return
		setBtnLoading(true)
		setList([])
		setShowContent(false)
		setShowLoading(true)
		setShowEmpty(false)
		setIsScrollBottom(true)
		// const results = []

		// for (const item of parmas.questions) {
		if (activeMode == 3) { // 文本
			const requestApi = generateByBrief(parmas);
			const response = await chatMessageFunction(requestApi, parmas);
			// results.push(response)
		} else if (activeMode == 2) { // 知识点
			const requestApi = generateByPoint(parmas);
			const response = await chatMessageFunction(requestApi, parmas);
			// results.push(response)
		} else if (activeMode == 4) {
			// 判断是自己上传的文件还是资源库先择的文件
			const file = mapRef.current?.fileList
			const fileMethod = mapRef.current?.fileMethod
			if (fileMethod == 1) { // 选择资源库材料
				const requestApi = enerateByVideoId({ ...parmas, videoId: file[0].id });
				const response = await chatMessageFunction(requestApi, { ...parmas, file: file[0] });
				// results.push(response)
			} else { // 文件
				delete parmas.brief
				let formData = new FormData();
				formData.append('file', file[0].originFileObj);
				formData.append('params', JSON.stringify(parmas));
				formData.append('brief', parmas.inputValue);
				const requestApi = generateByFile(formData);
				const response = await chatMessageFunction(requestApi, { ...parmas, file: file[0] });
				// results.push(response)
			}
		} else if (activeMode == 1) { // 章节
			const requestApi = generateByChapter(parmas);
			const response = await chatMessageFunction(requestApi, parmas);
			// results.push(response)
		}
		// }
		// Promise.all(results).then(() => {
		// 	setBtnLoading(false)
		// 	setIsScrollBottom(false)
		// })
	};

	// 增加题型和数量
	const addQuestionTypeNumber = () => {
		if (selectOptions.length < options.length) {
			const isHave = selectInputValue.map((ele: any) => ele.id);
			const filterOptions = options.filter((item) => !isHave.includes(item.id));
			if (filterOptions.length) {
				setSelectOptions([...selectOptions, filterOptions[0]]);
			}
		}
	};

	/**
	 * 
	 * @param value 当前选择的值
	 * @param option 当前选择的option
	 * @param item 当前点击的选择框
	 */
	const handleChangeSelect = (value: any, option: any, itemId: string) => {
		let newArr: any[] = []
		const existItemsId = selectInputValue.map((ele: any) => ele.id);
		if (existItemsId.includes(itemId)) { // 判断当前item是否存在
			newArr = selectInputValue?.map((element: any) => {
				if (element.id == itemId) {
					return { num: element.num, questions_type: value, label: value, id: itemId, value: option.value };
				} else {
					return element;
				}
			});
		} else { // 不存在则添加新的item
			newArr = [...selectInputValue, { ...option, id: itemId }]
		}
		setSelectInputValue(newArr);
	};

	// input框输入
	const handeChangeInput = (value: any, itemId: string) => {
		let newArr: any[] = selectInputValue?.map((element: any) => {
			if (element.id == itemId) {
				return { ...element, id: itemId, num: value };
			} else {
				return element;
			}
		});
		setSelectInputValue(newArr);
	};

	// 下拉options
	const renderSelectOptions = () => {
		const isUseOptions = selectInputValue?.map((ele: any) => ele.questions_type)
		return options.map((ele) => {
			return {
				...ele,
				disabled: isUseOptions.includes(ele.questions_type)
			};
		});
	};

	// 删除item
	const delTypeNumberItem = (item: any) => {
		const newSelectOptions = selectOptions.filter((ele: any) => ele.id !== item.id);
		setSelectOptions(newSelectOptions);
		const newSelectInputValue = selectInputValue.filter((ele: any) => ele.id !== item.id);
		setSelectInputValue(newSelectInputValue);
	}

	// 加入题库
	const addTestQuest = (list: any) => {
		console.log(list, 'list')
		list.forEach(async (item: any) => {
			const { cognitive_level, questions_difficulty, questions_level, file } = item.params
			const { questions_type, questions_answers, questions_analysis, questions_content, questions_options = [] } = item
			const options = questions_options?.map((item: any, index: number) => {
				return {
					content: item.slice(3),
					seq: index,
					uid: getUuid(),
				}
			})
			let answers = []
			if (questions_type == 4) { // 判断
				answers = [false, 'false'].includes(questions_answers) ? ['B'] : ['A']
			} else {
				answers = Array.isArray(questions_answers) ? questions_answers : [questions_answers]
			}
			// let fileList = [{
			// 	attachmentName: file?.name || '',
			// 	attachmentSize: file?.size || '',
			// 	attachmentSource,
			// 	attachmentType: file?.type || '',
			// 	category,
			// 	contentId,
			// 	order,
			// 	questionId,
			// }]
			let params = {
				questions_analysis, // 试题的解析
				questions_answers: answers, // 试题的正确答案  
				questions_content, // 试题内容
				questions_options: options || [], // 选项
				questions_type, // 试题类型
				questions_difficulty, // 试题难度
				cognitive_level, // 认知层次
				questions_level: questions_level?.split(',') || [],// 适用层次
			}
			const result: any = await examinationAdd(params);
			console.log(params, result, 'result')
			if (result?.status == 200) {
				message.success('加入成功')
			} else {
				message.error(result?.message || '加入失败')
				Promise.reject(result?.message || '加入失败')
				return;
			}
		})

	}

	return (
		<div className='teaching-plan'>
			<div className="content">
				<div className='left'>
					<div className='left-title'>
						<img src={require("@/assets/img/teachingPlan/ai_logo1.png")} alt="title" />
						<img src={require("@/assets/img/teachingPlan/title_logo.png")} alt="title" />
					</div>

					<div className='teaching-plan-mode'>
						{modeData.map((item: any) => {
							return <div key={item.id} className={`mode-item ${activeMode == item.id ? 'active-mode-item' : ''}`} onClick={() => {
								setActiveMode(item.id)
							}}>
								<img src={activeMode == item.id ? item.active_img : item.img} alt="" />
								<span>{item.name}</span>
							</div>
						})
						}
					</div>

					<div className='set-teaching-plan-content'>
						<RequireSet
							emitInputValue={(val) => {
								setInputValue(val)
							}}
							mapRef={mapRef}
							selectType={activeMode}
						/>
						<div className="questionSetting">
							<div className='setting-title'>
								<img src={require('@/assets/img/teachingPlan/zhangjie01.png')} alt="" />
								<span>题目设置</span>
							</div>
							<div className='setting-content'>
								<div className="question-type-Number">
									<div className="title"><span> * </span>题型及数量 （必填）</div>
									{
										selectOptions.map((item: any, index: number) => {
											return <div className="item" key={item.label}>
												<Select
													onChange={(value, option) => handleChangeSelect(value, option, item.id)}
													options={renderSelectOptions()}
													fieldNames={{ label: 'label', value: 'questions_type' }}
													value={selectInputValue[index]}
												/>
												<InputNumber
													defaultValue={1}
													min={1}
													max={100}
													onChange={(value) => handeChangeInput(value, item.id)}
													disabled={!selectInputValue[index]} // 未选择题型时禁用输入框
												/>
												{
													selectInputValue.length > 1 && <CloseCircleOutlined onClick={() => delTypeNumberItem(item)} />
												}
											</div>
										})
									}
								</div>
								{
									selectOptions.length < options.length && <div className='add-btn' onClick={addQuestionTypeNumber}>
										<PlusCircleFilled />
										添加
									</div>
								}
								<div className="bottom">
									<Form form={form}>
										<div className="item itemFirst">
											<div className="title">
												<div className="title-left">认知层次 （选填）</div>
												<div className="title-right">难度 （选填）</div>
											</div>
											<div className="select-input">
												<Form.Item name="cognitive_level">
													<Select allowClear placeholder="请选择认知层次" options={cognitive_levelOptions} />
												</Form.Item>
												<Form.Item name="questions_difficulty">
													<Select allowClear placeholder="请选择难度" options={questions_difficultyOptions} />
												</Form.Item>
											</div>
										</div>
										<div className="item">
											<div className="title">
												<div className="title-left">适用层次 （选填）</div>
												<div className="title-right">输出语言（选填）</div>
											</div>
											<div className="select-input">
												<Form.Item name="questions_level">
													<Select
														mode="multiple"
														allowClear
														placeholder="请选择适用层次"
														options={questions_levelOptions}
													/>
												</Form.Item>
												<Form.Item name="language">
													<Select allowClear placeholder="请选择输出语言" options={languageOptions} />
												</Form.Item>

											</div>
										</div>
									</Form>
								</div>
							</div>
						</div>
					</div>
					<Button loading={btnLoading} onClick={handleGenerate}>开始生成</Button>
				</div>
				<main className='right'>
					{/* 已经生成的内容 */}
					{showContent && <PlanContent
						isScrollBottom={isScrollBottom}
						planContentRef={planContentRef}
						list={list}
						btnLoading={btnLoading}
						addTestQuest={addTestQuest}
					/>}
					{/* 默认提示内容 */}
					{showEmpty && <Empty />}
					{/* 正在生成时的效果 */}
					<LoadingPage showLoading={showLoading} />
				</main>
			</div>
		</div>
	);
};

export default QuestionSetting;