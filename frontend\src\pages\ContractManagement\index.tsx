import Contract from "@/api/Contract";
import { CloseCircleOutlined, createFromIconfontCN, DeleteOutlined, EditOutlined, PlusOutlined, QuestionCircleFilled } from "@ant-design/icons";
import { Button, Form, Input, message, Modal, Space, Table } from "antd";
import React, { useEffect, useState } from "react";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import './index.less';

const ItemType = "ROW";
const IconFont = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_2019002_dva91dyojmi.js',
});

const iconTypes = [
  'iconmenu_home',
  'iconcacel',
  'iconmenu_coursetemplate',
  'icona-zhengshu_zizhibeifen2',
  'iconqingdan',
  'iconyulancaidan',
  'icontikuguanli',
  'iconshijuanguanli1',
  'iconSettingsbeifen',
  'iconiconqanda',
  'icona-bianzu7',
  'icona-bianzu3',
  'icona-bianzu8',
  'iconkaoshi2',
  'iconkechengxinxi1',
  'iconziyuan1',
  'icontaolun',
  'iconxueshengguanli',
  'iconhudong',
  'iconkebiaoguanli',
  'iconzhihuitiku',
  'icongongjuxiang'
];
// 拖拽行组件
const DraggableRow = ({
  index,
  moveRow,
  record,
  style,
  syncOrderToBackend,
  dataSource, // 传递完整数据源，用于获取目标项数据
  ...restProps
}: any) => {
  const ref = React.useRef();

  const [{ isOver }, drop] = useDrop({
    accept: ItemType,
    hover: (draggedItem: any) => {
      const isSameParent =
        draggedItem.record.parentId === record.parentId || draggedItem.record.isTop;
      if (isSameParent && draggedItem.index !== index) {
        moveRow(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
    drop: () => ({
      index,
      record, // 当前目标项的数据
    }),
  });

  const [{ isDragging }, drag] = useDrag({
    type: ItemType,
    item: { index, record },
    end: (draggedItem: any, monitor) => {
      const didDrop = monitor.didDrop();
      if (didDrop) {
        const dropResult = monitor.getDropResult();
        const toIndex = dropResult?.index;
        const targetItem = dropResult?.record;

        // 检查是否在同一范围内拖拽
        const isSameParent =
          draggedItem.record.parentId === targetItem.parentId || draggedItem.record.isTop;

        if (isSameParent) {
          syncOrderToBackend(draggedItem.record, targetItem, toIndex);
        } else {
          message.warning("子级只能在父级范围内拖拽！");
        }
      }
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(ref));

  return (
    <tr
      ref={ref}
      className={restProps.className}
      style={{
        ...style,
        opacity: isDragging ? 0.5 : 1,
        cursor: "move",
        backgroundColor: isOver ? "#f0f0f0" : "inherit", // 拖拽目标行高亮
      }}
      {...restProps}
    />
  );
};



const ContractManagement = () => {
  const [dataSource, setDataSource] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [iconVisible, seticonVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [form] = Form.useForm();
  const [currentTitle, setCurrentTitle] = useState(null);
  const [currentParentId, setCurrentParentId] = useState(null);
  const [currentSortid, setCurrentSortid] = useState(null);
  const [pagination, setPagination] = useState({
    current: 1,  // 当前页码
    pageSize: 10, // 每页显示的数量
  });
  const [pagtotal, setPagtotal] = useState();
  const [selectedIcon, setSelectedIcon] = useState<string | null>(null);
  const [isisTop, setisisTop] = useState(false);
  const [getname, setname] = useState('');
  // 获取数据源
  const fetchDetail = async (page: number, size: number) => {
    const res = await Contract.classification({ page, size });
    if (res.status === 200) {
      let data = res.data.data;
      console.log('fetchDetail', res.data);
      setDataSource(data);
      // total
      setPagtotal(res.data.totalCount)
    }
  };
  // 新增
  const InsertAPI = async (name: string, parentId: any, icon: any) => {
    const res = await Contract.classificationInsert({ name, parentId, icon });
    if (res.status === 200) {
      // console.log('fetchDetail', res);
      message.success("新增成功！");
      fetchDetail(pagination.current, pagination.pageSize);
    }
  };

  // 同步更新顺序到后端
  const syncOrderToBackend = async (draggedItem: any, targetItem: any, toIndex: number) => {
    if (targetItem?.isTop) {
      UpdateAPI(draggedItem?.id, draggedItem?.name, draggedItem.sort - 1, selectedIcon);
      // console.log("父级拖拽：", draggedItem?.id, draggedItem?.name, "目标索引：", toIndex, draggedItem.sort - 1);
    } else {
      const parent = dataSource.find((item) => item.id === targetItem.parentId);
      const childIndex = parent?.children?.findIndex((child) => child.id === targetItem.id);
      // console.log(
      //   "子级拖拽：", draggedItem?.id, draggedItem?.name, "目标父级：", parent?.name, "目标子级索引：", childIndex,
      //   draggedItem,targetItem
      // );
      UpdateAPI(draggedItem?.id, draggedItem?.name, targetItem.sort - 1, selectedIcon);
    }
  };


  // 更新分类顺序的接口
  const UpdateAPI = async (id: string, name: string, sort: string, icon: any) => {
    const res = await Contract.classificationUpdate({ id, name, sort, icon });
    fetchDetail(pagination.current, pagination.pageSize); // 更新数据
    if (res.status == 200) {
      message.success('编辑成功');
    } else {
      message.success(res.message);
    }
  };

  // 删除
  const DeleteAPI = async (data: any) => {
    const res = await Contract.classificationDelete(data);
    if (res.status === 200) {
      message.success("删除成功！");
      fetchDetail(pagination.current, pagination.pageSize);
    }
    // message.success(res.message);
  };
  useEffect(() => {
    fetchDetail(pagination.current, pagination.pageSize);
  }, [pagination.current, pagination.pageSize]);

  const handleTableChange = (pagination: { current: any; pageSize: any; }) => {
    setPagination({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 打开新增分类弹窗
  const showModal = (parentId = null, title: any, item = null) => {
    console.log(item, 'parentId', title);
    setname(item?.name)
    setCurrentSortid(item?.sort)
    setCurrentParentId(parentId);
    setCurrentTitle(title)
    setIsModalVisible(true);
    setSelectedIcon(item?.icon)
    if (item?.isTop) {
      setisisTop(true)
    } else if (title == '新增一级分类') {
      setisisTop(true)
    } else {
      setisisTop(false)
    }
    form.setFieldsValue({ name: item?.name || '' }); // 更新表单值
  };

  // 关闭弹窗
  const handleCancel = () => {
    form.resetFields();
    setIsModalVisible(false);
    setCurrentTitle(null);
  };

  // 新增分类
  const handleAdd = () => {
    form.validateFields().then((values) => {
      if (currentTitle == '编辑') {
        console.log(values, 'values');
        UpdateAPI(currentParentId, values.name, currentSortid, selectedIcon)
      } else {
        const parentId = currentTitle === "添加下级分类" ? currentParentId : '';
        InsertAPI(values.name, parentId, selectedIcon)
      }
      handleCancel();
    });
  };

  // 递归添加子分类
  const addChildToParent = (data: any, parentId: any, child: any) => {
    return data.map((item: any) => {
      if (item.id === parentId) {
        const updatedItem = {
          ...item,
          children: item.children ? [...item.children, child] : [child],
        };
        return updatedItem;
      } else if (item.children) {
        return {
          ...item,
          children: addChildToParent(item.children, parentId, child),
        };
      }
      return item;
    });
  };

  // 删除选中项
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请先选择要删除的项！");
      return;
    }
    Modal.confirm({
      content: '确定是否删除？',
      title: '删除确认',
      icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
      onOk: () => {
        const dataToSend = {
          "ids": selectedRowKeys  // 将当前的 id 添加到 ids 数组中
        };
        DeleteAPI(dataToSend)
      },
    });
  };

  // 递归删除多个节点
  const deleteNodesByIds = (data: any, ids: any) => {
    return data
      .filter((item: { id: any; }) => !ids.includes(item.id))
      .map((item: { children: any; }) => {
        if (item.children) {
          return { ...item, children: deleteNodesByIds(item.children, ids) };
        }
        return item;
      });
  };

  // 移动行
  const moveRow = (fromIndex: number, toIndex: number) => {
    if (fromIndex === toIndex) return;
    const updatedData = [...dataSource];
    const [movedRow] = updatedData.splice(fromIndex, 1);
    updatedData.splice(toIndex, 0, movedRow);
    setDataSource(updatedData);
  };

  const columns = [
    {
      title: "名称",
      dataIndex: "name",
      key: "name",
      width: 800,
      render: (text: string, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <IconFont style={{ fontSize: '18px', marginRight: '8px' }} type={record.icon} />
          {text}
        </div>
      ),
    },
    {
      title: "操作",
      key: "action",
      width: 340,
      align: 'center',
      render: (_: any, record: any) => (
        <>
          {record?.isTop && (
            <Button
              type="link"
              onClick={() => showModal(record.id, '添加下级分类', null)}
              icon={<PlusOutlined />}  // 添加下级分类图标
            >
              添加下级分类
            </Button>
          )}

          <Button
            type="link"
            onClick={() => showModal(record.id, '编辑', record)}
            icon={<EditOutlined />}  // 编辑图标
          >
            编辑
          </Button>

          <Button
            type="link"
            danger
            onClick={() => handleDelete(record.id)}
            icon={<DeleteOutlined />}  // 删除图标
          >
            删除
          </Button>
        </>
      ),
    }
  ];

  // 操作删除
  const handleDelete = (id: string) => {


    Modal.confirm({
      content: '确定是否删除？',
      title: '删除确认',
      icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
      onOk: () => {
        const dataToSend = {
          "ids": [id]  // 将当前的 id 添加到 ids 数组中
        };
        DeleteAPI(dataToSend);
      },
    });
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: any) => {
      setSelectedRowKeys(selectedKeys);
    },
  };

  // 点击图标时处理函数
  const handleIconClick = (type: string) => {
    setSelectedIcon(type);
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="ContractManagements">
        <div className="button_zuh">
          <Button type="primary" onClick={() => showModal(null, '新增一级分类', null)}>
            + 新增
          </Button>
          <Button
            danger
            style={{ marginLeft: 8 }}
            onClick={handleBatchDelete}
          >
            删除
          </Button>
        </div>
        <Table
          dataSource={dataSource}
          columns={columns}
          rowKey="id"
          rowSelection={rowSelection}
          pagination={{
            position: ["bottomCenter"],
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagtotal,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: (page, pageSize) => handleTableChange({ current: page, pageSize }),
          }}
          scroll={{ y: 'calc(100vh - 300px)' }}
          components={{
            body: {
              row: DraggableRow,
            },
          }}
          onRow={(record, index) => ({
            index,
            record,
            moveRow,
            syncOrderToBackend,
            dataSource,
          })}
        />
        <Modal
          title={currentTitle}
          visible={isModalVisible}
          onCancel={handleCancel}
          onOk={() => handleAdd(currentTitle)}>
          <Form form={form} initialValues={{ name: getname }}>
            <Form.Item
              name="name"
              label="名称"
              rules={[{ required: true, message: "请输入名称" }]}
            >
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Form>
          {isisTop && (
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }} >
              <div style={{ marginLeft: '11px', fontSize: '14px', display: 'flex', alignItems: 'center' }} >
                <div style={{ marginRight: '10px' }} >
                  图标
                </div>
                <IconFont style={{ fontSize: '26px' }} type={selectedIcon} />
                {selectedIcon && (
                  <div style={{ marginLeft: '10px', cursor: 'pointer' }} onClick={() => setSelectedIcon(null)}>
                    <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                  </div>
                )}
              </div>
              <div>
                <Button onClick={() => seticonVisible(true)} type="primary" shape="round" >选择图标</Button>
              </div>
            </div>
          )}

        </Modal>

        <Modal
          width='36%'
          title="选择图标"
          visible={iconVisible}
          onCancel={() => seticonVisible(false)}
          onOk={() => seticonVisible(false)}>

          <div className="icon_box">
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
              {iconTypes.map((type) => (
                <div
                  key={type}
                  style={{
                    padding: '15px',
                    borderRadius: '5px',
                    backgroundColor: selectedIcon === type ? '#eee' : 'transparent',
                    cursor: 'pointer'
                  }}
                  onClick={() => handleIconClick(type)}
                >
                  <IconFont style={{ fontSize: '26px' }} type={type} />
                </div>
              ))}
            </div>
          </div>
        </Modal>
      </div>
    </DndProvider>
  );
};

export default ContractManagement;
