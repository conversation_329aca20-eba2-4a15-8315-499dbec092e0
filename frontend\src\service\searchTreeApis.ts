import http from '../http/http';
import searchTypes from '@/types/searchTypes';

namespace searchTreeApis {
  export const allmaterial = () => {
    //全部素材
    return http('/search/config/fix-condition', {
      method: 'GET',
    });
  };
  export const publicmaterial = () => {
    //公共素材
    return http<searchTypes.IFolder>('/folder/init/public', {
      method: 'GET',
    });
  };
  export const getChildren = (folderId: string) => {
    return http<searchTypes.IFolder[]>(
      `/folder/children?folderId=${folderId}`,
      {
        method: 'GET',
      },
    );
  };
  export const tree = (folderType?: string) => {
    return http<searchTypes.IFolder[]>(`/folder/all/tree?level=2`, {
      method: 'GET',
    });
  };


  export const gettreebylevel = (gettreebylevel: number=2) => {
    return http<searchTypes.IFolder[]>(`/folder/all/tree?level=${gettreebylevel}`, {
      method: 'GET',
    });
  };
}

export default searchTreeApis;
