import loginApis from '@/service/loginApis';
import {
  Tabs,
  TabsProps
} from 'antd';
import { FC, useEffect, useMemo, useState } from 'react';
import './index.less';
import TestQuestionList from './TestQuestionList';

const ExamRecyle: FC = () => {
  //#region  获取用户信息
  const [userInfo, setUserInfo] = useState<any>(null);
  useEffect(() => {
    loginApis.fetchUserInfo().then((item: any) => {
      if (item && item.errorCode === 'success') {
        (window as any).login_useInfo = item.extendMessage;
        setUserInfo(item.extendMessage);
      }
    });
  }, []);
  //#endregion

  // 是否是管理员
  const isSuper = useMemo(() => {
    if (!userInfo?.roles) {
      return false;
    }
    return (
      userInfo.roles
        ?.map((item: any) => item.roleCode)
        ?.includes('r_sys_manager') || //是否是系统管理员
      userInfo.roles
        ?.map((item: any) => item.roleCode)
        ?.includes('r_course_manager') || //是否是课程管理员
      userInfo.roles
        ?.map((item: any) => item.roleCode)
        ?.includes('r_second_manager') || //第二权限
      userInfo.roles?.map((item: any) => item.roleCode)?.includes('admin_S1')
    ); // admin
  }, [userInfo]);

  //是否是普通教师
  const isTeacher = useMemo(() => {
    if (!userInfo?.roles) {
      return false;
    }
    return userInfo.roles
      ?.map((item: any) => item.roleCode)
      ?.includes('r_teacher');
  }, [userInfo]);

  const TabItems: TabsProps['items'] = [
    {
      key: '1',
      label: '试题',
      children: <TestQuestionList isSuper={isSuper} isTeacher={isTeacher} />,
    },
  ];

  const handleChange = (key: string) => {
    // console.log(key);
  };
  return (
    <div style={{ padding: 20 }}>
      <Tabs defaultActiveKey="1" items={TabItems} onChange={handleChange} />
    </div>
  );
};

export default ExamRecyle;
