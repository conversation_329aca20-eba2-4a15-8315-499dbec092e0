import StartOne from '@/assets/testSystem/start_one.png';
import StartThree from '@/assets/testSystem/start_three.png';
import StartTwo from '@/assets/testSystem/start_two.png';
import BG from '@/assets/testSystem/testSystemBg.png';
import TestSystemHeader from '@/components/TestSystemHeader';
import { FC } from 'react';
import { history, useSelector } from 'umi';
import './index.less';

import StartOneIcon from '@/assets/testSystem/start_icon.png';
import StartThreeIcon from '@/assets/testSystem/start_three_icon.png';
import StartTwoIcon from '@/assets/testSystem/start_two_icon.png';

const TestSystem: FC = () => {
  const userInfo = useSelector((state: any) => state.userInfo);
  const { title, logoUrl, isShow } = useSelector<any, any>(
    state => state.themes,
  );
  return (
    <div className="test_system_container">
      {/* <img src={logoUrl} className="system_bg" /> */}
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          background: '#ffffff',
          boxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.07)'
        }}
        onClick={() => {
          window.location.href = '/exam/#/testsystem';
        }}
        className="header_clickable"
      >
        <span className='icon_box'>
          <img
            src={logoUrl || require('@/images/login/default_logo.png')}
            style={{
              height: '32px'
            }}
          />
        </span>
        <TestSystemHeader title="招警联考测试系统" />
      </div>
      <div className="test_index_container">
        <div className="menu_item" onClick={() => history.push('/stuexam')}>
          <img className="menu_item_img" src={StartOne} />
          <img className="menu_item_icon" src={StartOneIcon} />
          开始测验
        </div>
        <div
          className="menu_item"
          style={{ color: '#fff' }}
          onClick={() => history.push('/testreport')}
        >
          <img className="menu_item_img" src={StartTwo} />
          <img className="menu_item_icon" src={StartTwoIcon} />
          测验报告
        </div>

        <div
          onClick={() => history.push('/FavoritesPage')}
          className="menu_item"
        >
          <img className="menu_item_img" src={StartThree} />
          <img className="menu_item_icon" src={StartThreeIcon} />
          收藏题目
        </div>
      </div>
    </div>
  );
};

export default TestSystem;
