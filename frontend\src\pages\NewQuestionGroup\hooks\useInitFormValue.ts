import paperManageApis from '@/api/Contract';
import examManageApis from '@/api/exam';
import { useEffect, useState } from 'react';

const useInitFormValue = () => {
  // 初始分享人
  const [initusers, setInitusers] = useState<any>([]);
  const fetchSharelist = async () => {
    const res = await examManageApis.initShareUsers();
    if (res.status === 200) {
      setInitusers(res.data);
    }
  };

  // 应用分类|考核方向
  const [initClassify, setInitClassify] = useState<any>([]);
  const fetchClassify = async () => {
    const res = await paperManageApis.classification({ page: 1, size: 100 });
    if (res.status === 200) {
      setInitClassify(res.data?.data || []);
    }
  };
  // 题目来源
  const [initQuestionSource, setInitQuestionSource] = useState<any>([]);
  const fetchQuestionSource = async () => {
    const res = await examManageApis.questionSource();
    if (res.status === 200) {
      setInitQuestionSource(res.data);
    }
  };

  useEffect(() => {
    fetchSharelist();
    fetchClassify();
    fetchQuestionSource();
  }, []);

  return {
    initusers,
    initClassify,
    initQuestionSource,
  };
};

export default useInitFormValue;
