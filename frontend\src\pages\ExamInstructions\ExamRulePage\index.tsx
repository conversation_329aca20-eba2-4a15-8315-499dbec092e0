import paperManageApis from '@/api/Contract';
import TestSystemHeader from '@/components/TestSystemHeader';
import { Modal } from 'antd';
import { FC, useEffect, useState } from 'react';
import { history, useLocation } from 'umi';
import FilePreview from '../FilePreview';
import styles from './index.less';

const ExamRulePage: FC = () => {
  const location: any = useLocation();

  const [instructUrl, setInstructUrl] = useState('');

  // 格式化文件地址
  const formatUrl = (url: string) => {
    //
    if (url.indexOf('http') === -1) {
      if (process.env.NODE_ENV === 'development') {
        url = `http://172.16.151.202${url}`;
      } else {
        url = `${location.origin}${url}`;
      }
    }
    return url;
  };

  const getInstructInfo = (id: string) => {
    paperManageApis.getVoById(id).then((res: any) => {
      if (res?.status == 200) {
        const url = formatUrl(res?.data?.explainFile);
        setInstructUrl(url);
      }
    });
  };

  useEffect(() => {
    if (location?.query?.testId) {
      getInstructInfo(location.query?.testId);
    }
  }, [location?.query?.testId]);
  //   去答题
  const handleToAnswer = () => {
    history.replace(`/answerPage?testId=${location.query?.testId}`);
  };
  //   跳过弹窗
  const confirm = () => {
    Modal.confirm({
      title: '',
      closable: true,
      icon: '',
      //   icon: <ExclamationCircleOutlined />,
      wrapClassName: styles['skip_modal'],
      content: (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <div style={{ color: '#525252', fontSize: '16px', fontWeight: 600 }}>
            确定直接跳转到答题页面吗？
          </div>
          <div style={{ color: '#9D9D9D', fontSize: '14px' }}>
            <span style={{ color: '#fd7676' }}>*</span> 正式考试不可跳过
          </div>
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk() {
        handleToAnswer()
      },
    });
  };
  return (
    <div className={styles['rule_container']}>
      <TestSystemHeader showSkip onSkip={confirm} />
      <div className={styles['docx-view']}>
        <div className={styles.content}>
          <FilePreview url={instructUrl} />
        </div>
      </div>
    </div>
  );
};

export default ExamRulePage;
