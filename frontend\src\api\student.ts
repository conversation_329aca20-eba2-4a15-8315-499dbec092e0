/*
 * @Author: 李武林
 * @Date: 2021-12-03 12:18:28
 * @LastEditors: 李武林
 * @LastEditTime: 2022-04-07 15:24:47
 * @FilePath: \coursemanger\src\api\student.ts
 * @Description: 
 * 
 * Copyright (c) 2022 by 李武林/索贝数码科技股份有限公司, All Rights Reserved. 
 */
import HTTP from './index';
// 查询列表
export function getstudentlist(data: string) {
  return HTTP.get(`/learn/v1/teaching/course/get/student/list?${data}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 查询mooc学生列表
export function getmoocstudentlist(data: any) {
  return HTTP.get(`/learn/v1/teaching/course/get/mooc/student/list`,{
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 添加
export function addstudent(
  code: string,
  courseType: string,
  data: Array<IStudent.IaddParams>,
) {
  return HTTP.post(
    `/learn/v1/teaching/course/add/student?typeId=${code}&courseType=${courseType}`,
    data,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}
// /cvod​/v1​/teaching​/course​/delete​/student
// 删除
export function deletestudent(code: string, data: string[]) {
  return HTTP.post(
    `/learn/v1/teaching/course/delete/student?typeId=${code}`,
    {data},
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

// 根据code查询用户信息
export function getstudentinfo(data: any) {
  return HTTP.post(`/rman/v1/3rd/user/info `,data)
    .then(res => {
      return res
    })
    .catch(error => {
      console.error(error);
    });
}