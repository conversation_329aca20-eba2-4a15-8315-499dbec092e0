import React, { useEffect, useRef, useState } from "react";
import { Layout, Menu, Image, Image as Img, message, Form, Input, Button, Select, Table, Upload, DatePicker, Radio, Checkbox, Row, Col, Modal, UploadProps, Popconfirm } from "antd";
import { UploadOutlined, ArrowLeftOutlined, LoadingOutlined, InboxOutlined, CaretDownOutlined, UpOutlined, CaretUpOutlined, QuestionCircleFilled, CloseOutlined } from "@ant-design/icons";
import "./index.less";
import Sidebar from "./Sidebar";  // 引入 Sidebar 组件
import Tables from "./Tables";  // 引入封装的 TableWithButton 组件
const { Header, Sider, Content } = Layout;
const { Option } = Select;
import RelativeUser from './Tables/components/relativeUser';
import train from '@/api/train'
import Contract from "@/api/Contract";
import { useHistory, useLocation, useSelector } from 'umi'
import moment from "moment";
import Dragger from "antd/lib/upload/Dragger";
import QuestionsModal from "@/components/QuestionsModal";
import Entity from '@/components/entity/entity';

const NewTest: React.FC = () => {

    const { Dragger } = Upload;
    const defaultCover = require('../../../images/papercovertup.png');
    const [cover, setCover] = useState(defaultCover);
    const [explainFile, setexplainFile] = useState(null);
    const history = useHistory();
    const location: any = useLocation();
    const [form] = Form.useForm();  // 使用 Form.useForm 来获取表单实例
    const sectionRefs = useRef<{ [key: string]: HTMLElement | null }>({});
    // 侧边栏
    const scrollToSection = (section: string) => {
        sectionRefs.current[section]?.scrollIntoView({ behavior: "smooth", block: "start" });
    };

    const [userData, setUserData] = useState({});
    const [userID, setID] = useState('');
    const [editID, seteditID] = useState('');
    const [userTable, setTable] = useState([]); // 测验题目表格
    const [itemName, setitemName] = useState(''); //创建部分
    const [testState, settestState] = useState();  // 发布状态
    // 上传
    const [uploadLoading, setUploadLoading] = useState(false);
    const [operationGuideFile, setOperationGuideFile] = useState<string | null>(null); // 操作指南
    const [examRulesFile, setExamRulesFile] = useState<string | null>(null); // 考场规则
    const [candidateNoticeFile, setCandidateNoticeFile] = useState<string | null>(null); // 考生须知

    const [openStartDate, setopenStartDate] = useState<string | null>('Invalid date');
    const [openEndDate, setopenEndDate] = useState<string | null>('Invalid date');
    const [resitStartDate, setresitStartDate] = useState<string | null>('Invalid date');
    const [resitEndDate, setresitEndDate] = useState<string | null>('Invalid date');
    const [Featuredstate, seFeaturedstate] = useState(false);  // 专题练习  能力测试隐藏
    const [joinfy, getjoin] = useState(false);
    const [topicVisible, setTopicVisible] = useState<boolean>(false); //试题弹框
    const [testNames, setTestName] = useState("");

    // 更新 userData，并同步到本地存储
    const updateUserData = (newUserData: any) => {
        setUserData(newUserData);
        localStorage.setItem('userData', JSON.stringify(newUserData));  // 更新本地存储
    };

    const { parameterConfig } = useSelector<
        { permission: any },
        any
    >(({ permission }) => permission);


    // 预览
    const [fileType, setFileType] = useState('');
    const [filePath, setFilePath] = useState('');

    // 假设通过某种方式获取文件路径（比如从 API 或用户选择）
    const handleFilePreview = (fileUrl: string) => {
        const Url = '#/entitys';
        // 使用 URL 查询参数传递
        window.open(`${Url}?fileUrl=${fileUrl}`, '_blank');
    };

    useEffect(() => {
        const id = location.query.classifyId;
        setID(id)
        const bjid = location.query.bjid;
        const itemNames = location.query.itemName;
        setitemName(itemNames)
        seteditID(bjid)
        if (location.query.name == "编辑") {
            // console.log('编辑模块');
            getVoByIdAPI(location.query.bjid)
        } else {
            builds(id)
            form.setFieldsValue({
                unit: 'minute',
            });
        }
    }, [localStorage.getItem("userData")]);

    useEffect(() => {
        // const Ash_customer_result = localStorage.getItem("Ash_customer_result");
        const Ash_parentName = localStorage.getItem("Ash_parentName");
        // true "专题练习"
        if (parameterConfig.target_customer !== 'ppsuc') {
            seFeaturedstate(false);
        } else {
            seFeaturedstate(true);
            if (Ash_parentName == "专题练习" || Ash_parentName === "能力测试") {
                seFeaturedstate(true);
            } else {
                seFeaturedstate(false);

            }
        }

    }, []);


    const getVoByIdAPI = async (id: any) => {
        const res = await Contract.getVoById(id);
        if (res.status === 200) {
            const userData = res.data;

            const openStartDate = moment(userData?.openStartDate).format('YYYY-MM-DD');
            const openEndDate = moment(userData?.openEndDate).format('YYYY-MM-DD');
            if (openStartDate) {


                setOpenDates([userData?.openStartDate, userData?.openEndDate])
                setopenStartDate(openStartDate)
                setopenEndDate(openEndDate)
            }

            const resitStartDate = moment(userData?.resitStartDate).format('YYYY-MM-DD');
            const resitEndDate = moment(userData?.resitEndDate).format('YYYY-MM-DD');
            if (resitStartDate) {
                setRepairDates([userData?.resitStartDate, userData?.resitEndDate])
                setresitStartDate(resitStartDate)
                setresitEndDate(resitEndDate)
            }
            localStorage.setItem('userData', JSON.stringify(userData));  // 将默认数据存储到本地
            setUserData(userData)
            gzao(userData)
            getnotify(userData.notifyType)
            getjoin(userData.isResit ? userData.isResit : false) // 补考设置
            setNoticeContent(JSON.parse(userData?.notice)?.stringValue ? JSON.parse(userData?.notice)?.stringValue : '')
            setrulesContent(JSON.parse(userData?.rule)?.stringValue ? JSON.parse(userData?.rule)?.stringValue : '')
            setnoticesContent(JSON.parse(userData?.guide)?.stringValue ? JSON.parse(userData?.guide)?.stringValue : '')
            setexplainFile(userData?.explainFile)
            setCandidateNoticeFile(JSON.parse(userData?.guide)?.address ? JSON.parse(userData?.guide)?.address : null) // 考生须知
            setOperationGuideFile(JSON.parse(userData?.notice)?.address ? JSON.parse(userData?.notice)?.address : null) // 操作指南
            setExamRulesFile(JSON.parse(userData?.rule)?.address ? JSON.parse(userData?.rule)?.address : null)
            setCover(userData?.cover)
            setTestName(userData.name)
            form.setFieldsValue({
                testName: userData.name,
                difficulty: userData.difficulty,
                multipleChoiceScoreConfig: userData.multipleChoiceScoreConfig, // 多选题得分设置
                fillScoreConfig: userData.fillScoreConfig,
                questionGroupScoreConfig: userData.questionGroupScoreConfig,
                examFrequency: userData.examFrequency,//考试次数
                // examMode: userData.examMode === 3 ? [1, 2] : userData.examMode ? [userData.examMode] : [],
                examMode: userData.examMode != null
                    ? userData.examMode === 3
                        ? [1, 2]
                        : [userData.examMode]
                    : [1],
                passScore: userData.passScore,//及格分数
                fineScore: userData.fineScore,//良好分数
                goodScore: userData.goodScore,//优秀分数
                //开放时间  补考开始时间 
                isResit: userData.isResit ? userData.isResit : false,//补考设置
                notifyType: userData.notifyType,//通知方式
                examBeforeMinute: userData.examBeforeMinute,//考试开始前
                examEndDay: userData.examEndDay,//考试结束前
                examEndHour: userData.examEndHour,//小时
                examEndMinute: userData.examEndMinute,//分钟
                gapDay: userData.gapDay,//天
                getHour: userData.getHour,//小时
                gapMinute: userData.gapMinute,//分钟
                msg: userData.msg,//短信内容
                // 测验介绍
                readMinute: userData.readTime?.time,//请输入阅读倒计时
                unit: userData.readTime?.unit ? userData.readTime.unit : 'minute',
                notices: JSON.parse(userData?.notice)?.stringValue,//考生须知
                rules: JSON.parse(userData?.rule)?.stringValue,//考场规则
                guides: JSON.parse(userData?.guide)?.stringValue,//操作指南
                // guide: userData.guide,//
            });
            if (userData.msgConfig === 1) {
                setSelectedValues([1]); // 选中考试开始前
            } else if (userData.msgConfig === 2) {
                setSelectedValues([2]); // 选中考试结束前
            } else if (userData.msgConfig === 3) {
                setSelectedValues([1, 2]); // 两个都选中
            }
            setTeacher(userData.readingTeacher)
            setsTudent(userData.examUser)
        }
    };

    const getVoByIdsAPI = async (id: any) => {
        const res = await Contract.getVoById(id);
        if (res.status === 200) {
            settestState(res.data.isIssue)
        }
    };

    const [noticeContent, setNoticeContent] = useState<string>(''); // 用于存储考生须知的内容
    const [rulesContent, setrulesContent] = useState<string>(''); // 用于存储考生须知的内容
    const [guidesContent, setnoticesContent] = useState<string>(''); // 用于存储考生须知的内容
    const handleNoticeChange = (e: React.ChangeEvent<HTMLTextAreaElement>, title: string) => {

        if (title == 'notices') {
            setNoticeContent(e.target.value); // 更新考生须知内容
        } else if (title == 'rules') {
            setrulesContent(e.target.value)
        } else {
            setnoticesContent(e.target.value)
        }
    };

    const builds = (id: any) => {
        const storedData = localStorage.getItem("userData");
        if (storedData) {
            gzao(JSON.parse(storedData))  // 计算总分
        } else {
            const userDatas = {
                'classifyId': id,
                "readingTeacher": [],
                "part": [],
                "examUser": [],
            }
            setUserData(userDatas);
            gzao(userDatas)
            localStorage.setItem('userData', JSON.stringify(userDatas));  // 将默认数据存储到本地
        }
    };
    const [tataol, settotalScore] = useState('')
    const gzao = async (data: any) => {
        // 计算总分数
        settestState(data.isIssue)
        seteditID(data.id)
        const totalScore = data?.part?.reduce((sum: number, part: any) => {
            if (!part?.questionList) return sum; // 如果没有 questionList，跳过该部分

            return sum + part?.questionList?.reduce((partSum: number, question: any) => {
                if (question.questions_type === 5 && Array.isArray(question.groupQuestions)) {
                    const groupScore = question.groupQuestions.reduce((groupSum: number, groupQuestion: any) => {
                        return groupSum + (groupQuestion.groupQuestionScore || 0); // 累加 groupQuestionScore
                    }, 0);
                    return partSum + groupScore;
                }


                return partSum + (question.score || 0);
            }, 0);
        }, 0);
        settotalScore(totalScore)


        const datas = {
            ...data,
            totalScore
        };
        setUserData(datas);
        const newArray = data?.part?.flatMap((part: any) => {
            if (!part?.questionList) {
                return [{
                    name: part.name
                }];
            }
            return part?.questionList?.map((question: any) => ({
                ...question,
                name: part.name,
            }));
        });

        const sortedArray = newArray?.sort((a: { createDate: number; }, b: { createDate: number; }) => a.createDate - b.createDate);
        // 更新 table 状态


        setTable(sortedArray);
    };


    const handleBack = () => {
        // 清空本地存储中的 userData
        localStorage.removeItem("userData");
        history.goBack();
    };

    const release = () => {
        if (testState) {
            Modal.confirm({
                content: '确定是否取消发布？',
                // title: '提示',
                icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
                onOk: () => {
                    cancelAPI(editID)
                },
            });
        } else {
            Modal.confirm({
                icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
                title: '提示',
                content: '发布后学员即可选择该测验进行答题',
                okText: '发布',
                cancelText: '取消',
                centered: true,
                onOk: () => {
                    cance(editID)
                },
            });
        }
    };


    const issueAPI = async (id: any, updatedValues: any) => {
        const res = await Contract.issue(id);
        if (res.status === 200) {
            // name
            message.success("发布成功！");
            getVoAPI(id, updatedValues)

        }
    };
    const getVoAPI = async (id: any, updatedValues: any) => { //详情接口
        const res = await Contract.getVoById(id);
        if (res.status === 200) {
            const updatedData = {
                ...res.data,
                ...updatedValues
            };
            SavesAPI(updatedData);
        }
    }

    const cance = async (editID: any) => {
        // form
        //     .validateFields()
        //     .then((values) => {
        //         const updatedValues = {
        //             ...userData,
        //             name: values.testName, // 名称
        //         };
        //         console.log(updatedValues, 'updatedValues');

        //         issueAPI(editID, values.testName)

        //     })
        //     .catch((error) => {
        //         message.error("输入测验名称");
        //     });


        form
            .validateFields() // 确保表单验证通过
            .then((values) => {
                const result = values.examMode ? handleExamMode(values.examMode) : null;
                // console.log("表单数据: ", values, result, opendates, selectedValues, getResult());
                const notice = {
                    "stringValue": values.notices,
                    "address": operationGuideFile ? operationGuideFile : ''
                }
                const rule = {
                    "stringValue": values.rules,
                    "address": examRulesFile ? examRulesFile : ''
                }
                const guide = {
                    "stringValue": values.guides,
                    "address": candidateNoticeFile ? candidateNoticeFile : ''
                }
                var readTime = {
                    "time": values.readMinute,
                    "unit": values.unit
                }
                const updatedValues = {
                    // ...userData,
                    ...values,
                    readTime: readTime,
                    notice: JSON.stringify(notice),  //考生须知
                    rule: JSON.stringify(rule), //考场规则
                    guide: JSON.stringify(guide),// 操作指南
                    name: values.testName, // 名称
                    classifyId: userID,
                    cover: cover, //封面
                    difficulty: values.difficulty * 1,// 难度
                    multipleChoiceScoreConfig: values.multipleChoiceScoreConfig ? values.multipleChoiceScoreConfig : 1, // 多选题得分设置
                    questionGroupScoreConfig: values.questionGroupScoreConfig ? values.questionGroupScoreConfig : 1,
                    fillScoreConfig: values.fillScoreConfig ? values.fillScoreConfig : 1,
                    examFrequency: userData.examFrequency ? values.examFrequency : true,//考试次数

                    readingTeacher: teacher,//阅卷老师
                    // 测验设置
                    examUser: setstudent, // 考试人员
                    openStartDate: opendates[0] || '', // 开放开始时间
                    openEndDate: opendates[1] || '', // 开放结束时间
                    resitStartDate: repairdates[0] || '',// 补考开始时间
                    resitEndDate: repairdates[1] || '',// 补考结束时间,
                    explainFile: explainFile,
                    examMode: result || '',
                    msgConfig: getResult()
                };

                // SaveAPI(updatedValues);
                issueAPI(editID, updatedValues)
            })
            .catch((error) => {
                message.error("输入测验名称");
            });

    };

    const SavesAPI = async (data: any) => {

        const res = await Contract.testSave(data);
        if (res.status === 200) {
            localStorage.setItem("userData", JSON.stringify(res.data));
            setUserData(res.data);
        }
    };

    const cancelAPI = async (dataSource: any) => {
        const res = await Contract.cancel(dataSource);
        if (res.status === 200) {
            getVoByIdsAPI(dataSource)
            message.success("取消成功！");
        }
    };



    const SaveAPI = async (data: any) => {
        const res = await Contract.testSave(data);
        if (res.status === 200) {

            localStorage.setItem("userData", JSON.stringify(res.data));  // 将对象转化为字符串
            setUserData(res.data);
        }
        message.success(res.message);
    };

    // 弹窗
    const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
    const [open, setOpen] = useState(false);
    const userRef: any = useRef({});
    const [relativeShow, setRelativeShow] = useState<boolean>(false); //关联人员
    const [roleCodeoleCode, setRoleCode] = useState('r_teacher')
    // 表单
    const [teacher, setTeacher] = useState<any[]>([]);
    const [setstudent, setsTudent] = useState<any[]>([]);


    const personnelShow = (roleCode: any) => {

        setRoleCode(roleCode)
        setOpen(true)
    }

    // 拼接老师姓名显示在 Input 中
    const teacherNames = () => {
        return teacher.length > 0 ? teacher.map((t: any) => t.name).join(' , ') : '';
    };

    const setsNames = () => {
        return setstudent.length > 0 ? setstudent.map((t: any) => t.name).join(' , ') : '';
    };


    const transformData = (data: any[]) => {
        return data.map((item) => {
            const org = item.organizations?.reduce((acc: any, orgItem: any) => {
                acc[orgItem.code] = orgItem.name;
                return acc;
            }, {});
            return {
                id: item.id,
                phone: item.phone || '',
                name: item.nick_name,
                code: item.user_code,
                org: org
            };
        });
    };

    const handleOk = () => {
        userRef.current.bindUpdate && userRef.current.bindUpdate();
        setConfirmLoading(true);
        const loaselectinfo = userRef.current.getLoaSelectInfo();
        console.log(loaselectinfo,'loaselectinfo');
        
        const transformedData = transformData(loaselectinfo.data);
        if (loaselectinfo.roleCode == 'r_teacher') {
            setTeacher(transformedData)
            form.setFieldsValue({ teacher: teacherNames() });
        } else {
            setsTudent(transformedData)
            form.setFieldsValue({ setstudent: setsNames() });
        }
        setOpen(false);
        setConfirmLoading(false);
    };


    const handleCancel = () => {
        setOpen(false);
    };

    // 初始化教师姓名
    useEffect(() => {
        form.setFieldsValue({ teacher: teacherNames() });
        form.setFieldsValue({ setstudent: setsNames() });
    }, [teacher, setstudent, form]);


    const [opendates, setOpenDates] = useState<[number | null, number | null]>([null, null]); // 开放时间
    const [repairdates, setRepairDates] = useState<[number | null, number | null]>([null, null]); // 补开时间

    // 用于处理日期转换和更新状态的通用函数
    const updateDate = (dates: any, setter: React.Dispatch<React.SetStateAction<[number | null, number | null]>>) => {
        const [startDate, endDate] = dates;
        const startTimestamp = startDate ? startDate.valueOf() : null;
        const endTimestamp = endDate ? endDate.valueOf() : null;
        setter([startTimestamp, endTimestamp]);

    };

    // 处理日期变化的通用函数
    const handleDateChange = (fieldName: string, dates: any) => {

        if (fieldName == '开放时间') {
            updateDate(dates, setOpenDates);
            const openStartDate = moment(dates[0]).format('YYYY-MM-DD');
            const openEndDate = moment(dates[1]).format('YYYY-MM-DD');
            setopenStartDate(openStartDate)
            setopenEndDate(openEndDate)
        } else if (fieldName == '补考开始时间') {
            updateDate(dates, setRepairDates);
            const openStartDate = moment(dates[0]).format('YYYY-MM-DD');
            const openEndDate = moment(dates[1]).format('YYYY-MM-DD');
            setresitStartDate(openStartDate)
            setresitEndDate(openEndDate)
        }
    };

    const handleExamMode = (examMode: string | any[]) => {
        // 如果 examMode 是 [1, 2]，返回 3
        if (examMode.length === 2) {
            return 3;
        }
        // 如果 examMode 是 [1] 或 [2]，返回对应的值
        return examMode[0];
    };

    const [selectedValues, setSelectedValues] = useState<number[]>([]); // 存储选中的值

    // 处理选中状态变化
    const handleChange = (value: number) => {
        setSelectedValues((prev) => {
            if (prev.includes(value)) {
                return prev.filter((item) => item !== value);
            } else {
                return [...prev, value];
            }
        });
    };

    // 计算最终结果
    const getResult = () => {
        if (selectedValues.includes(1) && selectedValues.includes(2)) return 3;
        if (selectedValues.includes(1)) return 1;
        if (selectedValues.includes(2)) return 2;
        return ''; // 默认未选中任何项
    };
    const [Unlimitedstate, setUnlimitedstate] = useState(true)
    const [OpeningHours, setOpeningHours] = useState(false)
    const [Allstaff, setAllstaff] = useState(false)
    const [Makeupsetting, setMakeupsetting] = useState(false)


    const Unlimited = (vaule: any) => {

        if (vaule == '开放时间') {
            setOpenDates([null, null])
            setUnlimitedstate((prev) => !prev);
            setopenStartDate('Invalid date')
            setopenEndDate('Invalid date')
            getjoin(null) //  补考开始时间
            //  补考开始时间 状态
            setresitStartDate('Invalid date')
            setresitEndDate('Invalid date')
        } else if (vaule == '考试人员') {
            setAllstaff((prev) => !prev);
            setsTudent([])
        } else if (vaule == '补考设置') {
            setMakeupsetting((prev) => !prev);
            // setsTudent([])
            // 补考开始时间 状态
            getjoin(null)
            setresitStartDate('Invalid date')
            setresitEndDate('Invalid date')
            // 
        } else {
            setOpeningHours((prev) => !prev);
            setRepairDates([null, null])
            setresitStartDate('Invalid date')
            setresitEndDate('Invalid date')
        }
    };

    const joinsealc = (e: any) => {
        getjoin(e.target.value)
        // setMakeupsetting((prev) => !prev);
    };

    const handleSave = () => {

        form
            .validateFields() // 确保表单验证通过
            .then((values) => {
                const result = values.examMode ? handleExamMode(values.examMode) : null;
                // console.log("表单数据: ", values, result, opendates, selectedValues, getResult());
                const notice = {
                    "stringValue": values.notices,
                    "address": operationGuideFile ? operationGuideFile : ''
                }
                const rule = {
                    "stringValue": values.rules,
                    "address": examRulesFile ? examRulesFile : ''
                }
                const guide = {
                    "stringValue": values.guides,
                    "address": candidateNoticeFile ? candidateNoticeFile : ''
                }
                var readTime = {
                    "time": values.readMinute,
                    "unit": values.unit
                }
                const updatedValues = {
                    ...userData,
                    ...values,
                    readTime: readTime,
                    notice: JSON.stringify(notice),  //考生须知
                    rule: JSON.stringify(rule), //考场规则
                    guide: JSON.stringify(guide),// 操作指南
                    name: values.testName, // 名称
                    classifyId: userID,
                    cover: cover, //封面
                    difficulty: values.difficulty * 1,// 难度
                    multipleChoiceScoreConfig: values.multipleChoiceScoreConfig ? values.multipleChoiceScoreConfig : 1, // 多选题得分设置
                    questionGroupScoreConfig: values.questionGroupScoreConfig ? values.questionGroupScoreConfig : 1,
                    fillScoreConfig: values.fillScoreConfig ? values.fillScoreConfig : 1,
                    examFrequency: userData.examFrequency ? values.examFrequency : true,//考试次数

                    readingTeacher: teacher,//阅卷老师
                    // 测验设置
                    examUser: setstudent, // 考试人员
                    openStartDate: opendates[0] || '', // 开放开始时间
                    openEndDate: opendates[1] || '', // 开放结束时间
                    resitStartDate: repairdates[0] || '',// 补考开始时间
                    resitEndDate: repairdates[1] || '',// 补考结束时间,
                    explainFile: explainFile,
                    examMode: result || '',
                    msgConfig: getResult()
                };

                SaveAPI(updatedValues);
            })
            .catch((error) => {
                console.error("表单验证失败:", error);
                message.error("表单验证失败，请检查输入");
            });
    };


    //图片文件

    const compressImage = (file: any, success: any) => {
        // 图片小于2M不压缩
        if (file.size < Math.pow(1024, 2) * 2) {
            return success(file);
        }
        const name = file.name; //文件名
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = (e: any) => {
            const src = e.target.result;
            const img = new Image();
            img.src = src;
            img.onload = (e: any) => {
                const w = img.width;
                const h = img.height;
                const quality = ((Math.pow(1024, 2) * 2) / file.size).toFixed(1); // 默认图片质量为0.92
                // 生成canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                // 创建属性节点
                const anw = document.createAttribute('width');
                anw.nodeValue = w.toString();
                const anh = document.createAttribute('height');
                anh.nodeValue = h.toString();
                canvas.setAttributeNode(anw);
                canvas.setAttributeNode(anh);

                // 铺底色 PNG转JPEG时透明区域会变黑色
                (ctx as CanvasRenderingContext2D).fillStyle = '#fff';
                (ctx as CanvasRenderingContext2D).fillRect(0, 0, w, h);

                (ctx as CanvasRenderingContext2D).drawImage(img, 0, 0, w, h);
                // quality值越小，所绘制出的图像越模糊
                const base64 = canvas.toDataURL('image/jpeg', quality); // 图片格式jpeg或webp可以选0-1质量区间

                // 返回base64转blob的值
                // 去掉url的头，并转换为byte
                const bytes = window.atob(base64.split(',')[1]);
                // 处理异常,将ascii码小于0的转换为大于0
                const ab = new ArrayBuffer(bytes.length);
                const ia = new Uint8Array(ab);
                for (let i = 0; i < bytes.length; i++) {
                    ia[i] = bytes.charCodeAt(i);
                }
                file = new Blob([ab], { type: 'image/jpeg' });
                file.name = name;
                success(file);
            };
        };
    };

    const beforeUpload = (file: any) => {
        return new Promise((resolve) => {
            compressImage(file, (newFile: any) => {
                resolve(newFile);
            });
        });
    };

    const onChange = (info: any) => {

        setUploadLoading(true);
        if (info.file.status !== 'uploading') {
        }
        if (info.file.status === 'done') {
            setUploadLoading(false);
            if (info.file.response.message === 'OK') {
                // 图片的地址
                setCover(info.file.response.data.httpPath);
                message.success(`封面上传成功！`);
            } else {
                message.error(info.file.response.message);
            }
        } else if (info.file.status === 'error') {
            setUploadLoading(false);
            message.error(`封面上传失败！`);
        }
    };

    const restoreDefault = () => {
        setCover(defaultCover);
    };


    const trateprops: UploadProps = {
        name: 'file',
        multiple: true,
        action: '/learn/v1/upload/fileupload',
        onChange(info) {
            setUploadLoading(true);
            if (info.file.status !== 'uploading') {
            }
            if (info.file.status === 'done') {
                setUploadLoading(false);
                if (info.file.response.message === 'OK') {
                    setexplainFile(info.file.response.data.httpPath);
                    message.success(`上传成功！`);
                } else {
                    message.error(info.file.response.message);
                }
            } else if (info.file.status === 'error') {
                setUploadLoading(false);
                message.error(`上传失败！`);
            }
        },
        onDrop(e) {
            console.log('Dropped files', e.dataTransfer.files);
        },
        showUploadList: false,
    };

    // 设置上传的文件属性
    const createUploadProps = (setFile: React.Dispatch<React.SetStateAction<string | null>>): UploadProps => ({
        name: 'file',
        multiple: false, // 每次只能上传一个文件
        action: '/learn/v1/upload/fileupload', // 上传接口地址
        beforeUpload(file) {
            // 'application/pdf ,'
            const allowedFileTypes = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
            const isAllowedType = allowedFileTypes.includes(file.type);
            if (!isAllowedType) {
                message.error('只能上传 Word!'); //  或 PDF 文件
                return false; // 阻止上传
            }
            return true; // 允许上传
        },
        onChange(info) {
            if (info.file.status === 'uploading') {
                return; // 上传中，不做任何处理
            }
            if (info.file.status === 'done') {
                if (info.file.response.message === 'OK') {
                    setFile(info.file.response.data.httpPath); // 更新文件路径

                    message.success('上传成功!');
                } else {
                    message.error(info.file.response.message);
                }
            } else if (info.file.status === 'error') {
                message.error('上传失败!');
            }
        },
        onDrop(e) {
            console.log('Dropped files', e.dataTransfer.files);
        },
        onRemove() {
            setFile(null); // 清空文件状态
        },
        showUploadList: false,
    });

    // 默认 
    const [notify, getnotify] = useState(3);  // 1关闭 2显示 默认消息设置关闭
    const handleRadioChange = (e: any) => {
        getnotify(e.target.value)
    };

    // test
    // 隐藏状态
    const [TestShow, setTestShow] = useState(true); //测验
    const [IntroduceShow, setIntroduceShow] = useState(true); //介绍
    const [IllustrateShow, setIllustrate] = useState(true);//说明
    const [expanded, setExpanded] = useState({
        quizSettings: false,
        quizIntroduction: false,
        quizDescription: false,
    });


    const handleToggle = (section: string | number) => {
        setExpanded((prev) => ({
            ...prev,
            [section]: !prev[section],
        }));
    };

    const StatusChange = (status: any) => {
        if (status === '测验设置') {
            handleToggle('quizSettings');
            setTestShow(prevTestShow => !prevTestShow)
        } else if (status === '介绍') {
            handleToggle('quizIntroduction');
            setIntroduceShow(prevTestShow => !prevTestShow)
        } else if (status === '说明') {
            handleToggle('quizDescription');
            setIllustrate(prevTestShow => !prevTestShow)
        } else {

        }
    }

    // 弹框
    const [addType, setAddType] = useState<string>('checkbox');
    const currentReplace = useRef<any>(null);
    const [list, setList] = useState<any>([]); //试卷题目列表
    const [Replace, setReplace] = useState(''); //题库项状态
    const [Switchitem, setSwitchitem] = useState(); //切换题库项
    const [partId, setpartIds] = useState('');

    const [selectedValuestwo, setselectedValuetwo] = useState([]);


    const onOk = (data: any) => {
        const newData = data.map((item: any) => ({ ...item, editable: false }));

        if (addType === 'radio') {
            //替换题目
            const tmep = JSON.parse(JSON.stringify(list));
            const index = tmep.findIndex(
                (x: any) => x.id === currentReplace.current.id,
            );
            tmep[index] = newData[0];
            setList(tmep);
        } else {
            //保留原数据分值
            const tmep = JSON.parse(JSON.stringify(list));
            newData.forEach((item_: any, index: number) => {
                //已保存过的数据的parent_id与之对应 新勾选的id与之对应
                if (
                    tmep.findIndex(
                        (x: any) => x.id === item_.id || x.parent_id === item_.id,
                    ) === -1
                ) {
                    tmep.push(item_);
                }
            });
            setList(tmep);
        }
        setTopicVisible(false);
    };

    // 当弹框数据修改时，更新表格和父组件数据
    const handleUpdateData = (newUserData: any) => {
        // setpartIds('')
        updateUserData(newUserData);  // 通知父组件更新数据
    };

    const toggleTopicVisibility = (isVisible: any, partIds: string, selectedValue: any,) => {
        setTopicVisible(isVisible);
        setpartIds('')
        setpartIds(partIds)
        // settitle(title)
        setselectedValuetwo(selectedValue)
    };

    const extractFileName = (path: string) => {
        // 使用正则表达式提取 `_` 后面的部分
        const match = path.match(/_([^/]+)$/); // 这里捕获 `_` 后面直到路径结束的所有内容
        return match ? match[1] : ''; // 如果匹配到，返回捕获到的部分；否则返回空字符串
    };

    const downloadExcel = async () => {
        const res = await Contract.checkexcel(data);
        if (res.status === 200) {
            settestState(res.data.isIssue)
        }
    }

    const uploadProps = {
        name: 'file',
        accept: '.xlsx,.xls',
        showUploadList: false,
        customRequest: async (options: any) => {
          const { file, onSuccess, onError } = options;
          const formData = new FormData();
          formData.append('file', file);
      
          try {
            const res = await Contract.checkexcel(formData); // 传递 FormData
            if (res.status === 200) {
              message.success('校验成功');
              // 合并后去重，code重复的只保留一个
              setsTudent((prev: any[]) => {
                const merged = [...prev, ...res.data];
                const unique = merged.filter(
                  (item, index, self) =>
                    item.code &&
                    self.findIndex(i => i.code === item.code) === index
                );
                return unique;
              })
            } else {
              message.error('校验失败');
            }
            onSuccess && onSuccess(res, file);
          } catch (err) {
            message.error('上传失败');
            onError && onError(err);
          }
        },
      };

    return (
        <Layout className="layout-container">
            {/* 顶部 Header */}
            <Header className="header-container">
                <div onClick={handleBack} className="header-title">
                    <ArrowLeftOutlined className="arrow-icon" />
                    <span>新建测验</span>
                </div>
                <div className="header-buttons">
                    {testState !== undefined && (
                        <Button onClick={release} type="default">
                            {testState ? '取消发布' : '发布'}
                        </Button>
                    )}
                    <Button onClick={handleBack} type="default">关闭</Button>
                    <Button onClick={handleSave} type="primary">保存</Button>
                </div>
            </Header>

            <Layout className="main-layout">
                {/* 左侧导航 */}
                <Sider width={200} className="sider-container">
                    <Sidebar onMenuItemClick={scrollToSection} />
                </Sider>

                {/* 内容区域 */}
                <Content className="content-container">
                    {/* 基本信息模块 */}
                    <div ref={(el) => (sectionRefs.current["basicInfo"] = el)} className="module-section">
                        <div className="flexs">
                            <div className="jux"></div>
                            <div>测验基本信息</div>
                        </div>
                        <Form
                            form={form}
                            layout="horizontal"
                            labelCol={{ flex: "110px" }}
                            wrapperCol={{ flex: 1 }}
                            labelAlign="left"
                            colon={false}
                            initialValues={{
                                multipleChoiceScoreConfig: userData?.multipleChoiceScoreConfig,
                                questionGroupScoreConfig: userData?.questionGroupScoreConfig,
                                fillScoreConfig: userData?.fillScoreConfig,
                                isResit: userData?.isResit ? userData?.isResit : false,
                                examFrequency: userData?.examFrequency ? userData?.examFrequency : true,
                                examMode: userData.examMode != null
                                    ? userData.examMode === 3
                                        ? [1, 2]
                                        : [userData.examMode]
                                    : [0],
                            }}
                        >
                            <Form.Item label="测验名称" name="testName" rules={[{ required: true, message: "请输入测验名称" }]}>
                                <Input placeholder="请输入测验名称" />
                            </Form.Item>
                            <Form.Item label="测验封面">
                                <div className="upload-section">
                                    <div className="img-box">
                                        <Img src={cover} />
                                    </div>

                                </div>

                                <div className="Cover" >
                                    <p className="upload-hint">
                                        建议图片比例16:9，支持jpg、jpeg、png，图片大小不超过2M
                                    </p>
                                    <div className="upload-buttons">
                                        <Upload
                                            accept=".png,.jpg,.jpeg"
                                            action="/learn/v1/upload/fileupload"
                                            showUploadList={false}
                                            beforeUpload={beforeUpload as any}
                                            onChange={onChange}
                                        >
                                            <Button type="primary" shape="round" style={{ margin: '0 20px' }} icon={uploadLoading ? <LoadingOutlined /> : ''}>
                                                选择图片
                                            </Button>
                                        </Upload>
                                        <Button onClick={restoreDefault} type="primary" shape="round" >

                                            恢复默认
                                        </Button>
                                    </div>
                                </div>
                            </Form.Item>

                            <Form.Item label="试卷难度" name="difficulty">
                                <Select >
                                    <Option value="1">1</Option>
                                    <Option value="2">2</Option>
                                    <Option value="3">3</Option>
                                    <Option value="4">4</Option>
                                    <Option value="5">5</Option>
                                </Select>
                            </Form.Item>

                            <Form.Item label="阅卷老师" name="teacher">
                                <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
                                    <Input value={teacherNames()} placeholder="请输入老师姓名" />
                                    <Button onClick={() => personnelShow('r_teacher')} type="dashed">+ 阅卷老师</Button>
                                </div>
                            </Form.Item>
                        </Form>
                    </div>

                    {/* 测验题目模块 */}
                    <div ref={(el) => (sectionRefs.current["testQuestions"] = el)} className="module-section">
                        {/* <h2>测验题目</h2> */}
                        {/* setTopicVisible(false) */}
                        <Tables
                            userTable={userTable}
                            userDatas={userData}
                            tataol={tataol}
                            updateUserData={updateUserData}
                            classifyID={editID}
                            userID={userID}  // classifyId
                            itemName={itemName}
                            ppsucAction={parameterConfig.target_customer !== 'ppsuc'}
                            toggleTopicVisibility={toggleTopicVisibility}
                        />
                    </div>

                    {/* 测验设置模块 */}
                    <div ref={(el) => (sectionRefs.current["testSettings"] = el)} className="module-section">
                        <div className="flexs" onClick={() => StatusChange('测验设置')}  >
                            <div className="jux"></div>
                            <div>测验设置</div>
                            {expanded.quizSettings ? (
                                <CaretUpOutlined style={{ paddingLeft: '3px', paddingTop: '4px', fontSize: '16px' }} />
                            ) : (
                                <CaretDownOutlined style={{ paddingLeft: '3px', paddingTop: '4px', fontSize: '16px' }} />
                            )}
                        </div>
                        {TestShow &&
                            <Form form={form} layout="horizontal" labelCol={{ flex: "110px" }} wrapperCol={{ flex: 1 }} labelAlign="left" colon={false}>

                                {parameterConfig.target_customer !== 'ppsuc' &&
                                    <div className="Scoresettings">
                                        <div className="settings_right" >
                                            <span className="sptext" >判分设置</span>
                                            <div style={{ marginLeft: '56px' }}>
                                                题组得分设置
                                                <span className="setting" onClick={() => setTopicVisible(true)}   >点击设置</span>
                                            </div>
                                        </div>
                                        <div style={{ marginLeft: '115px' }}>
                                            <Form.Item label="多选题得分设置" name="multipleChoiceScoreConfig">
                                                <Radio.Group defaultValue={userData?.multipleChoiceScoreConfig ? userData?.multipleChoiceScoreConfig : 1}>
                                                    <Radio value={1}>全部得分</Radio>
                                                    <Radio value={2}>部分正确得分</Radio>
                                                </Radio.Group>
                                            </Form.Item>
                                        </div>
                                        <div style={{ marginLeft: '115px' }}>
                                            <Form.Item label="填空题分设置" name="fillScoreConfig">
                                                <Radio.Group defaultValue={userData?.fillScoreConfig ? userData?.fillScoreConfig : 1}>
                                                    <Radio value={1}>全部得分</Radio>
                                                    <Radio value={2}>部分正确得分</Radio>
                                                </Radio.Group>
                                            </Form.Item>
                                        </div>
                                    </div>
                                }


                                <div className="Scoresettings">
                                    <div className="settings_right" >
                                        <span className="sptext" >答题设置</span>
                                        <div style={{ marginLeft: '56px' }}>

                                        </div>
                                    </div>
                                    <div style={{ marginLeft: '115px', marginTop: '-47px' }}>

                                        <Form.Item label="考试次数" name="examFrequency" defaultValue={userData?.examFrequency ? userData?.examFrequency : true}>
                                            <Radio.Group>
                                                <Radio value={true}>允许多次提交</Radio>
                                                <Radio value={false}>禁止多次提交</Radio>
                                            </Radio.Group>
                                        </Form.Item>

                                        <Form.Item name="examMode" label="考试方式">
                                            <Checkbox.Group>
                                                <Checkbox value={1}>部分内打乱试题顺序</Checkbox>
                                                <Checkbox value={2}>部分间打乱组成部分顺序</Checkbox>
                                            </Checkbox.Group>
                                        </Form.Item>
                                    </div>
                                </div>
                                <div className="Fractions"  >
                                    <Form.Item rules={[
                                        // { message: '请输入及格分数' },
                                        { pattern: /^[0-9]+$/, message: '请输入正整数' } // 校验正整数
                                    ]} label="及格分数" name="passScore" >
                                        <Input placeholder="请输入及格分数" />
                                    </Form.Item>
                                    <Form.Item label="良好分数" name="fineScore"
                                        rules={[
                                            // { message: '请输入良好分数' },
                                            { pattern: /^[0-9]+$/, message: '请输入正整数' } // 校验正整数
                                        ]}
                                    >
                                        <Input placeholder="请输入良好分数" />
                                    </Form.Item>
                                    <Form.Item label="优秀分数" name="goodScore"
                                        rules={[
                                            // { message: '请输入优秀分数' },
                                            { pattern: /^[0-9]+$/, message: '请输入正整数' } // 校验正整数
                                        ]}
                                    >
                                        <Input placeholder="请输入优秀分数" />
                                    </Form.Item>
                                </div>

                                <Form.Item label="开放时间">
                                    <span style={{}} >
                                        <Checkbox defaultChecked={true} disabled={Featuredstate} onChange={() => Unlimited('开放时间')} >不限</Checkbox>
                                    </span>
                                    <DatePicker.RangePicker
                                        onChange={(dates) => handleDateChange('开放时间', dates)}
                                        format="YYYY-MM-DD"
                                        value={[
                                            openStartDate == 'Invalid date' ? null : moment(openStartDate),
                                            openEndDate == 'Invalid date' ? null : moment(openEndDate)
                                        ]}
                                        disabled={Featuredstate === true || (Featuredstate === false && Unlimitedstate === true)}
                                    />
                                </Form.Item>
                                {/* <div className="Fractions" > */}


                                {openStartDate != 'Invalid date' && (
                                    <Form.Item
                                        label="补考设置" name="isResit" defaultValue={userData?.isResit ? userData?.isResit : false}>
                                        {/* <span style={{}} >
                                            <Checkbox onChange={() => Unlimited('补考设置')}  >不限</Checkbox>
                                        </span> */}
                                        <Radio.Group
                                            disabled={Featuredstate}
                                            // disabled={Featuredstate === true || (Featuredstate === false && Makeupsetting === true)}
                                            onChange={joinsealc}>
                                            <Radio value={false}>不允许</Radio>
                                            <Radio value={true}>允许不及格或未参加的学员重新参加考试</Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                )}


                                {joinfy && (
                                    <Form.Item label="补考开始时间">
                                        <span style={{}} >
                                            <Checkbox disabled={Featuredstate} onChange={() => Unlimited('补考开始时间')} >不限</Checkbox>
                                        </span>
                                        <DatePicker.RangePicker
                                            onChange={(dates) => handleDateChange('补考开始时间', dates)}
                                            format="YYYY-MM-DD"
                                            // disabled={Featuredstate}
                                            disabled={Featuredstate === true || (Featuredstate === false && OpeningHours === true)}
                                            value={[
                                                resitStartDate === 'Invalid date' ? null : moment(resitStartDate),
                                                resitEndDate === 'Invalid date' ? null : moment(resitEndDate)
                                            ]}
                                        />
                                    </Form.Item>
                                )}
                                {/* <Form.Item label="补考开始时间">
                                    <DatePicker.RangePicker
                                        onChange={(dates) => handleDateChange('补考开始时间', dates)}
                                        format="YYYY-MM-DD"
                                        disabled={Featuredstate}
                                        value={[
                                            resitStartDate == 'Invalid date' ? null : moment(resitStartDate),
                                            resitEndDate == 'Invalid date' ? null : moment(resitEndDate)
                                        ]}
                                    />
                                </Form.Item> */}
                            </Form>
                        }
                    </div>

                    {/* 学员设置 */}
                    <div ref={(el) => (sectionRefs.current["StudentSettings"] = el)} className="module-section">
                        <div className="flexs">
                            <div className="jux"></div>
                            <div>学员设置</div>
                        </div>
                        <Form form={form} layout="horizontal" labelCol={{ flex: "110px" }} wrapperCol={{ flex: 1 }} labelAlign="left" colon={false}>

                            <Form.Item label="考试人员" name="setstudent">
                                <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
                                    <span >
                                        <Checkbox
                                            checked={setsNames() === ''}
                                            onChange={() => Unlimited('考试人员')}
                                        >全员</Checkbox>
                                    </span>
                                    <Input disabled={Featuredstate === true || (Featuredstate === false && Allstaff === true)}
                                        value={setsNames()} placeholder="请输入考试人员" />
                                    <Button disabled={Featuredstate === true || (Featuredstate === false && Allstaff === true)} type="dashed" onClick={() => personnelShow('r_student')} >+ 考试人员</Button>
                                    {/* <Button type="dashed" onClick={() => downloadExcel()} >导入学生</Button> */}
                                    <Upload {...uploadProps}>
                                        <Button icon={<UploadOutlined />}>导入学生</Button>
                                    </Upload>
                                </div>
                            </Form.Item>
                        </Form>


                    </div>


                    {/* 测试通知 */}
                    <div ref={(el) => (sectionRefs.current["testNotification"] = el)} className="module-section">

                        <div className="flexs">
                            <div className="jux"></div>
                            <div>测试通知</div>
                        </div>
                        <Form form={form}
                            initialValues={{
                                notifyType: notify, // 默认选中“系统消息通知”
                            }}
                            layout="horizontal"
                            labelCol={{ flex: "110px" }}
                            wrapperCol={{ flex: 1 }}
                            labelAlign="left"
                            colon={false}>
                            <Form.Item label="通知方式" name="notifyType">
                                <Radio.Group disabled={Featuredstate} onChange={handleRadioChange} >
                                    <Radio value={1}>系统消息通知</Radio>
                                    {/* <Radio value={2}>手机短信通知</Radio> */}
                                    <Radio value={3}>不通知</Radio>
                                </Radio.Group>
                            </Form.Item>

                            {notify == 2 && (
                                <div>
                                    <Row gutter={16}>
                                        <Col >消息设置</Col>
                                        <Col span={20}>
                                            <Row gutter={16}>
                                                {/* 考试开始前 */}
                                                <Col span={24} style={{ paddingLeft: '44px' }} >
                                                    <Form.Item
                                                        name="examBeforeChecked" valuePropName="checked" noStyle>
                                                        <Checkbox
                                                            checked={selectedValues.includes(1)}
                                                            onChange={() => handleChange(1)}
                                                        >考试开始前</Checkbox>
                                                    </Form.Item>
                                                    <Form.Item name="examBeforeMinute"
                                                        className="information"
                                                        rules={[
                                                            { pattern: /^[0-9]+$/, message: '请输入正整数' } // 校验正整数
                                                        ]} >
                                                        <Input placeholder="分钟" suffix="分钟"
                                                        />
                                                    </Form.Item>
                                                    <span>发布考试，并通过短信推送提醒</span>
                                                </Col>
                                            </Row>
                                            <Row gutter={16} className="jsTOP" style={{ paddingLeft: '35px' }} >
                                                {/* 考试结束前 */}
                                                <Col span={24}>
                                                    <Form.Item
                                                        name="examAfterChecked"
                                                        valuePropName="checked" noStyle>
                                                        <Checkbox
                                                            checked={selectedValues.includes(2)}
                                                            onChange={() => handleChange(2)}
                                                        >考试结束前</Checkbox>
                                                    </Form.Item>
                                                    <Form.Item
                                                        className="jsflex" name="examEndDay"
                                                        rules={[
                                                            { pattern: /^[0-9]+$/, message: '请输入正整数' } // 校验正整数
                                                        ]}
                                                    >
                                                        <Input placeholder="天" suffix="天" />
                                                    </Form.Item>
                                                    <Form.Item
                                                        className="jsflex"
                                                        name="examEndHour"
                                                        rules={[
                                                            { pattern: /^[0-9]+$/, message: '请输入正整数' } // 校验正整数
                                                        ]}
                                                    >
                                                        <Input placeholder="小时" suffix="小时" />
                                                    </Form.Item>
                                                    <Form.Item
                                                        className="jsflex"
                                                        name="examEndMinute"
                                                        rules={[
                                                            { pattern: /^[0-9]+$/, message: '请输入正整数' } // 校验正整数
                                                        ]}
                                                    >
                                                        <Input placeholder="分钟" suffix="分钟" />
                                                    </Form.Item>
                                                    <span className="spansLh" >起,</span>
                                                    <span className="spansLh">每</span>
                                                    <Form.Item
                                                        className="jsflex" name="gapDay"
                                                        rules={[
                                                            { pattern: /^[0-9]+$/, message: '请输入正整数' } // 校验正整数
                                                        ]}
                                                    >
                                                        <Input placeholder="天" suffix="天" />
                                                    </Form.Item>
                                                    <Form.Item
                                                        className="jsflex ma"
                                                        name="getHour"
                                                        rules={[
                                                            { pattern: /^[0-9]+$/, message: '请输入正整数' } // 校验正整数
                                                        ]}
                                                    >
                                                        <Input placeholder="小时" suffix="小时" />
                                                    </Form.Item>
                                                    <Form.Item
                                                        className="jsflex ma"
                                                        name="gapMinute"
                                                        rules={[
                                                            { pattern: /^[0-9]+$/, message: '请输入正整数' } // 校验正整数
                                                        ]}
                                                    >
                                                        <Input placeholder="分钟" suffix="分钟" />
                                                    </Form.Item>
                                                    <span className="spans" >通过短信推送提醒未参加和可补考的人。</span>
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>

                                    <Row gutter={16} style={{ marginTop: "20px" }}>
                                        {/* 短信内容 */}
                                        <Col span={24}>
                                            <Form.Item label="短信内容" name="msg">
                                                <Input.TextArea
                                                    placeholder="'"
                                                    rows={4}
                                                />
                                            </Form.Item>
                                            <p style={{ marginLeft: '107px' }} className="upload-hint">
                                                如不填写短信内容，则默认发送 '请考生尽快前往小程序或网页端参与考试，考试入口即将关闭。
                                            </p>
                                        </Col>
                                    </Row>
                                </div>
                            )}

                        </Form>
                    </div>

                    {/* 测验介绍模块 */}
                    <div ref={(el) => (sectionRefs.current["testIntro"] = el)} className="module-section">

                        <div className="flexs" onClick={() => StatusChange('介绍')} >
                            <div className="jux"></div>
                            <div>测验介绍</div>
                            {expanded.quizIntroduction ? (
                                <CaretUpOutlined style={{ paddingLeft: '3px', paddingTop: '4px', fontSize: '16px' }} />
                            ) : (
                                <CaretDownOutlined style={{ paddingLeft: '3px', paddingTop: '4px', fontSize: '16px' }} />
                            )}
                        </div>
                        {IntroduceShow &&
                            <Form form={form} layout="horizontal" labelCol={{ flex: "110px" }} wrapperCol={{ flex: 1 }} labelAlign="left" colon={false}>

                                <div className="times" >
                                    <span style={{ paddingRight: '40px' }} >阅读倒计时</span>
                                    <Form.Item label="" name="readMinute">
                                        <Input placeholder="请输入阅读倒计时" />
                                    </Form.Item>

                                    <Form.Item style={{ marginLeft: '10px' }} label="" name="unit">
                                        <Select>
                                            <Option value="minute">分</Option>
                                            <Option value="second">秒</Option>
                                        </Select>
                                    </Form.Item>
                                </div>
                                <div style={{ display: 'flex', alignItems: 'center' }} >
                                    <span style={{ paddingRight: '54px' }} >考生须知</span>
                                    <Form.Item label="" name="notices">
                                        <Input.TextArea value={noticeContent} onChange={(e) => handleNoticeChange(e, 'notices')} disabled={candidateNoticeFile} rows={3} placeholder="请输入考生须知" />
                                    </Form.Item>
                                    <div style={{ paddingLeft: '10px' }} >
                                        <Upload disabled={noticeContent !== ''}  {...createUploadProps(setCandidateNoticeFile)} maxCount={1}>
                                            <Button disabled={noticeContent !== ''} icon={<UploadOutlined />}>上传文档(支持word)</Button>
                                            {/* 或者pdf */}
                                        </Upload>
                                        {candidateNoticeFile && (
                                            <div style={{ display: 'flex', alignItems: 'flex-end' }} >
                                                <div onClick={() => handleFilePreview(candidateNoticeFile)} >
                                                    {extractFileName(candidateNoticeFile)}
                                                </div>
                                                <div style={{ marginLeft: '5px' }} >
                                                    <Popconfirm
                                                        title="确定要删除吗?"
                                                        onConfirm={(e) => {
                                                            setCandidateNoticeFile('')
                                                        }}
                                                        okText="确定"
                                                        cancelText="取消"
                                                    >
                                                        <Image
                                                            width={16}
                                                            height={16}
                                                            src={require('@/images/icons/sc.png')}
                                                            title="删除"
                                                            preview={false}
                                                        />
                                                    </Popconfirm >
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                <div style={{ display: 'flex', alignItems: 'center' }} >
                                    <span style={{ paddingRight: '54px' }} >考场规则</span>
                                    <Form.Item label="" name="rules">
                                        <Input.TextArea value={rulesContent} rows={3} placeholder="请输入考场规则" onChange={(e) => handleNoticeChange(e, 'rules')} disabled={examRulesFile} />
                                    </Form.Item>
                                    <div style={{ paddingLeft: '10px' }} >
                                        <Upload disabled={rulesContent !== ''} {...createUploadProps(setExamRulesFile)} maxCount={1}>
                                            <Button disabled={rulesContent !== ''} icon={<UploadOutlined />}>上传文档(支持word)</Button>
                                            {/* 或者pdf */}
                                        </Upload>
                                        {examRulesFile && (
                                            <div style={{ display: 'flex', alignItems: 'flex-end' }} >
                                                <div onClick={() => handleFilePreview(examRulesFile)} >
                                                    <a>
                                                        {extractFileName(examRulesFile)}
                                                    </a>
                                                </div>
                                                <div style={{ marginLeft: '5px' }} >
                                                    <Popconfirm
                                                        title="确定要删除吗?"
                                                        onConfirm={(e) => {
                                                            setExamRulesFile('')
                                                        }}
                                                        okText="确定"
                                                        cancelText="取消"
                                                    >
                                                        <Image
                                                            width={16}
                                                            height={16}
                                                            src={require('@/images/icons/sc.png')}
                                                            title="删除"
                                                            preview={false}
                                                        />
                                                    </Popconfirm >
                                                </div>
                                            </div>


                                        )}
                                    </div>
                                </div>

                                {/* <Form.Item label="操作指南" name="guide">
                                    <Input.TextArea rows={3} placeholder="操作指南" />
                                </Form.Item> */}
                                <div style={{ display: 'flex', alignItems: 'center' }} >
                                    <span style={{ paddingRight: '54px' }} >操作指南</span>
                                    <Form.Item label="" name="guides">
                                        <Input.TextArea value={guidesContent} rows={3} onChange={(e) => handleNoticeChange(e, 'guides')} placeholder="请输入操作指南" disabled={operationGuideFile} />
                                    </Form.Item>
                                    <div style={{ paddingLeft: '10px' }} >
                                        <Upload disabled={guidesContent !== ''} {...createUploadProps(setOperationGuideFile)} maxCount={1}>
                                            <Button disabled={guidesContent !== ''} icon={<UploadOutlined />}>上传文档(支持word)</Button>
                                            {/* 或者pdf */}
                                        </Upload>

                                        {/* {operationGuideFile && (
                                            <div onClick={() => handleFilePreview(operationGuideFile)}  >
                                                <a>
                                                    {extractFileName(operationGuideFile)}
                                                </a>
                                            </div>
                                        )} */}

                                        {operationGuideFile && (
                                            <div style={{ display: 'flex', alignItems: 'flex-end' }} >
                                                <div onClick={() => handleFilePreview(operationGuideFile)} >
                                                    <a>
                                                        {extractFileName(operationGuideFile)}
                                                    </a>
                                                </div>
                                                <div style={{ marginLeft: '5px' }} >
                                                    <Popconfirm
                                                        title="确定要删除吗?"
                                                        onConfirm={(e) => {
                                                            setOperationGuideFile('')
                                                        }}
                                                        okText="确定"
                                                        cancelText="取消"
                                                    >
                                                        <Image
                                                            width={16}
                                                            height={16}
                                                            src={require('@/images/icons/sc.png')}
                                                            title="删除"
                                                            preview={false}
                                                        />
                                                    </Popconfirm >
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </Form>
                        }
                    </div>

                    {/* 测验说明 */}
                    <div ref={(el) => (sectionRefs.current["testInstructions"] = el)} className="module-section">

                        <div className="flexs" onClick={() => StatusChange('说明')} >
                            <div className="jux"></div>
                            <div>测验说明</div>
                            {expanded.quizDescription ? (
                                <CaretUpOutlined style={{ paddingLeft: '3px', paddingTop: '4px', fontSize: '16px' }} />
                            ) : (
                                <CaretDownOutlined style={{ paddingLeft: '3px', paddingTop: '4px', fontSize: '16px' }} />
                            )}
                        </div>
                        {IllustrateShow &&

                            <div>
                                <Dragger {...trateprops}>
                                    <p className="ant-upload-drag-icon">
                                        <InboxOutlined />
                                    </p>
                                    <p className="ant-upload-text">上传说明</p>
                                    <p className="ant-upload-hint">
                                        支持word
                                        {/* 或者pdf */}
                                    </p>
                                </Dragger>
                                <div style={{ marginLeft: '103px', marginTop: '20px' }} >
                                    {/* {explainFile && (
                                        <div onClick={() => handleFilePreview(explainFile)} >
                                            <a>
                                                {extractFileName(explainFile)}
                                            </a>
                                        </div>
                                    )} */}
                                    {explainFile && (
                                        <div style={{ display: 'flex', alignItems: 'flex-end' }} >
                                            <div onClick={() => handleFilePreview(explainFile)} >
                                                <a>
                                                    {extractFileName(explainFile)}
                                                </a>
                                            </div>
                                            <div style={{ marginLeft: '5px' }} >
                                                <Popconfirm
                                                    title="确定要删除吗?"
                                                    onConfirm={(e) => {
                                                        setexplainFile(null)
                                                    }}
                                                    okText="确定"
                                                    cancelText="取消"
                                                >
                                                    <Image
                                                        width={16}
                                                        height={16}
                                                        src={require('@/images/icons/sc.png')}
                                                        title="删除"
                                                        preview={false}
                                                    />
                                                </Popconfirm >
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        }


                    </div>
                </Content>
            </Layout >
            <Modal
                title={roleCodeoleCode == 'r_teacher' ? '添加老师' : '添加学员'}
                open={open}
                width={1200}
                onOk={handleOk}
                destroyOnClose={true}
                confirmLoading={confirmLoading}
                onCancel={handleCancel}
            >
                <RelativeUser
                    studentCode={roleCodeoleCode == 'r_teacher' ? teacher : setstudent}
                    ref={userRef}
                    onOk={() => setRelativeShow(false)}
                    roleCode={roleCodeoleCode}
                    insider={[]}
                />
            </Modal>

            <QuestionsModal
                visible={topicVisible}
                title={'题组判分设置'}
                partIds={partId}
                selectedValues={selectedValuestwo}
                selectkeys={addType === 'radio' ? [currentReplace.current] : list}
                ppsucAction={parameterConfig.target_customer !== 'ppsuc'}
                itemName={itemName}
                userData={userData}
                callback={onOk}
                Switchitem={Switchitem}
                classifyID={userID}
                userID={editID}
                updateData={handleUpdateData}
                onclose={() => {
                    setpartIds('') // 清空 partIds
                    setTopicVisible(false); // 关闭弹窗
                }}

            />
        </Layout >
    );
};

export default NewTest;
