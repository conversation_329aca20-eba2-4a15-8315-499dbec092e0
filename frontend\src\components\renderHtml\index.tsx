import { createMarkup } from '@/utils';
import React, { useEffect, useState } from 'react';

const RenderHtml: React.FC<any> = ({cname='',value=''}:any) => {
    //监听组件渲染完毕
    useEffect(()=>{
        setTimeout(() => {
            (window as any).MathJax.typeset()
        }, 100);
    },[value])

  // 这里不能用 createMarkup 不知道为啥会导致公式显示不全
  return (
    <div className={cname} dangerouslySetInnerHTML={{__html:value}}  ></div>
  );
};

export default RenderHtml;