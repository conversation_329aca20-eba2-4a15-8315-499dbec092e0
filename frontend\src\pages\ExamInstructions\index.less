.instruction_container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f7f9fa;
  .content_container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    .instruction_info {
      width: 53vw;
      height: 69vh;
      background: #ffffff;
      border-radius: 10px;
      padding: 30px;
      display: flex;
      flex-direction: column;
      //   justify-content: center;
      align-items: center;
      position: relative;
      .time_down {
        position: absolute;
        right: 30px;
        top: 30px;
        display: flex;
        align-items: center;
        // height: 100%;
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid #e8e8e8;
        gap: 12px;
        .time_label {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #2a2a2a;
        }
        .time_item {
          border-radius: 4px;
          background: rgba(241, 84, 74, 0.1);
          font-family: DINAlternate, DINAlternate;
          font-weight: bold;
          font-size: 18px;
          color: #f1544a;
          padding: 4px;
        }
        .time_gap {
          font-family: SourceHanSansSC, SourceHanSansSC;
          font-weight: 400;
          font-size: 18px;
          color: #2a2a2a;
        }
      }
      .test_name {
        font-weight: 600;
        font-size: 24px;
        color: #2a2a2a;
        height: 34px;
      }
      .tab_list {
        width: 100%;
        height: 48px;
        margin-top: 36px;
        display: flex;
        background: #f7f9fa;
        border-radius: 6px;
        overflow: hidden;
        .tab_item {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #2a2a2a;
          cursor: pointer;
        }
        .active {
          background: #549cff;
          color: #ffffff;
        }
      }
      .detail {
        width: 100%;
        height: calc(100% - 216px);
        margin-top: 30px;
      }
      .btn {
        width: 240px;
        height: 48px;
        margin-top: 30px;
        background: rgba(84, 156, 255, 0.1);
        border-radius: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #549cff;
        cursor: pointer;
      }
    }
  }
}
