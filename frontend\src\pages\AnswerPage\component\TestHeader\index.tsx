import { Button } from 'antd';
import { FC, forwardRef } from 'react';
import './index.less';

const TestHeader: FC<any> = forwardRef(
  (
    {
      answerMinute,
      pagesTime,
      userInfo,
      testName,
      onSubmit,
      onSave,
    },
    ref,
  ) => {
    return (
      <div className="test_header_container">
        <div className="header_left">
          <div className="test_title">{testName}</div>
          {answerMinute > 0 && (
            <div className="test_time">
              <div className="test_time_text">剩余时间</div>
              <div className="test_left_time">{pagesTime.minutes}</div>:
              <div className="test_left_time">{pagesTime.seconds}</div>
            </div>
          )}
        </div>
        <div className="header_right">
          <div className="btns">
            <Button
              style={{
                color: 'var(--primary-color)',
                border: '1px solid var(--primary-color)',
              }}
              // onClick={() => onSave()}
              onClick={async () => {
                await onSave(); // 如果 onSave 是异步的
                history.back(); // 跳转上一个页面
              }}
            >
              保存
            </Button>
            <Button type="primary" onClick={() => onSubmit()}>
              立刻交卷
            </Button>
          </div>
          <div className="tester_info">
            <img className="test_avatar" src={userInfo?.avatar} />
            <div className="test_desc">
              <div className="username">{userInfo?.nickName}</div>
              <div className="phone">{userInfo?.phone}</div>
            </div>
          </div>
        </div>
      </div>
    );
  },
);

export default TestHeader;
