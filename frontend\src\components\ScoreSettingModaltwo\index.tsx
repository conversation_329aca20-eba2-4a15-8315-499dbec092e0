import React, { FC, useState, useEffect } from 'react';
import { Table, Input, Button, Modal, message, Form, Tooltip, Radio } from 'antd';
import './index.less';
import Contract from '@/api/Contract';
import paperManageApis from '@/api/Contract';
import examManageApis from '@/api/exam';
import RenderHtml from '../renderHtml';
import examType from '@/types/examType';

interface params {
    userID: any;
    title: any;
    userData?: any;
    visibles: boolean;
    callback: (data: any) => void;
    onclose: () => void;
    updateData: (newUserData: any) => void;  // 更新数据的回调
    setup: (partIds: any, bos: boolean, selectedValue: any) => void;
}

const ScoreSettingModaltwo: React.FC<params> = ({ title, userID, visibles, updateData, callback, userData, onclose, setup }) => {
    const [form] = Form.useForm();
    const [dataSource, setDataSource] = useState(userData);
    const [selectedValue, setSelectedValue] = useState<any>({});
    const [selectedtargetValue, setSelectedtargetValue] = useState<any>({});
    const [Scoringmodes, setScoringmodes] = useState<any>(false);

    const [query, setQuery] = useState<any>({
        questions_content: undefined,
        labels: undefined,
        knowledge_points: undefined,
        questions_type: 5,
        page: 1,
        size: 10000000
    })
    const [data, setData] = useState<any>([]);
    const [total, setTotal] = useState<any>([]);
    const [selectRows, setSelectedRows] = useState<any>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
    // 应用分类|考核方向
    const [initClassify, setInitClassify] = useState<any>([]);
    const [examDirection, setExamDirection] = useState<any>([]);
    const [initQuestionSource, setInitQuestionSource] = useState<any>([]);
    const [selectRows1, setselectRows1] = useState([]);  // 用于保存状态1的选中行
    const [selectRows2, setselectRows2] = useState([]);  // 用于保存状态2的选中行
    const [Visible, setVisible] = useState(1);  // 当前可见的状态，1 或 2
    const [Status, setStatus] = useState('');  // 是确认 还是 设置

    useEffect(() => {
        setDataSource(userData);

        const transformedObject = dataSource?.part?.reduce((acc: { [x: string]: { questionGroupScoreConfig: any; }; }, item: { id: string | number; questionGroupScoreConfig: any; }) => {
            acc[item.id] = { questionGroupScoreConfig: item.questionGroupScoreConfig };
            return acc;
        }, {});

        setSelectedValue(transformedObject)

    }, [userData]);


    const handleRadioChange = (e: any, id: string, column: string) => { // 更新每一行的值
        setSelectedValue((prevValue: any) => ({
            ...prevValue,
            [id]: {
                ...prevValue[id],
                [column]: e.target.value * 1,  // 更新对应行和列的值
            },
        }));
        setSelectedtargetValue((prevValue: any) => ({
            ...prevValue,
            [id]: {
                ...prevValue[id],
                [column]: e.target.value * 1,  // 更新对应行和列的值
            },
        }));

    };

    const handleUpdate = (id: string, value: any) => {

        const updatedData = {
            ...userData,
            part: userData.part.map((item: { id: string; }) => {
                if (item.id === id) {

                    return {
                        ...item,
                        questionGroupScoreConfig: value
                    };
                }
                return item;
            })
        };

        // 更新数据
        setDataSource(updatedData);
    };


    const handleInputChange = (value: string, recordId: string, totalScore: number, field: string) => {

        const numericValue = isNaN(Number(value)) ? 0 : Number(value);  // 如果不是数字，设为 0

        setSelectedValue((prevValue: any) => ({
            ...prevValue,
            [recordId]: {
                ...prevValue[recordId],
                [field]: numericValue  // 存储为数字
            }
        }));
        setSelectedtargetValue((prevValue: any) => ({
            ...prevValue,
            [recordId]: {
                ...prevValue[recordId],
                [field]: numericValue  // 存储为数字
            }
        }));
    };

    useEffect(() => {
        if (dataSource?.part) {
            const updatedValues = dataSource.part.reduce((acc: any, record: any) => {
                const questionListLength = Array.isArray(record.questionList)
                    ? record.questionList.filter((question: any) => question.questions_type !== 5).length
                    : 0;

                const groupQuestionsLength = Array.isArray(record.questionList)
                    ? record.questionList.reduce((total: number, item: any) => {
                        if (Array.isArray(item.groupQuestions)) {
                            return total + item.groupQuestions.length;
                        }
                        return total;
                    }, 0)
                    : 0;
                const totalQuestions = questionListLength + groupQuestionsLength;

                acc[record.id] = {
                    ...acc[record.id],
                    questionQuantity: totalQuestions,
                };

                return acc;
            }, {});

            setSelectedValue((prevValue: any) => ({
                ...prevValue,
                ...updatedValues,
            }));
        }
    }, [dataSource]);




    // 定义表格列
    const columns = [
        {
            title: '部分',
            dataIndex: 'name',
            key: 'name',
            width: '200px',
            align: 'center',
        },
        {
            title: '题数',
            dataIndex: 'questionQuantity',
            key: 'questionQuantity',
            align: 'center',
            render: (text: any, record: any) => {
                const totalQuestions = selectedValue[record.id]?.questionQuantity || 0;
                return <span>{totalQuestions}</span>;
            },
        },
        {
            title: '总分(分)',
            dataIndex: 'totalScore',
            key: 'totalScore',
            width: 120,
            align: 'center',
            render: (text: string | number | readonly string[] | undefined, record: any) => (
                <div className="times">
                    <Input
                        type="number"
                        defaultValue={record.totalScore || ''}
                        onChange={(e) => handleInputChange(e.target.value, record.id, record.totalScore, 'totalScore')}
                        placeholder="请输入"
                    />
                </div>
            ),
        },
        {
            title: (
                <div>
                    <span>题组判分</span>
                    <Tooltip
                        title={
                            <div>
                                <p style={{ margin: 0 }}>
                                    倒扣分：该部分每道试题分值相同，采用倒扣分的方式计分，扣完本部分总分为止。答对则得分，答错倒扣分，不答不得分。
                                </p>
                                <p style={{ margin: 0, marginTop: '10px' }}>
                                    正加分：分为题组小题错误整个题组不得分、题组小题错误只有该小题不得分，剩余小题得分。
                                </p>
                            </div>
                        }
                    >
                        <span style={{ color: '#559cff', cursor: 'pointer' }}> 判分说明</span>
                    </Tooltip>
                </div>
            ),
            dataIndex: 'questionGroupScoreConfig',
            key: 'questionGroupScoreConfig',
            align: 'center',
            width: 300,
            render: (text: string | number | readonly string[] | undefined, record: any, index_0: any) => {
                // 根据 questionGroupScoreConfig 来设置默认值
                const defaultValue = record.questionGroupScoreConfig === 1 ? '1' : '2';

                return (
                    <div style={{ display: 'flex', justifyContent: 'space-evenly' }} >
                        <Radio.Group
                            defaultValue={selectedValue[record.id]?.minTime || defaultValue}  // 获取当前行的选中值，若没有值则默认选中倒扣分（1）
                            onChange={(e) => handleRadioChange(e, record.id, 'questionGroupScoreConfig')}  // 选中变化时更新状态
                        >
                            <Radio value="1">倒扣分</Radio>
                            <Radio value="2">正加分</Radio>
                        </Radio.Group>
                        {/* <div onClick={() => { handleRadioClick(record, index_0) }} style={{ color: 'red', cursor: 'pointer' }}>
                            设置 {record.questionGroupScoreConfig}--- {selectedValue[record?.id]?.questionGroupScoreConfig}
                        </div>
                        {record?.id} */}

                        {!(record.questionGroupScoreConfig === 1 && selectedValue[record?.id]?.questionGroupScoreConfig == null) && (
                            <div
                                onClick={() => { handleRadioClick(record, index_0) }}
                                style={{ color: 'red', cursor: 'pointer' }}
                            >
                                {record.questionGroupScoreConfig === 2 && selectedValue[record?.id]?.questionGroupScoreConfig == null
                                    ? '查看设置'
                                    : record.questionGroupScoreConfig === 2 && selectedValue[record?.id]?.questionGroupScoreConfig === 1
                                        ? ''
                                        : record.questionGroupScoreConfig === 1 && selectedValue[record?.id]?.questionGroupScoreConfig === 2
                                            ? '设置'
                                            : null
                                }
                            </div>
                        )}

                        {/* <div onClick={() => { handleRadioClick(record, index_0) }} style={{ color: 'red', cursor: 'pointer' }}>
                            设置
                        </div> */}
                    </div>
                );
            },

        },
        {
            title: '多选判分',
            dataIndex: 'multipleChoiceScoreConfig',
            key: 'multipleChoiceScoreConfig',
            align: 'center',
            render: (text: string | number | readonly string[] | undefined, record: any) => {
                const defaultValue = record.multipleChoiceScoreConfig === 1 ? '1' : '2';

                return (
                    <Radio.Group
                        defaultValue={selectedValue[record.id]?.minTime || defaultValue}  // 获取当前行的选中值，若没有值则默认选中倒扣分（1）
                        onChange={(e) => handleRadioChange(e, record.id, 'multipleChoiceScoreConfig')}  // 选中变化时更新状态
                    >
                        <Radio value="1">全部得分</Radio>
                        <Radio value="2">部分正确得分</Radio>
                    </Radio.Group >
                );
            },
        },
        {
            title: '填空判分',
            dataIndex: 'fillScoreConfig',
            key: 'fillScoreConfig',
            align: 'center',
            render: (text: string | number | readonly string[] | undefined, record: any) => {
                const defaultValue = record.fillScoreConfig === 1 ? '1' : '2';

                return (
                    <Radio.Group
                        defaultValue={selectedValue[record.id]?.minTime || defaultValue}  // 获取当前行的选中值，若没有值则默认选中倒扣分（1）
                        onChange={(e) => handleRadioChange(e, record.id, 'fillScoreConfig')}  // 选中变化时更新状态
                    >
                        <Radio value="1">全部得分</Radio>
                        <Radio value="2">部分正确得分</Radio>
                    </Radio.Group >
                );
            },
        },
    ];

    const columntwo: any = [
        Table.SELECTION_COLUMN,
        Table.EXPAND_COLUMN,
        {
            title: '题目类型',
            width: '13%',
            dataIndex: 'questions_type',
            key: 'questions_type',
            ellipsis: true,
            render: (item: any, record: any) => (
                <div>{examType.optionType_[Number(item)]}</div>
            )
        },
        {
            title: '难度',
            width: '10%',
            dataIndex: 'questions_difficulty',
            key: 'questions_difficulty',
            ellipsis: true
        },
        {
            title: '题目',
            width: '60%',
            dataIndex: 'questions_content',
            key: 'questions_content',
            ellipsis: true,
            render: (value: any) => {
                const extractedText = value ? removeMathML(value) : '';
                return (
                    <RenderHtml
                        cname="auto-img"
                        value={extractedText || '\u200B'}
                    />
                );
            },
        },
        {
            title: '答案',
            width: '10%',
            dataIndex: 'questions_answers',
            key: 'questions_answers',
            ellipsis: true,
            render: (value: any) =>
                value?.map((item: any, index: any) => (
                    <RenderHtml key={index} cname="auto-img" value={item}></RenderHtml>
                )),
        },
        {
            title: '创建人',
            width: '30%',
            dataIndex: 'add_username',
            key: 'add_username',
            ellipsis: true
        },
    ];

    const expandedRowRender = (expandList: any) => {
        const columns: any = [
            {
                title: '题目类型',
                width: '10%',
                dataIndex: 'questions_type',
                key: 'questions_type',
                ellipsis: true,
                render: (item: any, record: any) => (
                    <div>{examType.optionType_[Number(item)]}</div>
                ),
            },
            {
                title: '难度',
                width: '10%',
                dataIndex: 'questions_difficulty',
                key: 'questions_difficulty',
                ellipsis: true,
                render: ''
            },
            {
                title: '题目',
                width: '60%',
                dataIndex: 'questions_content',
                key: 'questions_content',
                ellipsis: true,
                render: (value: any) => (
                    <RenderHtml cname="auto-img" value={value}></RenderHtml>
                ),
            },
            {
                title: '答案',
                width: '10%',
                dataIndex: 'questions_answers',
                key: 'questions_answers',
                ellipsis: true,
                render: (value: any) =>
                    value?.map((item: any) => (
                        <RenderHtml cname="auto-img" value={item}></RenderHtml>
                    )),
            },
            {
                title: '创建人',
                width: '30%',
                dataIndex: 'add_username',
                key: 'add_username',
                ellipsis: true
            },

        ];

        return <Table sticky={false} style={{ paddingLeft: 31 }} columns={columns} showHeader={false} dataSource={expandList} pagination={false} />;
    };

    const rowSelection = {
        type: 'checkbox',
        onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
            if (Visible == 1) {
                const filteredRows = newSelectedRows.filter((row: any) =>
                    !selectRows2.some((selected: any) => selected.id == row.id)
                );
                setSelectedRowKeys(newSelectedRowKeys.filter(Boolean));
                setselectRows1(filteredRows);
            }
            else if (Visible == 2) {
                const filteredRows = newSelectedRows.filter((row: any) =>
                    !selectRows1.some((selected: any) => selected.id == row.id)
                );
                setSelectedRowKeys(newSelectedRowKeys.filter(Boolean));
                setselectRows2(filteredRows);
            }
        },
        preserveSelectedRowKeys: true,
        selectedRowKeys,
        getCheckboxProps: (record: any) => ({
            disabled:
                (Visible === 2 && selectRows1.some((row: any) => row.id == record.id)) || // 禁用 selectRows1 中选中的行
                (Visible === 1 && selectRows2.some((row: any) => row.id == record.id)), // 禁用 selectRows2 中选中的行
        }),
    };

    const removeMathML = (html: string): string => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        // 移除数学公式相关的元素
        const mathElements = doc.querySelectorAll('.math-tex, mjx-container');
        mathElements.forEach((el) => el.remove());
        return doc.body.textContent || '';  // 获取去除数学公式后的纯文本内容
    };

    // 题组
    useEffect(() => {
        fetchClassify();
        fetchQuestionSource();
    }, [])

    useEffect(() => {
        fetchDataList(query);
    }, [query]); // 监听 visible 和 query 变化


    const fetchClassify = async () => {
        const res = await paperManageApis.classification({ page: 1, size: 100 });
        if (res.status === 200) {
            setInitClassify(res.data?.data || []);
            setExamDirection(res.data?.data?.[0]?.children || []);
        }
    };

    // 题目来源

    const fetchQuestionSource = async () => {
        const res = await examManageApis.questionSource();
        if (res.status === 200) {
            setInitQuestionSource(res.data);
        }
    };

    const fetchDataList = async (value: any) => {
        // s数据 fetchTopicLists
        const res = await Contract.groupQuestionPage(value);
        if (res.status === 200) {
            setData(res.data?.data)
            setTotal(res.data?.totalCount)
            // 使用 filter 对数据进行分类
            const rows1 = res.data?.data.filter((item: any) => item.questionGroupScoreConfig == 1);
            const rows2 = res.data?.data.filter((item: any) => item.questionGroupScoreConfig == 2);
            // 更新选中的状态
            setselectRows1(rows1);
            setselectRows2(rows2);
            // 确保 selectedRowKeys 被设置为当前选中的行的 key
            const selectedKeys = [...rows1.map((item: { id: any; }) => item.id), ...rows2.map((item: { id: any; }) => item.id)];
            setSelectedRowKeys(selectedKeys);
        }
    };

    // 设置 setup


    const toggleVisible = (value: React.SetStateAction<number>) => {
        setVisible(value);
    };

    const getItemByIndex = (obj: { [x: string]: any; }, index: string | number) => {
        const keys = Object.keys(obj); // 获取所有键
        const keyAtIndex = keys[index]; // 根据索引获取键
        return keyAtIndex ? obj[keyAtIndex] : null; // 返回对应的值或 null

    }

    const handleRadioClick = (e: any, index_0: any) => {

        setQuery({
            ...query,
            partIds: e.id,
            testId: userID,
        })
        setStatus('设置')
        setScoringmodes(true)
    };

    // 确认操作
    const confirm = () => {
        setStatus('确定')
        var mergedData = {};

        Object.keys(selectedValue).forEach(key => {
            mergedData[key] = {
                ...selectedValue[key],
                ...selectedtargetValue[key]
            };
        });

        let totalScoreForAllParts = 0;  // 初始化总分变量
        userData.part.forEach((item: any) => {
            const key = item.id;
            if (mergedData[key]) {
                const scoreData = mergedData[key];
                const totalScore = parseFloat(scoreData.totalScore !== undefined ? scoreData.totalScore : item.totalScore);
                const questionQuantity = parseFloat(scoreData.questionQuantity);
                const averageScore = totalScore / questionQuantity;
                totalScoreForAllParts += totalScore; // 累加总分
                Object.assign(item, scoreData);// 合并数据

                if (item.questionList && Array.isArray(item.questionList)) {

                    item.questionList.forEach((question: any) => {
                        question.score = averageScore;

                        // 当 questions_type === 5 时，处理 groupQuestions
                        if (question.questions_type === 5 && Array.isArray(question.groupQuestions)) {
                            question.groupQuestions.forEach((groupQuestion: any) => {
                                groupQuestion.groupQuestionScore = averageScore;
                            });
                        }
                    });
                }
            }
        });


        const updatedFormDatas = {
            ...userData,
            totalScore: totalScoreForAllParts
        };

        testSaveAPI(updatedFormDatas);
        setSelectedValue([])
        setSelectedtargetValue([])
        onclose();
    };


    // 保存
    const testSaveAPI = async (data: any) => {
        const res = await Contract.testSave(data);
        if (res.status === 200) {
            updateData(res.data);  // 通知父组件更新数据
            localStorage.setItem("userData", JSON.stringify(res.data));

            if (Status == '确定') {
                const ids: any[] = [];


                res.data?.part.forEach((item: any) => {
                    if (item.questionGroupScoreConfig === 2) {
                        ids.push(item.id);
                    }
                });

                // 只有当数组非空时，调用 toggleTopicVisibility
                if (ids.length > 0) {
                    setScoringmodes(true)
                    setQuery({
                        ...query,
                        partIds: ids,
                        testId: userID,
                    })
                }
            }
        }
        message.success(res?.message);
    };

    // 题组 
    const Scoringconfirm = () => {
        // console.log('题组判分');
        const allCorrect = selectRows1.map((item: any) => item.id); // 提取 selectRows1 中的 id
        const partCorrect = selectRows2.map((item: any) => item.id); // 提取 selectRows2 中的 id
        const data = {
            "allCorrect": allCorrect,
            "partCorrect": partCorrect
        };
        var Correcttoal = allCorrect.length + partCorrect.length
        if (Correcttoal == total) {
            updateGroupQuestionScoreConfigAPI(data)
        } else {
            message.warning("请勾选对应试题完成判分设置");
        }
    };

    const updateGroupQuestionScoreConfigAPI = async (value: any) => {  // 添加判分设置

        const res = await Contract.updateGroupQuestionScoreConfig(value);
        if (res.status === 200) {
            // fetchDataList(query)
            getVoByIdAPI(query.testId, value)
        }
    };

    const getVoByIdAPI = async (id: any, value: any) => {  // 详情接口
        const res = await Contract.getVoById(id);
        if (res.status === 200) {

            if (Status == '设置') {
                var mergedData = {};

                Object.keys(selectedValue).forEach(key => {
                    mergedData[key] = {
                        ...selectedValue[key],
                        ...selectedtargetValue[key]
                    };
                });
                let totalScoreForAllParts = 0;  // 初始化总分变量
                res.data.part.forEach((item: any) => {
                    const key = item.id;
                    if (mergedData[key]) {
                        const scoreData = mergedData[key];
                        const totalScore = parseFloat(scoreData.totalScore !== undefined ? scoreData.totalScore : item.totalScore);
                        const questionQuantity = parseFloat(scoreData.questionQuantity);
                        const averageScore = totalScore / questionQuantity;
                        // 累加总分
                        totalScoreForAllParts += totalScore;
                        Object.assign(item, scoreData);
                        if (item.questionList && Array.isArray(item.questionList)) {
                            item.questionList.forEach((question: any) => {
                                question.score = averageScore;
                            });
                        }
                    }
                });
                const updatedFormDatas = {
                    ...res.data,
                    totalScore: totalScoreForAllParts
                };
                setDataSource(updatedFormDatas);
                testSaveAPI(updatedFormDatas);
                setScoringmodes(false)
            } else {
                updateData(res.data);  // 通知父组件更新数据
                localStorage.setItem('userData', JSON.stringify(userData));  // 将默认数据存储到本地
                setScoringmodes(false)
                setDataSource(res.data);
            }
            //保存
        };
    };

    const Scoringclose = () => {
        setScoringmodes(false)
    };


    // 关闭操作
    const close = () => {
        // setSelectedValue([])
        // setSelectedtargetValue([])
        onclose();
    };


    const nameChange = (e: any) => {
        setQuery({
            ...query,
            questions_content: e.target.value
        })
    }

    const labelNameChange = (e: any) => {
        setQuery({
            ...query,
            labels: e.target.value
        })
    }
    const knowledge_nameChange = (e: any) => {
        setQuery({
            ...query,
            knowledge_points: e.target.value
        })
    }

    const search = () => {
        fetchDataList(query)
    }

    const reset = () => {  // 重置
        // if (itemName) {
        //     setQuery({
        //         ...query,
        //         questions_content: '',
        //         applicationClassCode: '',
        //         knowledge_name: '',
        //         labelName: '',
        //         questionSourceCode: '',
        //         // questions_type: undefined
        //     })
        // } else {
        //     // setExamDirection('')
        //     setQuery({
        //         ...query,
        //         questions_content: '',
        //         assessmentCode: '',
        //         applicationClassCode: '',
        //         questionSourceCode: '',
        //         knowledge_name: '',
        //         labelName: '',
        //         // questions_type: undefined
        //     })
        // }

        setQuery({
            ...query,
            questions_content: '',
            // assessmentCode: '',

            knowledge_points: '',
            labels: '',

            // questions_type: undefined
        })
    }


    return (
        <><Modal
            title={title}
            visible={visibles}
            onCancel={close}
            width={1250}
            className="ScoreSettingtwo"
            footer={[
                <Button key="confirm" type="primary" onClick={confirm}>
                    确定
                </Button>,
                <Button key="cancel" onClick={close}>
                    取消
                </Button>,
            ]}
        >
            <Table
                columns={columns}
                dataSource={dataSource?.part}
                pagination={false}
                rowKey="id" />
        </Modal>
            <Modal
                title='题组判分设置'
                visible={Scoringmodes}
                onCancel={Scoringclose}
                width={1250}
                className="questions_modal"
                footer={[
                    <Button key="confirm" type="primary" onClick={Scoringconfirm}>
                        确定
                    </Button>,
                    <Button key="cancel" onClick={Scoringclose}>
                        取消
                    </Button>,
                ]}
            >
                <div className='header'>
                    <div onClick={() => toggleVisible(1)} className={Visible == 1 ? 'acton' : 'box'}>任意小题错误整个题组不得分</div>
                    <div onClick={() => toggleVisible(2)} className={Visible == 2 ? 'acton' : 'box'}>任意小题错误只该小题不得分</div>
                </div>
                <div className='searchbox'>

                    <Input placeholder='请输入名称' onChange={nameChange} value={query.questions_content} />
                    <Input placeholder='请输入知识点' onChange={labelNameChange} value={query.labels} />
                    <Input placeholder='请输入标签' onChange={knowledge_nameChange} value={query.knowledge_points} />
                    <Button
                        style={{ marginRight: '10px' }}
                        type='primary'
                        onClick={search}
                    >
                        搜索
                    </Button>
                    <Button
                        type='primary'
                        onClick={reset}
                    >
                        重置
                    </Button>
                </div>
                <div>
                    <Table
                        dataSource={data}
                        rowKey={"id"}
                        columns={columntwo}
                        // key={selectedRowKeys}
                        rowSelection={rowSelection as any}
                        pagination={{
                            position: ['bottomCenter'],
                            showSizeChanger: true,
                            total: total,
                            showQuickJumper: true,
                            // onChange: (page: number, size: any) =>
                            //     setQuery({
                            //         ...query,
                            //         page,
                            //         size
                            //     }),
                            showTotal: total => `共 ${total} 条`,
                            size: 'small'
                        }}
                        expandable={{
                            expandedRowRender: record => expandedRowRender(record?.groupQuestions || []),
                            rowExpandable: record => record.questions_type == 5,
                            expandedRowClassName: () => 'group_expand'
                        }}
                        scroll={{ y: '420px', x: 1000 }}
                    />
                </div>
            </Modal></>
    );
};

export default ScoreSettingModaltwo;
