declare namespace API {
  type Response<T = string> = {
    success: boolean;
    data: typeof T;
  };

  type PageList<T = string> = {
    isSuccess: boolean;
    data: {
      page: number;
      pageTotal: number;
      size: number;
      total: number;
      results: typeof T;
    };
  };
  type OsResponse<T = any> = {
    errorCode: string;
    errorMsg: string;
    extendMessage: typeof T;
  };
}
