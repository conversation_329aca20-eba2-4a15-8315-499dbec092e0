import paperManageApis from '@/api/Contract';
import { IconFont } from '@/components';
import TestSystemHeader from '@/components/TestSystemHeader';
import { FC, useEffect, useState } from 'react';
import ClassifyContent from './ClassifyContent';
import './index.less';
import { useSelector } from 'umi';

const StartExam: FC<any> = () => {
  const [menuList, setMenuList] = useState<any[]>([]);

  const [selectedMenu, setSelectMenu] = useState<any>('');
  //
  const [menuTabList, setMenuTabList] = useState<any[]>([]);
  //
  const handleChangeMenu = (menuId: string) => {
    if (menuId !== selectedMenu) {
      setSelectMenu(menuId);
      const children = menuList?.find(
        (item: any) => item.id === menuId,
      )?.children;
      setMenuTabList(children || []);
    }
  };

  const fetchClassify = async () => {
    const res = await paperManageApis.classification({ page: 1, size: 100 });
    if (res.status === 200) {
      setMenuList(res.data?.data || []);
      setSelectMenu(res.data?.data?.[0]?.id || '');
      setMenuTabList(res.data?.data?.[0]?.children || []);
    }
  };
  useEffect(() => {
    fetchClassify();
  }, []);
  const { title, logoUrl, isShow } = useSelector<any, any>(
    state => state.themes,
  );
  return (
    <div className={`uf-exam-layout-wrapper-container`}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
           background: '#ffffff',
          boxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.07)'
        }}
        onClick={() => {
          window.location.href = '/exam/#/testsystem';
        }}
        className="header_clickable"
      >
        <span className='icon_box'>
          <img
            src={logoUrl || require('@/images/login/default_logo.png')}
            style={{
              height: '32px'
            }}
          />
        </span>
        <TestSystemHeader title="招警联考测试系统" />
      </div>
      <div className="uf-exam-layout-content">
        <div className="uf-exam-left-part">
          <div className="left_menu_container">
            {menuList.map((item: any) => {
              return (
                <div
                  onClick={() => handleChangeMenu(item.id)}
                  className={`menu_item ${item.id === selectedMenu ? 'menu_item_active' : ''
                    }`}
                  key={item.id}
                >
                  <IconFont type={item.icon} />
                  {item.name}
                </div>
              );
            })}
          </div>
        </div>
        <div className="uf-exam-right-part">
          <ClassifyContent tabList={menuTabList} />
        </div>
      </div>
    </div>
  );
};

export default StartExam;
