import HTTP from './index';
import { message } from 'antd';

export function folder() {
  return HTTP.get(`/rman/v1/folder/init`)
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function folderPublic() {
  return HTTP.get(`/rman/v1/folder/init/public`)
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function folderChildren(folderId: string) {
  // return HTTP.get(`/rman/v1/folder/children?folderId=${folderId}`)
  return HTTP.get(`/rman/v1/folder/children?folderPath=${encodeURIComponent(folderId)}%2F&isChildCount=true`)
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function searchKey(data: any) {
  return HTTP.post('/rman/v1/search/all', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function searchfolder(data: any) {
  return HTTP.post('/rman/v1/search/folder', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function searchResList(id:any,path: any, current: number, type: string[] | undefined,pageSize?:number) {
  let condition: any = [
    // {
    //   field: 'createDate_',
    //   searchRelation: 6,
    //   value: [''],
    // },
    // {
    //   field: 'createDate_',
    //   searchRelation: 4,
    //   value: [''],
    // },
    // {
    //   field: 'type_',
    //   searchRelation: 0,
    //   value: [''],
    // },
  ];
  if (type) {
    condition = [
      ...condition,
      {
        field: 'type_',
        searchRelation: 0,
        value: type,
      },
    ];
  }
  return HTTP.post('/rman/v1/search/folder', {
    folderId: id,
    folderPath:path+'/',
    // path: encodeURIComponent(id),
    keyword: [''],
    conditions: condition,
    sortFields: [
      {
        field: 'createDate_',
        isDesc: true,
      },
    ],
    pageIndex: current,
    pageSize: pageSize || 9,
  })
    .then(res => {
      // if (res.status === 200) {
      return res.data;
      // }
    })
    .catch(error => {
      console.error(error);
      return error;
    });
}

export function searchResAll(
  id: any,
  type: string,
  path: string,
  keyword: string,
  starttime: string,
  endtime: string,
  current: number,
) {
  return HTTP.post('/rman/v1/search/all', {
    folderId: id,
    keyword: [keyword],
    folderPath: path,
    conditions: [
      {
        field: 'createDate_',
        searchRelation: 6,
        value: [endtime],
      },
      {
        field: 'createDate_',
        searchRelation: 4,
        value: [starttime],
      },
      {
        field: 'type_',
        searchRelation: 0,
        value: [type],
      },
    ],
    sortFields: [
      {
        field: 'createDate_',
        isDesc: true,
      },
    ],
    pageIndex: current,
    pageSize: 9,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

export function folderTree() {
  return HTTP.get(`/rman/v1/folder/all/tree?level=5`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      } else {
        message.error(res);
      }
    })
    .catch(error => {
      console.error(error);
    });
}
export function getTreebylevel(level: number = 2) {
  return HTTP.get(`/rman/v1/folder/all/tree?level=${level}`)
    .then((res: any) => {
      if (res.status === 200) {
        return res.data;
      } else {
        message.error(res);
      }
    })
    .catch(error => {
      console.error(error);
    });
}
//查询课程
export function courseDetail(id: string) {
  return HTTP.get(`/learn/v1/course/${id}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

//查询资源
export function resourceDetail(id: string, pathType?: number) {
  return HTTP.get(`/rman/v1/entity/base/${id}`, {
    params: {
      pathType,
    },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

// 保存接口
export function saveCourse(data: any) {
  return HTTP.post('/learn/v1/course/create', data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      console.error(error);
    });
}

//删除附件
export function deleteAttchmentApi(courseId: string, fileguid: string) {
  return HTTP.get(`/learn/v1/upload/course/attachment`, {params: {courseId, fileguid}})
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}

/**
 * 更新附件名称
 * @param params
 */
export function updateAttachmentName(
  params: MicroCourse.IAttachmentRenameForm,
) {
  return HTTP(
    `/learn/v1/upload/course/attachment/rename`,
    {
      method: 'GET',
      params
    },
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}