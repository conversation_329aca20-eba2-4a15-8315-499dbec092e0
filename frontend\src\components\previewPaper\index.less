.paperPreview{
  .ant-drawer-content-wrapper{
    width: 100%;
    .ant-drawer-content{
      // width: 580px;
      .ant-drawer-wrapper-body{
        .ant-drawer-body{
          .content_row{
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
            .content{
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              flex-wrap: wrap;
              margin-bottom: 20px;
              >span{
                margin-right: 10px;
              }
              .type{
                margin-left: 5px;
                >span{
                  opacity: 0.5;
                }
              }
              .spcialDom {
                max-width: 100%;
                p{
                  margin-bottom: 0 !important;
                  width: 100%;
                  overflow: hidden;
                }
                img{
                  max-width: 50px;
                  height: auto;
                  vertical-align:bottom;
                }
              }
            }
            .fileList_{
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              margin-bottom: 20px;
              >span{
                width: 75px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
              >div{
                width: calc(100% - 75px);
                display: flex;
                flex-direction: column;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
            .fileList_upload{
              display: flex;
              flex-direction: row;
              align-items: flex-start;
              margin-bottom: 20px;
              >span{
                width: 75px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              }
              >div{
                width: calc(100% - 75px);
                display: flex;
                flex-direction: column;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                >div{
                  >span:first-child{
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                  }
                  >span:last-child{
                    margin-left: 5px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    color: #cbcbcb;
                  }
                }
              }
            }
            .answers{
              margin-left: 20px;
              .answer_item{
                display: flex;
                align-items: flex-start;
                flex-direction: row;
                margin-bottom: 10px;
                .radio_content{
                  font-size: 14px;
                }
                .spcialDom {
                  flex:1 1;
                  img{
                    max-width: 50px;
                    height: auto;
                    vertical-align:bottom;
                  }
                }
              }
              .blanks{
                >span{
                  margin-right: 10px;
                }
              }
            }
            .spcialDom {
              img{
                height: auto;
                max-width: 50px;
                vertical-align:bottom;
              }
            }
            >p:first-child{
              width: 119px;
              background: #F7F8FA;
              border-right: solid 1px #EEEEEE;
            }
            >p:last-child{
              flex: 1;
            }
          }
          .content_row:last-child{
            border-bottom: solid 1px #EEEEEE;
          }
        }
      }
    }
  }
  
}