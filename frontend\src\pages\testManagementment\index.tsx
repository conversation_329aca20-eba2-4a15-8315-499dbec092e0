import React, { useState, useEffect } from "react";
import { Form, Image, Table, Input, Button, Select, Space, Menu, Layout, message, Modal } from "antd";
import { EyeOutlined, BarChartOutlined, DeleteOutlined, DownloadOutlined, CopyFilled, CopyOutlined, PlusCircleOutlined, EditOutlined, QuestionCircleFilled, createFromIconfontCN } from "@ant-design/icons";
import { SizeType } from "antd/lib/config-provider/SizeContext";
import './index.less';
import { history, useSelector } from 'umi';
import Contract from '@/api/Contract';
import loginApis from '@/service/loginApis';
import { format } from "echarts";
const { Header, Sider, Content } = Layout;
const { Option } = Select;
const { SubMenu } = Menu;
import dayjs from 'dayjs';
const IconFont = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_2019002_dva91dyojmi.js',
});


const Markingoptions = [
  { value: 1, label: "未阅卷" },
  { value: 2, label: "已阅卷" },
  { value: 3, label: "系统阅卷" }
];

const Testgoptions = [
  { value: 1, label: "未发布" },
  { value: 2, label: "未开始" },
  { value: 3, label: "进行中" },
  { value: 4, label: "已结束" }
];

// 测试管理主组件
const TestManagementment: React.FC = () => {
  const [form] = Form.useForm();
  const [size, setSize] = useState<SizeType>('middle');
  const [data, setData] = useState([]);
  const [pagtotal, setPagtotal] = useState();
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]); // 当前选中的行
  const [menuData, setMenuData] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,  // 当前页码
    pageSize: 10, // 每页显示的数量
  });
  const [classifyId, setClassifyId] = useState<string | undefined>();
  const [FounderList, setFounderList] = useState([]); // 创建人下拉
  const [openKeys, setOpenKeys] = useState<string[]>([]); // 当前展开的菜单项
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]); // 当前选中的菜单项

  // 表格列配置
  const columns = [
    {
      title: "测试名称",
      dataIndex: "name",
      key: "name",
      width: 200,
      align: 'center',
    },
    {
      title: "测试状态",
      dataIndex: "testState",
      key: "testState",
      width: 100,
      align: 'center',
      render: (text: any) => {
        const option = Testgoptions.find(option => option.value === text);
        return option ? option.label : "未知状态";
      }
    },
    {
      title: "是否阅卷",
      dataIndex: "readState",
      key: "readState",
      width: 100,
      align: 'center',
      render: (text: any) => {
        const option = Markingoptions.find(option => option.value === text);
        return option ? option.label : "未知状态";
      }
    },

    {
      title: "开放时间",
      dataIndex: "openStartDate",
      key: "openStartDate",
      width: 200,
      align: 'center',
      render: (text: any, record: any) => {
        const startDate = dayjs(text);
        const endDate = dayjs(record.openEndDate);

        if (!startDate.isValid() || !endDate.isValid()) {
          return '';
        }
        const formattedStartDate = startDate.format('YYYY-MM-DD HH:mm:ss');
        const formattedEndDate = endDate.format('YYYY-MM-DD HH:mm:ss');
        // return `${formattedStartDate} - <br /> ${formattedEndDate}`;
        const formattedTime = `${formattedStartDate} <br /> ${formattedEndDate}`;

        return (
          <div dangerouslySetInnerHTML={{ __html: formattedTime }} />
        );
      },
    },
    {
      title: "补考时间",
      dataIndex: "resitStartDate",
      key: "resitStartDate",
      width: 200,
      align: 'center',
      render: (text: any, record: any) => {
        const startDate = dayjs(text);
        const endDate = dayjs(record.resitEndDate);

        if (!startDate.isValid() || !endDate.isValid()) {
          return '';
        }
        const formattedStartDate = startDate.format('YYYY-MM-DD HH:mm:ss');
        const formattedEndDate = endDate.format('YYYY-MM-DD HH:mm:ss');
        // return `${formattedStartDate} - ${formattedEndDate}`;
        const formattedTime = `${formattedStartDate} <br /> ${formattedEndDate}`;
        return (
          <div dangerouslySetInnerHTML={{ __html: formattedTime }} />
        );
      },
      // render: (text: any) => {
      //   const formattedDate = dayjs(text);
      //   if (!formattedDate.isValid()) {
      //     return '';
      //   }
      //   return formattedDate.format('YYYY-MM-DD HH:mm:ss');
      // },
    },
    {
      title: "补考设置",
      dataIndex: "isResit",
      key: "isResit",
      width: 130,
      align: 'center',
      render: (isResit: Boolean) => {
        // 根据布尔值显示相应的文本
        return isResit ? '允许' : '不允许补考';
      },
    },

    {
      title: "难度",
      dataIndex: "difficulty",
      key: "difficulty",
      width: 80,
      align: 'center',
    },
    {
      title: "题目数量",
      dataIndex: "questionQuantity",
      key: "questionQuantity",
      width: 100,
      align: 'center',

    },
    {
      title: "提交人数",
      dataIndex: "submitQuantity",
      key: "submitQuantity",
      width: 100,
      align: 'center',

    },
    {
      title: "创建人",
      dataIndex: "createUserName",
      key: "createUserName",
      width: 140,
      align: 'center',
    },
    {
      title: "创建时间",
      dataIndex: "createDate",
      key: "createDate",
      width: 200,
      align: 'center',
      render: (text: string | number | Date) => {
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
      }
    },
    {
      title: "操作",
      key: "action",
      width: 270, // 调整列宽以适应内容
      align: "center",
      fixed: "right",
      render: (_: any, record: any) => (
        <Space className="clexs" size="middle">
          {record.testState === 1 && (
            <div onClick={() => actionBtun('发布', record)} style={{ marginBottom: '3px' }}>
              <Image
                width={16}
                height={16}
                src={require('@/images/icons/fbu.png')}
                title="发布"
                preview={false}
              />
            </div>
          )}

          {record.testState > 1 && (
            <div onClick={() => actionBtun('取消发布', record)} style={{ marginBottom: '3px' }}>
              <Image
                width={16}
                height={16}
                src={require('@/images/icons/bfb.png')}
                title="取消发布"
                preview={false}
              />
            </div>
          )}
          < EditOutlined onClick={() => YourComponent('编辑', record)} title='编辑' />
          <EyeOutlined onClick={() => actionBtun('预览', record)} className="iconfs" title="预览" />
          {record.testState === 1 && (
            <DeleteOutlined
              className="iconfs"
              title="删除"
              onClick={() => actionBtun("删除", record)}
            />
          )}
          <a
            style={{ color: "#1890ff", marginRight: 8 }}
            onClick={() => actionBtun("批阅", record)}
          >
            {record.readState === 1 ? "批阅" : "查看"}
          </a>

          <a
            style={{ color: "#1890ff" }}
            onClick={() => actionBtun("统计操作", record)}
          >统计</a>
        </Space >
      ),
    },

  ];



  const YourComponent = (name: string, record: any) => {
    if (name === '编辑' && record.testState > 1) {
      Modal.confirm({
        icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
        title: '提示', // 修改标题
        content: '修改后考试数据会被删除', // 内容描述
        okText: '确定', // 确认按钮文本
        cancelText: '取消', // 取消按钮文本
        centered: true, // 居中显示（可选）
        onOk: () => {
          history.push(`/testManagementment/Test?classifyId=${classifyId}&name=${name}&bjid=${record.id}`);
        },
      });
    } else {
      if (itemName) {
        const Name = `第一部分：${itemName}`;
        const userDatas = {
          'classifyId': classifyId,
          "readingTeacher": [],
          "part": [{ name: Name }],
          "examUser": [],
        }
        console.log(name, '谢谢谢谢');
        localStorage.setItem('userData', JSON.stringify(userDatas));
        history.push(`/testManagementment/Test?classifyId=${classifyId}&name=${name}&bjid=${record.id}&itemName=${itemName}`);
      } else {
        console.log(name, '比较接近');
        history.push(`/testManagementment/Test?classifyId=${classifyId}&name=${name}&bjid=${record.id}`);
      }
    }
  }
  
  const actionBtun = (name: string, record: any) => {
    // console.log(name, record);
    if (name == '发布') {

      Modal.confirm({
        icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
        title: '提示', // 修改标题
        content: '发布后学员即可选择该测验进行答题', // 内容描述
        okText: '发布', // 确认按钮文本
        cancelText: '取消', // 取消按钮文本
        centered: true, // 居中显示（可选）
        onOk: () => {
          issueAPI(record.id); // 发布接口调用
        },
      });
    } else if (name == '取消发布') {

      Modal.confirm({
        content: '确定是否取消发布？',
        // title: '提示',
        icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
        onOk: () => {
          cancelAPI(record.id)
        },
      });
    } else if (name == '删除') {

      Modal.confirm({
        content: '确定是否删除？',
        title: '删除确认',
        icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
        onOk: () => {
          const data = []
          data.push(record.id)
          const dataToSend = {
            "ids": data
          };
          handleDelete(dataToSend)
        },
      });

    } else if (name == "统计操作") {
      // name=${record.name}
      history.push(`/ExamModule/Revise?Id=${record.id}&name=${record.name}`);
    } else if (name == "批阅") {
      history.push(`/ExamModule/Details?Id=${record.id}`);
    } else if (name == '预览') {
      // console.log(name, record, '预览');

      var data = {
        listVoByAnswerId: record.id,
        titleName: record.name,
        SelectId: record.id,
        detailIndex: 1,
        property: '预览',
      }
      history.push({
        pathname: `/ExamModule/Answer`,
        state: data
      });
    }

  }


  useEffect(() => {
    setLoading(true);
    // const savedClassifyId = localStorage.getItem('classifyId');
    // if (savedClassifyId) {
    //   setClassifyId(savedClassifyId);
    // }
    const categoryData = localStorage.getItem('categoryData');
    if (categoryData) {
      const parsedData = JSON.parse(categoryData);
      setClassifyId(parsedData.classifyId);
      setitemName(parsedData.itemName);
      setOpenKeys([parsedData.openKeys]);  // 展开第一个父项

      if (parsedData.parentName === '专题练习') {
        setitemName(parsedData.itemName)
      } else {
        setitemName('')
      }
    } else {
      console.log('No data found in localStorage');
    }
    fetchMenuData(1, 100000);
    fetchFounderList();
    // 获取全局参数
    fetchParameterConfigsAPI()
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  // 更新分页、分类时获取数据
  useEffect(() => {
    if (classifyId) {
      const updatedDataSource = createDataSource();
      fetchTestData(updatedDataSource);
    }
  }, [pagination.current, pagination.pageSize, classifyId]);

  // 生成当前数据源  数据 classifyId 不同
  const createDataSource = () => {
    return {
      page: pagination.current,
      size: pagination.pageSize,
      classifyId: classifyId || '',
      name: '',
      readState: '',
      testState: '',
      createId: '',
    };
  };

  // 获取测试数据
  const fetchTestData = async (dataSource: any) => {
    setLoading(true);
    const res = await Contract.testcation(dataSource);
    setLoading(false);
    if (res.status === 200) {
      setData(res.data.data);
      setPagtotal(res.data.totalCount);
    }
  };
  const [customer, setcustomer] = useState(false)

  const fetchParameterConfigsAPI = async () => {
    try {
      const res = await loginApis.fetchParameterConfigs();
      const extendMessage = res.extendMessage;
      // 默认值
      let result = false;
      if (Array.isArray(extendMessage)) {
        const ashCustomerConfig = extendMessage.find(
          (item) => item.code === "Ash_customer"
        );
        if (ashCustomerConfig?.value) {
          result = JSON.parse(ashCustomerConfig?.value);
        }
      }
      localStorage.setItem("Ash_customer_result", JSON.stringify(result));
    } catch (error) {
      console.error("Failed to fetch parameter configs:", error);
    }
  };


  const issueAPI = async (id: any) => {
    // console.log(id, 'DDDD');
    const res = await Contract.issue(id);
    if (res.status === 200) {
      fetchTestData(createDataSource());
      message.success("发布成功！");
    }
  };

  const cancelAPI = async (dataSource: any) => {
    const res = await Contract.cancel(dataSource);
    if (res.status === 200) {
      fetchTestData(createDataSource());
      message.success("取消成功！");
    }
  };

  // 获取菜单数据
  const fetchMenuData = async (page: number, size: number) => {
    const res = await Contract.classification({ page, size });
    if (res.status === 200) {
      const formattedMenu = formatMenuData(res.data.data);
      setMenuData(formattedMenu);
      localStorage.setItem("Ash_parentName", res.data.data[0].name);
      if (res.data.data[0]?.children && res.data.data[0].children.length > 0) {
        // 默认选中第一个父级的第一个子项
        const categoryData = localStorage.getItem('categoryData');


        if (!categoryData) {
          setClassifyId(res.data.data[0].children[0].id);
          setitemName(res.data.data[0].children[0].name);
          setOpenKeys([res.data.data[0].id]);  // 展开第一个父项
          // setSelectedKeys([res.data.data[0].children[0].id]); // 默认选中第一个子项
        }

      }
    }
  };



  const formatMenuData = (data: any, parentName: string | null = null) => {
    return data.map((item: any) => {
      if (item.children && item.children.length > 0) {
        return (
          <SubMenu
            key={item.id}
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {/* 父级菜单图标 */}

                {item.icon && <IconFont style={{ fontSize: '18px', marginRight: '8px' }} type={item.icon} />}
                {item.name}
              </div>
            }
            data-name={item.name}
            data-parent={parentName || ''}
          >
            {formatMenuData(item.children, item.name)} {/* 传递当前层级的父级名称 */}
          </SubMenu>
          // <SubMenu key={item.id} title={item.name} data-name={item.name} data-parent={parentName || ''}>
          //   {formatMenuData(item.children, item.name)} {/* 传递当前层级的父级名称 */}
          // </SubMenu>
        );
      }

      return (
        <Menu.Item key={item.id} data-name={item.name} data-parent={parentName}>
          {item.name}
        </Menu.Item>
      );
    });
  };


  // 获取创建人下拉数据
  const fetchFounderList = async () => {
    const res = await Contract.addusercodeList();
    if (res.status === 200) {
      setFounderList(res.data);
    }
  };

  const testCopyAPI = async (data: {}) => {
    const res = await Contract.testCopy(data);
    if (res.status === 200) {
      message.success("复制成功！");
      fetchTestData(createDataSource());

    }
  };

  // 删除数据
  const handleDelete = async (data: {}) => {
    const res = await Contract.testDelete(data);
    if (res.status === 200) {
      message.success("删除成功！");
      fetchTestData(createDataSource());
    }
  };

  // 点击菜单事件
  const [itemName, setitemName] = useState('')
  const handleMenuClick = (e: any) => {
    const menuItem = e.domEvent.currentTarget;
    const itemName = menuItem.getAttribute('data-name');
    const parentName = menuItem.getAttribute('data-parent');

    setClassifyId(e.key); // 设置分类 ID
    var data = {
      classifyId: e.key,
      itemName: itemName,
      parentName: parentName,
      openKeys: e.keyPath[1],
    }
    // localStorage.setItem('classifyId', e.key);
    localStorage.setItem('categoryData', JSON.stringify(data));
    // console.log('ID:', e.key);
    // console.log('名称:', itemName);
    // console.log('父级名称:', parentName, itemName);
    localStorage.setItem("Ash_parentName", parentName);
    if (parentName === '专题练习') {
      setitemName(itemName)
    } else {
      setitemName('')
    }
  };

  // 搜索
  const handleSearch = () => {
    form.validateFields().then(values => {
      const dataSource = {
        ...createDataSource(),
        name: values.name,
        readState: values.testState,
        testState: values.readState,
        // readState: values.readState,
        // testState: values.testState,
        createId: values.creator,
      };
      fetchTestData(dataSource);
    });
  };

  // 重置
  const handleReset = () => {
    form.resetFields();
    fetchTestData(createDataSource());
  };

  // 分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    setPagination({ current: page, pageSize });
  };
  // 复制
  const handleCopeClick = () => {
    const dataToSend = {
      "ids": selectedRowKeys
    };
    testCopyAPI(dataToSend)
  };

  //删除操作
  const handleDlectClick = () => {
    Modal.confirm({
      content: '确定是否删除？',
      title: '删除确认',
      icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
      onOk: () => {
        const dataToSend = {
          "ids": selectedRowKeys
        };
        handleDelete(dataToSend)
      },
    });

  };
  // rowSelection 配置 行选择的回调
  const rowSelection = {
    selectedRowKeys,
    // onChange: onSelectChange,
    onChange: (newSelectedRowKeys: any[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <Layout className="testManagementment" style={{ height: "calc(100vh - 52px)" }}>
      {/* 侧边栏 */}
      <Sider width={200}>
        <Menu
          onClick={handleMenuClick}
          mode="inline"
          style={{ height: '100%' }}
          selectedKeys={[classifyId || '']}  // 默认选中项
          openKeys={openKeys} // 控制展开的菜单项
          onOpenChange={(keys) => setOpenKeys(keys)}
        >
          {menuData}
        </Menu>
      </Sider>
      <Layout style={{ flex: 1 }}>
        <Content style={{
          background: "#fff",
          padding: 24,
          margin: 0,
          minHeight: 280,
          flex: 1,
        }}
          className="conts"
        >
          {/* 筛选和操作区域 */}
          <Form
            form={form}
            layout="inline"
            className="forms"
            style={{ marginBottom: 16 }}
          >
            <Form.Item name="name" >
              <Input placeholder="测试名称" style={{ width: '200px' }} />
            </Form.Item>
            <Form.Item name="creator">
              <Select placeholder="创建人" style={{ width: 150 }}>
                {FounderList.map(option => (
                  <Option key={option.add_usercode} value={option.add_usercode}>
                    {option.add_username}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="readState">
              <Select placeholder="测验状态" style={{ width: 150 }}>
                {Testgoptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="testState">
              <Select placeholder="是否阅卷" style={{ width: 150 }}>
                {Markingoptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
            </Form.Item>
            <Form.Item>
              <Button onClick={handleReset}>清空</Button>
            </Form.Item>
          </Form>


          <div style={{ display: 'flex', alignItems: 'center', margin: '20px 0 ' }} >
            <Button onClick={() => YourComponent('新建测验', '1')} type="primary" shape="round" icon={<PlusCircleOutlined className="iconfs" />} size={size}>
              新建测验
            </Button>
            <Button className="disabled" disabled={selectedRowKeys.length === 0} onClick={handleCopeClick} icon={<CopyOutlined className="iconfs" />} type="text">复制</Button>
            <Button disabled={selectedRowKeys.length === 0} onClick={handleDlectClick} icon={<DeleteOutlined className="iconfs" />} type="text">删除</Button>
          </div>

          {/* 表格区域 */}
          <Table
            columns={columns}
            dataSource={data}
            loading={loading}
            pagination={{
              position: ["bottomCenter"], // 确认位置

              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagtotal,
              showSizeChanger: true,
              showQuickJumper: true,
              onChange: handleTableChange,
            }}
            rowKey="id"
            rowSelection={rowSelection} // 添加选择框功能
            scroll={{ y: 'calc(100vh - 320px)' }}
            style={{ flex: 1 }}
          />
        </Content>
      </Layout>
    </Layout>
  );
};

export default TestManagementment;
