// 选择题选项
export interface ISelectQuestionItem {
    /** 选项序号 */
    seq: string
    /** 选项内容 */
    content: string
    /** 只针对填空 1：文本 2：数字 */
    answerType?: 1 | 2
    /** 只针对填空answerType为2的情况 1：确定数值 2：范围答案 */
    answerRange?: 1 | 2
    /** 只针对填空answerType为2的情况 最大值 */
    answerMax?: number
    /** 只针对填空answerType为2的情况 最小值 */
    answerMin?: number
}

// 试题附件类型
export interface IQuestionAttachmentItem {
    category?: string
    /** 试题ID */
    questionId: string
    /** 资源ID */
    contentId: string
    /** 附件名称 */
    attachmentName: string
    /** 附件资源地址 */
    attachmentSource: string
    /** 附件类型 */
    attachmentType: string
    /** 附件大小,单位是kb */
    attachmentSize?: number
    order?: number
}

// 题组的类型
export interface IQuestionGropItem {
    id: string
    /** 试题状态, 未使用:1、已使用:0 */
    state?: 1 | 0
    /** 试题绑定的素材id */
    source_materialId?: string
    /** 试题类型, 单选:0、多选:1、填空:2、主观:3、判断：4、 题组：5 */
    questions_type: 0 | 1 | 2 | 3 | 4 | 5
    /** 试题的正确答案 */
    questions_answers?: any
    /** 试题名称 内容 */
    questions_content?: string
    /** 添加试题的机构 */
    questions_mechanism?: string
    /** 机构名称 */
    questions_mechanism_name?: string
    /** 添加试题的人名字 */
    add_username?: string
    /** 添加试题的人的code */
    add_usercode?: string
    /** 添加得创建时间, yyyy-MM-dd HH:mm:ss */
    creator_time?: string
    /** 试题的修改时间, yyyy-MM-dd HH:mm:ss */
    update_time?: string
    /** 试题的解析 */
    questions_analysis?: string
    /** 选择题选项 */
    questions_options?: ISelectQuestionItem[]
    /** 选项数量 */
    options_nums?: number
    /** 试题难度 */
    questions_difficulty?: number
    /** 适用层次id */
    questions_level?: string[]
    /** 适用专业名称 */
    questions_major?: string[]
    /** 适用课程名称 */
    questions_lesson?: string
    /** 分享人实体类数组 */
    share_users?: string[]
    share_userCodes?: string[]
    share_only_userCodes?: string[]
    /** 是否可编辑 默认是false 不可编辑 列表返回用 */
    editable?: boolean
    /** 标签 */
    labels?: string[]
    /** 是否允许附件上传(主观题), 用于标识作业端学生上传附件 */
    hasAttachment?: any
    /** 试题附件, 老师上传附件，用于学生预览 */
    fileList?: IQuestionAttachmentItem[]
    /** 正确率 */
    accuracy?: string
    /** 知识点 */
    knowledge_points?: any
    /** 认知层次 */
    cognitive_level?: string
    weight?: number
    /** 试题是否被使用过, 未使用:1 、已使用:0 */
    use_state?: 1 | 0
    /** 案例库id实体类 key案例库真实id value 假的ids */
    applicationLibMap?: any
    /** 是否被回收 是true 不是false 默认false null */
    reclamationStatus?: boolean
    /** 把这个题送进 回收站的人 */
    reclamationUserCode?: string
    /** 进入回收站时间, yyyy-MM-dd HH:mm:ss */
    reclamationTime?: string
    /** 考核方向 */
    assessment?: any
    /** 应用code */
    applicationClassCode?: string
    /** 应用名字 */
    applicationClassName?: string
    /** 题目来源Code */
    questionSourceCode?: string
    /** 题目来源Name */
    questionSourceName?: string
    /** 是否是题组里面的小题 默认是 false(不是题组里面新增的题) */
    insideQuestionGroup?: boolean
    /** 题组里面小题的分数 */
    groupQuestionScore?: number
    /** 主观题允许上传附件 */
    enableHasAttachment?: boolean
    /** 是否排序过 */
    isSort?: boolean
    /** 排序次数 */
    sortTimes?: number
}

// 小题组件类型
export interface IQuestionComponentProp {
    /** 小题信息 */
    detail: IQuestionGropItem,
    /** 小题的编号 */
    itemIndex: number
    /** 编辑 | 新增 */
    editType: string
    /** 删除小题 */
    onDeleteTopic: (value: string) => void
    /** 修改小题信息 */
    onChangeItem: (type:keyof IQuestionGropItem, value: any, id: string) => void
}