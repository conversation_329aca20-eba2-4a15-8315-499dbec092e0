import React, { useEffect, useRef, useState } from 'react';
import { Card, Row, Col, Button, Input, Tabs, Image, Tag, List, PageHeader, message, Radio, Checkbox, Avatar, Empty, Modal, InputNumber, Space, Upload, Popconfirm } from 'antd';
import { useLocation } from 'umi';
import { PictureOutlined, NotificationOutlined, PaperClipOutlined, CloseOutlined, DeleteOutlined, QuestionCircleFilled } from '@ant-design/icons';
import Contract from '@/api/Contract';
import './index.less';
import RenderHtml from '@/components/renderHtml';
import examType from '@/types/examType';
import { Editorfs } from '@/components';
import { convertToChinaNum } from '@/utils';
import QuestionButtons from './QuestionButtons';
import ReviewRecord from './ReviewRecord';

const { TextArea } = Input;
// 答题详情


const ExamPage = () => {
    let globalIndex = 1;  // 初始化
    let globalIndexs = 1;  // 初始化全局计数器
    let globalIndexflase = 0;  //题组序列号
    const location: any = useLocation();
    const [currentIndex, setCurrentIndex] = useState(0); // 当前选中的项索引
    const [score, setScore] = useState(0);
    const [answers, setAnswers] = useState([{ answer: '', feedback: '' }]);
    const [Data, setData] = useState([]);
    const [Cicismlist, setiCismlist] = useState([]);
    const [currentItem, setCurrentItem] = useState(null); // 当前选中的数据

    const [totalDetailsCount, setTotalDetailsCount] = useState(0); // 用于存储 details 的总数
    const [totalUnreadCount, setTotalUnreadCount] = useState(0); // 用于存储 answerQuestion.isRead 为 false 的数量
    const [TotalScore, setTotalScore] = useState(0); // 总分
    // 初始化题目引用
    const questionRefs = useRef<any>({});
    const [isEditable, setIsEditable] = useState(false); // 是否允许编辑
    const [isEditableID, setIsEditableID] = useState(''); // 是否允许编辑
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [topicid, settopicid] = useState('');  //题目id
    const [userinfo, setuserinfo] = useState();  //userinfo

    const [visible, setVisible] = useState(false);
    const [sectionId, setsectionId] = useState('')
    const [stateId, setstateId] = useState()

    // 初始化数据
    useEffect(() => {
        const state = location.state.data;
        if (state && state.length > 0) {
            setCurrentItem(state[0]); // 默认选中第一个
            listVoByAnswerId(state[0].id);
        }
        if (location.state.id) {
            console.log(location.state.id, 'location.state.id');
            setstateId(location.state.id)
            listByAnswerAPI(location.state.id);
        }
    }, [location]);

    useEffect(() => {
        const userInfoString = localStorage.getItem("userinfo");
        const parsedUserInfo = JSON.parse(userInfoString);
        setuserinfo(parsedUserInfo)
    }, []);

    const listVoByAnswerIddata = async (id: any) => {
        const res = await Contract.listVoByAnswerId(id);
        if (res.status === 200) {
            setData(res.data);
        }
    };

    const listVoByAnswerId = async (id: any) => {
        const res = await Contract.listVoByAnswerId(id);
        if (res.status === 200) {
            setData(res.data);
            let totalDetailsCount = 0;
            let totalUnreadCount = 0;
            let totalScore = 0;
            res.data.forEach((item: { details: any[]; }) => {
                if (item.details && Array.isArray(item.details)) {
                    totalDetailsCount += item.details.length;

                    item.details.forEach(detail => {
                        const answerQuestion = detail?.answerQuestion;

                        if (answerQuestion?.length > 0) {
                            const firstAnswer = answerQuestion[0];

                            // 计算答题未阅的数量
                            if (firstAnswer?.isRead === false) {
                                totalUnreadCount += 1;
                            }

                            // 计算总分
                            if (firstAnswer?.score) {
                                totalScore += firstAnswer.score; // 累加分数
                            }
                        }
                    });
                }
            });

            // 更新状态
            setTotalDetailsCount(totalDetailsCount);
            setTotalUnreadCount(totalUnreadCount);
            setTotalScore(totalScore); // 设置总分
            // if (res.data[0].details[0].answerQuestion && res.data[0].details[0].answerQuestion.length > 0) {
            //     const id = res.data[0].details[0].answerQuestion[0].id;
            //     // 处理id
            //     // criticismlistAPI(id)

            //     // criticismlistAPI(res.data[0].details[0].answerQuestion[0].id)
            // } else {
            //     console.log("answerQuestion is null or empty");
            // }
        }
    };


    const criticismlistAPI = async (id: any) => { // 废弃获取批阅记录
        const res = await Contract.criticismlist(id);
        if (res.status === 200) {
            console.log('获取批阅记录', res.data);
            setiCismlist(res.data)
        }
    };


    const listByAnswerAPI = async (id: any) => { //获取所有得批阅记录
        const res = await Contract.listByAnswer(id);
        if (res.status === 200) {
            const reversedData = res.data.reverse(); // 翻转数组
            setiCismlist(reversedData);
            // setiCismlist(res.data)
        }
    };

    const insertAPI = async (data: any) => {//添加记录
        const res = await Contract.insert(data);
        if (res.status === 200) {
            // criticismlistAPI(data.answerQuestionId)
            listByAnswerAPI(stateId);
        }
    };

    const testAnswerQuestionscoreAPI = async (data: any) => {//打分
        const res = await Contract.testAnswerQuestionscore(data);
        if (res.status === 200) {
            listVoByAnswerId(currentItem.id)
        }
        message.success(res?.message);
    };

    const criticismdeleteAPI = async (id: any) => {//删除
        const res = await Contract.criticismdelete(id);
        if (res.status === 200) {
            // criticismlistAPI(topicid)
            listByAnswerAPI(stateId);
        }
        message.success(res?.message);
    };

    const preserveAPI = async (id: any) => {//保存
        const res = await Contract.preserve(id);
        message.success(res?.message);
    };


    const handleScoreChange = (value: any) => {
        setScore(value);
    };
    const [buttonReturnedString, setbuttonReturnedString] = useState()
    const [RecordSelection, setRecordSelection] = useState()

    const butncard = (value: any, title: String) => {
        setRecordSelection(value.id)
        let questionId = '';
        if (title == 'record') {
            questionId = value.questionId;
            setbuttonReturnedString(value.text)
        } else {
            questionId = value.question.id;
            if (value.answerQuestion === null) {
                settopicid('')
            } else {
                settopicid(value.answerQuestion[0].id)
                // criticismlistAPI(value.answerQuestion[0].id)
                listByAnswerAPI(stateId);
            }
        }
        setsectionId(questionId)
        const questionElement = questionRefs.current[questionId];
        if (questionElement) {
            const container = document.querySelector('.scflow');
            const rect = questionElement.getBoundingClientRect();
            if (container) {
                const containerScrollTop = container.scrollTop;
                const offset = 300;
                const scrollTop = containerScrollTop + rect.top - offset;

                container.scrollTo({
                    top: scrollTop,
                    behavior: 'smooth',
                });
            }
        } else {
            setgroupstionsB(questionId)
            scrollToElementB(questionId)
            // setsectionId('')
        }


    };
    const [groupstionsB, setgroupstionsB] = useState('')
    const questionRefsB = useRef<{ [key: string]: HTMLDivElement | null }>({});
    const butnQuestionsB = (value: any, groupQuestion: any, index: any) => {
        setsectionId('')
        setgroupstionsB(groupQuestion.id)
        scrollToElementB(groupQuestion.id)
        if (value.answerQuestion === null) {
            settopicid('')
        } else {
            settopicid(value.answerQuestion[index])
            listByAnswerAPI(stateId);
        }
    }

    const scrollToElementB = (id: string) => {
        const targetElement = questionRefsB.current[id]; // 获取目标元素
        const innerContainer = document.querySelector('.yul_scflow'); // 子滚动容器
        const outerContainer = document.querySelector('.scflow'); // 父滚动容器

        if (targetElement && innerContainer && outerContainer) {
            // 获取目标元素相对于视口的矩形
            const targetRect = targetElement.getBoundingClientRect();

            // 获取父容器和子容器的视口矩形
            const innerRect = innerContainer.getBoundingClientRect();
            const outerRect = outerContainer.getBoundingClientRect();

            // 定义额外的偏移量（单位为像素）
            const additionalOffset = -200;

            // 计算目标居中位置所需的偏移量
            const innerOffsetTop =
                targetRect.top -
                innerRect.top +
                innerContainer.scrollTop -
                (innerRect.height / 2 - targetRect.height / 2);
            const outerOffsetTop =
                targetRect.top -
                outerRect.top +
                outerContainer.scrollTop -
                (outerRect.height / 2 - targetRect.height / 2) +
                additionalOffset; // 在外层容器偏移量中增加额外的偏移量

            // 滚动两个容器到指定位置
            innerContainer.scrollTo({
                top: innerOffsetTop,
                behavior: 'smooth',
            });
            outerContainer.scrollTo({
                top: outerOffsetTop,
                behavior: 'smooth',
            });
        } else {
            console.error('目标元素或容器未找到');
        }
    };

    // 下一份按钮点击事件
    const handleNextItem = () => {
        const nextIndex = currentIndex + 1;
        const state = location.state.data;

        if (nextIndex < state.length) {
            setCurrentIndex(nextIndex);
            const nextItem = state[nextIndex];
            setCurrentItem(nextItem);
            listVoByAnswerId(nextItem.id);
        } else {
            message.info("没有更多题目了！");
        }
    };

    const handleSave = () => {
        preserveAPI(currentItem.id)
    };

    const [content, setContent] = useState("");
    const editorRef = useRef(null);
    const [selectedText, setselectedText] = useState('')
    const [selectedValue, setselectedValue] = useState([])
    const [textAreaValue, setTextAreaValue] = useState(""); // TextArea 的值
    const selectionRangeRef = useRef<Range | null>(null); // 存储选区范围
    const [InputNumberscore, setInputNumberScore] = useState(0); // 用于存储 InputNumber 的值

    const handleOk = () => {
        // if (selectionRangeRef.current && selectedText) {
        //     // 确认时添加波浪线
        //     const range = selectionRangeRef.current;
        //     const span = document.createElement("span");
        //     span.style.textDecoration = "underline wavy red";
        //     span.style.textDecorationColor = "red";
        //     span.textContent = selectedText;

        //     range.deleteContents();
        //     range.insertNode(span);
        // }
        const userInfoString = localStorage.getItem("userinfo"); // 获取数据
        const parsedUserInfo = JSON.parse(userInfoString); // 转换为对象
        setIsModalOpen(false);
        var data = {
            answerQuestionId: selectedValue?.answerQuestion[0].id,
            content: textAreaValue,
            teacherHeadUri: parsedUserInfo?.avatar ? parsedUserInfo.avatar : '',
            text: selectedText,
            // questionId: selectedValue?.question.id
            questionId: SubjectiveQuestion ? SubjectiveQuestion : selectedValue?.question.id,
        }
        insertAPI(data)
        settopicid(selectedValue?.answerQuestion[0].answerId)
        handleToggleEdit()
        setIsModalOpen(false);
        setIsEditable(false);
        setIsEditableID('')
        setTextAreaValue('');
        listVoByAnswerIddata(selectedValue.answerQuestion[0]?.answerId);
        // listVoByAnswerId(selectedValue.answerQuestion[0]?.answerId);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
        setIsEditable(false);
        setIsEditableID('')
        // 清除鼠标选中的文本
        const selection = window.getSelection();
        if (selection) {
            setIsEditable(false);
            setIsEditableID('')
            selection.removeAllRanges(); // 清除选中的文本
            setTextAreaValue('');
        }
    };

    const handleTextAreaChange = (e: any) => {
        setTextAreaValue(e.target.value); // 更新 TextArea 的值
    };

    const [SubjectiveQuestion, setSubjectiveQuestion] = useState('')

    const handleContextMenu = (event: any, value: any, qIndex: any, title: any) => {
        event.preventDefault(); // 阻止默认右键菜单

        // 获取选中的文本
        const selection = window.getSelection();
        const selectedTexts = selection.toString();

        if (selectedTexts) {
            // 如果选中内容存在，设置为可编辑
            setsectionId(value.question.id)
            setIsEditableID(value.question.id)
            setIsEditable(true);
            setselectedText(selectedTexts);
            setselectedValue(value);
            if (title == 'nosubjectivity') {
                setSubjectiveQuestion(value.question.groupQuestions[qIndex].id,)
            } else {
                setSubjectiveQuestion('')
            }

            const range = selection?.getRangeAt(0);
            selectionRangeRef.current = range; // 将范围保存到 Ref 中
        } else {
            // 如果没有选中内容，设置为不可编辑
            setIsEditable(false);
            setIsEditableID('')
        }

        // 如果不可编辑则不处理右键事件
        if (!isEditable) return;
    };



    const YourComponent = (item: any, id: any, index: any, title: any) => { // 添加
        var questionId = ''
        if (title == 'nosubjectivity') {
            questionId = item.question.groupQuestions[index].id
        } else {
            questionId = item?.question.id
        }
        const result = fileLists[id] || [];
        const fileUris = result.map(item => item?.response.data.httpPath);
        if ((Array.isArray(fileUris) && fileUris.length > 0) || (cleanData && cleanData.trim() !== "")) {
            const userInfoString = localStorage.getItem("userinfo");
            const parsedUserInfo = JSON.parse(userInfoString);
            setsectionId(item.question.id)
            if (item.answerQuestion === null) {
                settopicid('')
            } else {
                settopicid(item.answerQuestion[0]?.id)
                var data = {
                    answerQuestionId: item.answerQuestion[0]?.id,
                    content: cleanData,
                    teacherHeadUri: parsedUserInfo?.avatar ? parsedUserInfo.avatar : '',
                    fileUri: fileUris,
                    // questionId: item.question.id,
                    questionId: questionId ? questionId : item?.question.id,
                };
                insertAPI(data);
                // listVoByAnswerId(currentItem.id)
                listVoByAnswerIddata(item.answerQuestion[0]?.answerId);
                setSubjectiveQuestion('')
            }

        } else {
            message.warning("批阅内容必须有值才能添加");
        }
    }



    const handleToggleEdit = () => {
        setIsModalOpen(true);
    };


    const handleScoreSubmit = (item_one: any) => {
        console.log(item_one.answerQuestion, '1112');
        if (item_one.answerQuestion === null) {
        } else {
            if (InputNumberscore > item_one.question.score) {
                message.warning("分值不能超过该题总分");
            } else {
                var Data = {
                    id: item_one?.answerQuestion[0]?.id,
                    score: InputNumberscore
                }
                if (item_one.answerQuestion.id == 0) return
                testAnswerQuestionscoreAPI(Data)
            }

        }

    };
    const [fileList, setFileList] = useState<any>([]);
    const [cleanData, setcleanData] = useState<any>();
    const [fileUri, setfileUri] = useState<Record<string, any[]>>({});
    // 添加附件
    const uploadRef = useRef<any>(null);
    // 添加
    const [fileLists, setFileLists] = useState<Record<string, any[]>>({}); // 用于存储每道题的附件列表
    const [fileListID, setfileListID] = useState(null); // 用于存储每道题的附件列表

    const topicChange = (e: any, title: any, questionId: any) => {
        setsectionId(questionId)
        const data = e.level?.content;
        const cleanData = data.replace(/<\/?p>/g, '');
        setcleanData(cleanData)
    };

    const handleFileChange = (fileListID: string, info: any) => {
        setsectionId(fileListID)
        const questionElement = questionRefs.current[fileListID];
        if (questionElement) {
            const container = document.querySelector('.scflow');
            const rect = questionElement.getBoundingClientRect();
            if (container) {
                const containerScrollTop = container.scrollTop;
                const offset = 300;
                const scrollTop = containerScrollTop + rect.top - offset;

                container.scrollTo({
                    top: scrollTop,
                    behavior: 'smooth',
                });
            }
        }
        setFileLists((prev) => ({
            ...prev,
            [fileListID]: info.fileList, // 根据 questionId 更新对应题目的附件列表
        }));
        if (info.file.status === 'done') {
            console.log(`题目 ${fileListID} 上传接口返回的数据:`, info.file.response);
            message.success(`${info.file.name} 上传成功`);
            setfileUri((prev: any) => ({
                ...prev,
                [fileListID]: info.fileList,
            }));
        } else if (info.file.status === 'error') {
            message.error(`${info.file.name} 上传失败.`);
        }
    };

    const uploadProp = (fileListID: string) => ({
        name: 'file',
        action: '/rman/v1/upload/reference/material/import',
        headers: {
            authorization: 'authorization-text',
        },
        beforeUpload: (file: any) => {
            const isLt100M = file.size / 1024 / 1024 < 100;
            const isValidType = ['image/png', 'image/jpeg', 'image/jpg', 'audio/mpeg', 'audio/wav'].includes(file.type);

            if (!isLt100M) {
                message.error('上传文件不能超过100M');
                return false;
            }
            if (!isValidType) {
                message.error('只能上传图片或音频文件');
                return Upload.LIST_IGNORE;
            }
            return true;
        },
        onChange: (info: any) => handleFileChange(fileListID, info),
        fileList: fileLists[fileListID] || [], // 使用对应题目的附件列表
    });


    const [currentImage, setCurrentImage] = useState([]);
    const [audios, setAudios] = useState([]);
    // 添加 selectedType 状态，记录当前点击的类型
    const [selectedType, setSelectedType] = useState<string>('');

    const classifyFiles = (fileUris: string[]) => {
        // 定义图片和音频的后缀
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp'];
        const audioExtensions = ['.mp3', '.wav', '.ogg', '.flac'];

        // 用来存放分类后的图片和音频
        let images: string[] = [];
        let audios: string[] = [];

        // 分类
        fileUris?.forEach((file: string) => {
            if (imageExtensions.some(ext => file.endsWith(ext))) {
                images.push(file);  // 如果是图片，放入图片数组
            } else if (audioExtensions.some(ext => file.endsWith(ext))) {
                audios.push(file);  // 如果是音频，放入音频数组
            }
        });

        return { images, audios };
    };

    const handleFileClassification = (name: string, value: any) => {
        const { images, audios } = classifyFiles(value.fileUri);

        setCurrentImage(images);
        setAudios(audios);
        setVisible(true);
    };


    const ondelete = (name: string, value: any) => {
        if (name === 'Delete') {
            Modal.confirm({
                content: '确定是否删除？',
                title: '删除确认',
                icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
                onOk: () => {
                    criticismdeleteAPI(value.id)
                },
            });
        } else if (name === 'Picture') {
            handleFileClassification('Picture', value);
            setSelectedType('Picture')
        } else if (name === 'Audio') {
            handleFileClassification('Audio', value);
            setSelectedType('Audio')
        }
    }

    //删除上传附件
    const onConfirmdealt = (index: number, questionId: any) => {
        if (fileUri[questionId]) {
            const updatedFileLists = { ...fileLists };
            updatedFileLists[questionId].splice(index, 1);
            if (updatedFileLists[questionId].length === 0) {
                delete updatedFileLists[questionId];
            }

            setfileUri(updatedFileLists);
        }
    };

    const handleCancelTUP = () => {
        setVisible(false)
    }



    return (
        <div className='exampage' >
            <PageHeader
                className='header'
                ghost={false}
                style={{ padding: '10px 20px' }}
                onBack={() => window.history.back()}
                title="答题详情"
                extra={[
                    <Button key="3" onClick={handleNextItem} >跳过</Button>,
                    <Button key="2" onClick={handleNextItem} >下一份</Button>,
                    <Button key="1" onClick={handleSave} type="primary">
                        保存
                    </Button>,
                ]}
            ></PageHeader>
            <div style={{ padding: '20px' }}  >
                <Row gutter={16}>
                    <Col span={6}>
                        <div className='card' >
                            <div className='card_name'>
                                <div className='timu_name'>
                                    <span className='name_padding'>答题卡</span>(
                                    <span className='blues'>
                                        {totalUnreadCount}
                                    </span>
                                    /{totalDetailsCount})
                                </div>
                                <div className='name_cr'>
                                    <div className='cr' >
                                        <div>已阅</div>
                                        <div className='name_bor'></div>
                                    </div>
                                    <div className='cr'>
                                        <div>
                                            未阅</div>
                                        <div className='name_bor reds'></div>
                                    </div>
                                </div>
                            </div>
                            <div className='bt_card'>
                                {(() => {
                                    return (
                                        <QuestionButtons
                                            data={Data}
                                            globalIndex={globalIndex}
                                            butncard={butncard}
                                            butnQuestionsB={butnQuestionsB}
                                        />
                                    );
                                })()}
                            </div>
                        </div>
                    </Col>
                    <Col span={12}>
                        <div className='card scflow'  >
                            <div className='flexs'>
                                <div className='middle'>
                                    <div className='middle_name' >
                                        <div className='dlename fw' >{currentItem?.name}</div>
                                        <div className='mato' >
                                            <span>满分:  </span> <span className='fw400'> {currentItem?.totalScore ? currentItem?.totalScore?.toFixed(2) : 0}分</span>
                                            <span style={{ marginLeft: '40px' }} >提交人 : <span className='fw400'>{currentItem?.createUserName}</span></span>
                                        </div>
                                    </div>
                                    <div>
                                        <span className='name_point'>得分: <span className='fs'>{TotalScore}</span></span>
                                    </div>
                                </div>
                            </div>
                            {
                                Data?.map((item: any, index: number) => {
                                    return (
                                        <div className="content_row" key={index}>
                                            <div className="content">
                                                <h2>{item.name}</h2>


                                                <div className="questions_list">
                                                    {item?.details.map((item_one: any, qIndex: number) => {
                                                        const questionId = item_one.question.id;

                                                        const globalIndextwo = globalIndexs++;
                                                        if (item_one.question.questions_type !== 5) {
                                                            const globalIndexsie = globalIndexflase++;
                                                        }
                                                        return (
                                                            <div className="answers"
                                                                key={qIndex}
                                                                ref={(el) => questionRefs.current[questionId] = el} >
                                                                <div className={sectionId === item_one.question.id ? "type_other" : "fs"} >
                                                                    <span className="type" >
                                                                        {convertToChinaNum(globalIndextwo)}、
                                                                    </span>
                                                                    <span    >{`${examType.optionType_[item_one.question.questions_type]}题`}  </span>
                                                                    (
                                                                    <span>
                                                                        {`本题: ${item_one.question.questions_type == 5
                                                                            ? item_one.question.groupQuestions.reduce((total: any, group: { groupQuestionScore: any; }) => total + (group.groupQuestionScore ?? 0), 0)?.toFixed(2)
                                                                            : item_one.question.score?.toFixed(2) ?? 0
                                                                            }分`}
                                                                    </span>

                                                                    )
                                                                    <div className="form_item_header">
                                                                        <span className="tag"></span>
                                                                        <RenderHtml cname="auto-img"
                                                                            value={item_one.question.questions_content}>
                                                                        </RenderHtml>
                                                                    </div>
                                                                </div>

                                                                <div className="type_xz" >
                                                                    {item_one.question.questions_type === 0 ? ( // 单选题
                                                                        <div>

                                                                            <Radio.Group value={item_one?.answerQuestion?.length > 0 ? item_one.answerQuestion[0].answer : '默认值'} >
                                                                                {item_one.question.questions_options.map((item_0: any, index_0: number) => {
                                                                                    return (
                                                                                        <div className="answer_item" key={index_0}>
                                                                                            <Radio value={String.fromCharCode(64 + Number(index_0 + 1))}>
                                                                                                {String.fromCharCode(64 + Number(index_0 + 1))}
                                                                                            </Radio>
                                                                                            <RenderHtml
                                                                                                cname="radio_content spcialDom"
                                                                                                value={item_0.content}
                                                                                            />
                                                                                        </div>
                                                                                    );
                                                                                })}
                                                                            </Radio.Group>
                                                                            <div className='Answer' >
                                                                                <div className='Answer_item' >
                                                                                    <span>正确答案：</span>
                                                                                    <span className='fs' >{item_one.question.questions_answers ? item_one.question.questions_answers : '无'}</span>
                                                                                </div>
                                                                                <div className="answer_item" >
                                                                                    <span>答案解析：</span>
                                                                                    <RenderHtml cname="auto-img"
                                                                                        value={item_one.question.questions_analysis ? item_one.question.questions_analysis : '无'}>
                                                                                    </RenderHtml>
                                                                                </div>
                                                                                <Space>
                                                                                    <span ><span style={{ color: 'red' }} >*</span> 得分：</span>
                                                                                    <InputNumber
                                                                                        disabled={true}
                                                                                        value={item_one?.answerQuestion?.[0]?.score ?? 0}
                                                                                        placeholder="请输入分数"
                                                                                    />
                                                                                </Space>
                                                                            </div>
                                                                        </div>
                                                                    ) : item_one.question.questions_type === 1 ? ( // 多选题

                                                                        <div>
                                                                            <Checkbox.Group
                                                                                value={
                                                                                    Array.isArray(item_one?.answerQuestion) && item_one.answerQuestion[0]?.answer !== undefined
                                                                                        ? item_one.answerQuestion[0].answer
                                                                                        : ""
                                                                                }
                                                                            >
                                                                                {item_one.question.questions_options.map(
                                                                                    (item_1: any, index_1: number) => {
                                                                                        return (
                                                                                            <div className="answer_item" key={index_1}>
                                                                                                <Checkbox
                                                                                                    value={String.fromCharCode(
                                                                                                        64 + Number(index_1 + 1),
                                                                                                    )}
                                                                                                >
                                                                                                    {String.fromCharCode(64 + Number(index_1 + 1))}
                                                                                                </Checkbox>
                                                                                                <RenderHtml
                                                                                                    cname="spcialDom"
                                                                                                    value={item_1.content}
                                                                                                ></RenderHtml>
                                                                                            </div>
                                                                                        );
                                                                                    },
                                                                                )}
                                                                            </Checkbox.Group>
                                                                            <div className='Answer' >
                                                                                <div className='Answer_item' >
                                                                                    <span>正确答案：</span>
                                                                                    <span className='fs' >{item_one.question.questions_answers ? item_one.question.questions_answers : '无'}</span>
                                                                                </div>
                                                                                <div className="answer_item" >
                                                                                    <span>答案解析：</span>
                                                                                    <RenderHtml cname="auto-img"
                                                                                        value={item_one.question.questions_analysis ? item_one.question.questions_analysis : '无'}>
                                                                                    </RenderHtml>
                                                                                </div>
                                                                                <Space>
                                                                                    <span ><span style={{ color: 'red' }} >*</span> 得分：</span>
                                                                                    <InputNumber
                                                                                        disabled={true}
                                                                                        value={item_one?.answerQuestion?.[0]?.score ?? 0}
                                                                                        placeholder="请输入分数"
                                                                                    />
                                                                                </Space>
                                                                            </div>
                                                                        </div>
                                                                    ) : item_one.question.questions_type === 2 ? ( // 填空题
                                                                        <div>
                                                                            <div className='Answer_item' style={{ display: 'flex', alignItems: 'center' }} >
                                                                                <div>学生答案:</div>
                                                                                <div style={{ marginLeft: '20px' }} >
                                                                                    <RenderHtml
                                                                                        cname="spcialDom"
                                                                                        value={item_one?.answerQuestion?.[0]?.answer}
                                                                                    ></RenderHtml>
                                                                                </div>
                                                                            </div>
                                                                            <div className='Answer' >
                                                                                <div className='Answer_item' style={{ display: 'flex', alignItems: 'center' }}  >
                                                                                    <span>正确答案：</span>
                                                                                    <div className="answer-container" onClick={() => console.log("queItem:", item_one)} >
                                                                                        {item_one.question.questions_options.map((item_2: any, index_2: number) => {
                                                                                            const blankAnswer =
                                                                                                item_2?.answerRange === 2
                                                                                                    ? `${item_2.answerMin}~${item_2.answerMax}`
                                                                                                    : item_2.content;

                                                                                            return (
                                                                                                <div className=" blanks" key={index_2} style={{ marginLeft: '20px' }}>
                                                                                                    <span>{`第${index_2 + 1}空：`}</span>
                                                                                                    <RenderHtml
                                                                                                        value={blankAnswer}
                                                                                                    ></RenderHtml>
                                                                                                </div>
                                                                                            );
                                                                                        })}
                                                                                    </div>
                                                                                </div>
                                                                                <div className="answer_item" >
                                                                                    <span>答案解析：</span>
                                                                                    <RenderHtml cname="auto-img"
                                                                                        value={item_one.question.questions_analysis ? item_one.question.questions_analysis : '无'}>
                                                                                    </RenderHtml>
                                                                                </div>
                                                                                <Space>
                                                                                    <span ><span style={{ color: 'red' }} >*</span> 得分：</span>
                                                                                    <InputNumber
                                                                                        disabled={true}
                                                                                        value={item_one?.answerQuestion?.[0]?.score ?? 0}
                                                                                        placeholder="请输入分数"
                                                                                    />
                                                                                </Space>
                                                                            </div>
                                                                        </div>

                                                                    ) : item_one.question.questions_type === 3 ? ( // 主观题
                                                                        <div >

                                                                            <div style={{ display: 'flex', alignItems: 'center' }} >
                                                                                <span>答案：</span>
                                                                                <div className='rich' >


                                                                                    {(() => {
                                                                                        const answer = item_one?.answerQuestion?.[0]?.answer || ''; // 当前内容
                                                                                        const highlightedContent = answer.replace(
                                                                                            new RegExp(`(${buttonReturnedString})`, 'g'),
                                                                                            `<span style="text-decoration: underline wavy red; text-decoration-thickness: 2px; color: inherit;">$1</span>`
                                                                                        );


                                                                                        return (
                                                                                            <div
                                                                                                onContextMenu={(event) => handleContextMenu(event, item_one, qIndex, 'subjectivity')}
                                                                                                className="rich-text-editor"
                                                                                                ref={editorRef}
                                                                                                dangerouslySetInnerHTML={{
                                                                                                    __html: highlightedContent, // 替换后的内容
                                                                                                }}
                                                                                                onInput={(e) => setContent(e.target.innerHTML)} // 同步内容
                                                                                            />
                                                                                        );
                                                                                    })()}
                                                                                    {/* 批阅按钮 */}
                                                                                    {isEditable && isEditableID === item_one.question.id && (
                                                                                        <button
                                                                                            onClick={handleToggleEdit}
                                                                                            className="review-button"
                                                                                        >
                                                                                            <div className="reviewtext" >批阅内容 </div>
                                                                                        </button>
                                                                                    )}

                                                                                </div>
                                                                            </div>
                                                                            <div style={{ display: 'flex', alignItems: 'center' }} >
                                                                                <span>批阅： </span>
                                                                                <div style={{ width: '90%', marginTop: '20px' }} >

                                                                                    <Editorfs
                                                                                        name={`topic_${questionId}`}
                                                                                        onChange={(e: any) => topicChange(e, 'topic', item_one.question.id)}
                                                                                        addBlanks={undefined}
                                                                                        addFile={() => {
                                                                                            uploadRef.current?.click();
                                                                                            setfileListID(item_one.question.id)
                                                                                        }}
                                                                                        addtupFile={() => {
                                                                                            uploadRef.current?.click();
                                                                                            setfileListID(item_one.question.id)
                                                                                        }}
                                                                                        textSetting={{
                                                                                            max: 5000,
                                                                                            spaces: true,
                                                                                            toast: function () {
                                                                                                message.info(`题目输入不能超过${this.max}个字`);
                                                                                            },
                                                                                        }}
                                                                                    />
                                                                                    <div className={`enclosure${fileList.length > 0 ? '' : ' hidden'}`}>
                                                                                        <span className="label">附件列表:</span>
                                                                                        <Upload {...uploadProp(fileListID)} key={questionId} >
                                                                                            <Button
                                                                                                ref={uploadRef}
                                                                                                icon={<PaperClipOutlined />}
                                                                                                type="ghost"

                                                                                            >
                                                                                                添加附件
                                                                                            </Button>
                                                                                        </Upload>
                                                                                    </div>

                                                                                    {/* 其他内容 */}
                                                                                    <div>
                                                                                        {Object.keys(fileUri).map(key => {
                                                                                            if (key === questionId) {
                                                                                                return (
                                                                                                    <div key={key}>
                                                                                                        {fileUri[key]?.map((item, index) => (
                                                                                                            <div key={item.id} style={{ display: 'flex' }}>                                                                                                                    <div style={{ marginLeft: '10px' }}>文件名称: {item.name}</div>
                                                                                                                <div style={{ marginLeft: '10px' }}>
                                                                                                                    <Popconfirm
                                                                                                                        title="确定要删除吗?"
                                                                                                                        onConfirm={(e) => {

                                                                                                                            onConfirmdealt(index, questionId);
                                                                                                                        }}
                                                                                                                        okText="确定"
                                                                                                                        cancelText="取消"
                                                                                                                    >
                                                                                                                        <Image
                                                                                                                            width={16}
                                                                                                                            height={16}
                                                                                                                            src={require('@/images/icons/sc.png')}
                                                                                                                            title="删除"
                                                                                                                            preview={false}
                                                                                                                        />
                                                                                                                    </Popconfirm>
                                                                                                                </div>
                                                                                                            </div>
                                                                                                        ))}

                                                                                                    </div>
                                                                                                );
                                                                                            }
                                                                                            return null;
                                                                                        })}
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                            <div style={{ display: 'flex', justifyContent: 'flex-end', paddingRight: '21px' }}>
                                                                                <Button
                                                                                    onClick={() => YourComponent(item_one, item_one.question.id, qIndex, 'subjectivity')}
                                                                                    type="primary"

                                                                                    className='add_button'
                                                                                >
                                                                                    确认添加
                                                                                </Button>
                                                                            </div>


                                                                            <div className='Answer' >

                                                                                <div className="answer_item" >
                                                                                    <span>答案解析：</span>
                                                                                    <RenderHtml cname="auto-img"
                                                                                        value={item_one.question.questions_analysis ? item_one.question.questions_analysis : '无'}>
                                                                                    </RenderHtml>
                                                                                </div>
                                                                                <Space>
                                                                                    <InputNumber
                                                                                        value={item_one?.answerQuestion?.[0]?.score || 0}
                                                                                        onChange={(value) => setInputNumberScore(value)} // 监听分数输入变化
                                                                                        placeholder="请输入分数"
                                                                                        style={{ width: '110px' }}
                                                                                        min={0}
                                                                                    />
                                                                                    <Button
                                                                                        type="primary"
                                                                                        className='add_button'

                                                                                        onClick={() => handleScoreSubmit(item_one)}                                                                                        >
                                                                                        打分
                                                                                    </Button>
                                                                                </Space>
                                                                            </div>
                                                                        </div>
                                                                    ) : item_one.question.questions_type === 5 ? ( //题组
                                                                        <div className="yul_scflow" style={{ height: '100%' }} key={index}>

                                                                            {item_one?.question.groupQuestions?.map((queItem: any, qIndexs: any) => {
                                                                                const globalIndexsie = globalIndexflase++; // 每次递增
                                                                                return (
                                                                                    <div className="content_row" key={queItem.id} ref={(el) => (questionRefsB.current[queItem.id] = el)}  >
                                                                                        <div className={groupstionsB == queItem.id ? "type_other " : "type"} >
                                                                                            <span className='fs' >
                                                                                                {globalIndexsie + 1}.
                                                                                            </span>
                                                                                            <span className='fs color30'>{`${examType.optionType_[queItem.questions_type]}题`}  </span>
                                                                                            (
                                                                                            <span  >{`本题: ${queItem.groupQuestionScore?.toFixed(2) ?? 0
                                                                                                }分`}</span>
                                                                                            )


                                                                                            <div className="form_item_header">
                                                                                                <span className="tag"></span>
                                                                                                <RenderHtml cname="auto-img"
                                                                                                    value={queItem.questions_content}>
                                                                                                </RenderHtml>
                                                                                            </div>

                                                                                        </div>
                                                                                        {queItem.fileList?.length > 0 && (
                                                                                            <div className="fileList_">
                                                                                                <span>题目附件：</span>
                                                                                                <div>
                                                                                                    {queItem.fileList.map((item: any, index: number) => {
                                                                                                        return (
                                                                                                            <a
                                                                                                                href={item.attachmentSource}
                                                                                                                key={index}
                                                                                                                target={item.attachmentSource}
                                                                                                                title={item.attachmentName || ''}
                                                                                                            >
                                                                                                                {item.attachmentName || ''}
                                                                                                            </a>
                                                                                                        );
                                                                                                    })}
                                                                                                </div>
                                                                                            </div>
                                                                                        )}
                                                                                        <div className="answers">
                                                                                            {queItem.questions_type === 0 ? ( //单选
                                                                                                <Radio.Group value={
                                                                                                    item_one?.answerQuestion?.find((answerItem: any) => answerItem.questionId == queItem.id)?.answer
                                                                                                    || ''
                                                                                                }>
                                                                                                    {queItem.questions_options.map(
                                                                                                        (item_0: any, index_0: number) => {
                                                                                                            return (
                                                                                                                <div onClick={() => console.log("queItem:", item_one?.answerQuestion, queItem.id)} className="answer_item" key={index_0}>
                                                                                                                    <Radio
                                                                                                                        value={String.fromCharCode(
                                                                                                                            64 + Number(index_0 + 1),
                                                                                                                        )}
                                                                                                                    >
                                                                                                                        {String.fromCharCode(64 + Number(index_0 + 1))}
                                                                                                                    </Radio>
                                                                                                                    <RenderHtml
                                                                                                                        cname="radio_content auto-img"
                                                                                                                        value={item_0.content}

                                                                                                                    ></RenderHtml>
                                                                                                                </div>
                                                                                                            );
                                                                                                        },
                                                                                                    )}
                                                                                                </Radio.Group>
                                                                                            ) : queItem.questions_type === 1 ? ( //多选
                                                                                                <Checkbox.Group value={
                                                                                                    item_one?.answerQuestion?.find((answerItem: any) => answerItem.questionId == queItem.id)?.answer
                                                                                                    || ''
                                                                                                }>
                                                                                                    {queItem.questions_options.map(
                                                                                                        (item_1: any, index_1: number) => {
                                                                                                            return (
                                                                                                                <div className="answer_item" key={index_1}>
                                                                                                                    <Checkbox
                                                                                                                        value={String.fromCharCode(
                                                                                                                            64 + Number(index_1 + 1),
                                                                                                                        )}
                                                                                                                    >
                                                                                                                        {String.fromCharCode(64 + Number(index_1 + 1))}
                                                                                                                    </Checkbox>
                                                                                                                    <RenderHtml
                                                                                                                        cname="auto-img"
                                                                                                                        value={item_1.content}
                                                                                                                    ></RenderHtml>
                                                                                                                </div>
                                                                                                            );
                                                                                                        },
                                                                                                    )}
                                                                                                </Checkbox.Group>
                                                                                            ) : queItem.questions_type === 2 ? ( // 填空题
                                                                                                queItem.questions_options.map(
                                                                                                    (item_2: any, index_2: number) => {
                                                                                                        const dataRange = `${item_2?.answerMin}~${item_2?.answerMax}`;
                                                                                                        return (
                                                                                                            <div className="answer_item blanks" key={index_2}>
                                                                                                                <span>{`第${index_2 + 1}空：`}</span>

                                                                                                                <RenderHtml
                                                                                                                    cname="auto-img"

                                                                                                                    value={item_2.content || dataRange}
                                                                                                                ></RenderHtml>
                                                                                                            </div>
                                                                                                        );
                                                                                                    },
                                                                                                )
                                                                                            ) : queItem.questions_type === 3 ? ( // 主观题
                                                                                                <div key={index}>

                                                                                                    <div style={{ display: 'flex', alignItems: 'center' }} >
                                                                                                        <span>答案：</span>
                                                                                                        <div className='rich' >

                                                                                                            {(() => {
                                                                                                                const answer = item_one?.answerQuestion?.[0]?.answer || '';

                                                                                                                const highlightedContent = answer.replace(
                                                                                                                    new RegExp(`(${buttonReturnedString})`, 'g'),
                                                                                                                    `<span style="text-decoration: underline wavy red; text-decoration-thickness: 2px; color: inherit;">$1</span>`
                                                                                                                );


                                                                                                                return (
                                                                                                                    <div
                                                                                                                        onContextMenu={(event) => handleContextMenu(event, item_one, qIndexs, 'nosubjectivity')}
                                                                                                                        className="rich-text-editor"
                                                                                                                        ref={editorRef}
                                                                                                                        dangerouslySetInnerHTML={{
                                                                                                                            __html: highlightedContent, // 替换后的内容
                                                                                                                        }}
                                                                                                                        onInput={(e) => setContent(e.target.innerHTML)} // 同步内容
                                                                                                                    />
                                                                                                                );
                                                                                                            })()}
                                                                                                            {/* 批阅按钮 */}
                                                                                                            {isEditable && (
                                                                                                                <button
                                                                                                                    onClick={handleToggleEdit}
                                                                                                                    className="review-button"

                                                                                                                >
                                                                                                                    批阅内容
                                                                                                                </button>
                                                                                                            )}

                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div style={{ display: 'flex', alignItems: 'center' }} >
                                                                                                        <span>批阅：</span>
                                                                                                        <div style={{ width: '90%', marginTop: '20px' }} >
                                                                                                            <div>
                                                                                                                <Editorfs
                                                                                                                    name={'topic'}

                                                                                                                    onChange={(e: any) => topicChange(e, 'topic', item_one.question.id)}
                                                                                                                    addBlanks={undefined}

                                                                                                                    addFile={() => {
                                                                                                                        uploadRef.current?.click();
                                                                                                                        setfileListID(item_one.question.id)
                                                                                                                    }}
                                                                                                                    addtupFile={() => {
                                                                                                                        uploadRef.current?.click();
                                                                                                                        setfileListID(item_one.question.id)
                                                                                                                    }}
                                                                                                                    textSetting={{
                                                                                                                        max: 5000,
                                                                                                                        spaces: true,
                                                                                                                        toast: function () {
                                                                                                                            message.info(`题目输入不能超过${this.max}个字`);
                                                                                                                        },
                                                                                                                    }}
                                                                                                                />
                                                                                                            </div>
                                                                                                            <div className={`enclosure${fileList.length > 0 ? '' : ' hidden'}`}>
                                                                                                                <span className="label">附件列表:</span>
                                                                                                                <Upload {...uploadProp(fileListID)}>
                                                                                                                    <Button
                                                                                                                        ref={uploadRef}
                                                                                                                        icon={<PaperClipOutlined />}
                                                                                                                        type="ghost"
                                                                                                                    >
                                                                                                                        添加附件
                                                                                                                    </Button>
                                                                                                                </Upload>
                                                                                                            </div>
                                                                                                            <div>
                                                                                                                {/* 附件 */}
                                                                                                                <div>
                                                                                                                    {Object.keys(fileUri).map(key => {
                                                                                                                        if (key === questionId) {
                                                                                                                            return (
                                                                                                                                <div key={key}>
                                                                                                                                    {fileUri[key]?.map((item, index) => (
                                                                                                                                        <div key={item.id} style={{ display: 'flex' }}>                                                                                                                    <div style={{ marginLeft: '10px' }}>文件名称: {item.name}</div>
                                                                                                                                            <div style={{ marginLeft: '10px' }}>
                                                                                                                                                <Popconfirm
                                                                                                                                                    title="确定要删除吗?"
                                                                                                                                                    onConfirm={(e) => {
                                                                                                                                                        onConfirmdealt(index, questionId);
                                                                                                                                                    }}
                                                                                                                                                    okText="确定"
                                                                                                                                                    cancelText="取消"
                                                                                                                                                >
                                                                                                                                                    <Image
                                                                                                                                                        width={16}
                                                                                                                                                        height={16}
                                                                                                                                                        src={require('@/images/icons/sc.png')}
                                                                                                                                                        title="删除"
                                                                                                                                                        preview={false}
                                                                                                                                                    />
                                                                                                                                                </Popconfirm>
                                                                                                                                            </div>
                                                                                                                                        </div>
                                                                                                                                    ))}

                                                                                                                                </div>
                                                                                                                            );
                                                                                                                        }
                                                                                                                        return null;
                                                                                                                    })}
                                                                                                                </div>                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                                                                                                        <Button
                                                                                                            onClick={() => YourComponent(item_one, item_one.question.id, qIndexs, 'nosubjectivity')}
                                                                                                            type="primary"
                                                                                                            shape="round"
                                                                                                        >
                                                                                                            确认添加
                                                                                                        </Button>
                                                                                                    </div>


                                                                                                    <div className='Answer'>
                                                                                                        <div className="answer_item" >
                                                                                                            <span>答案解析：</span>
                                                                                                            <RenderHtml cname="auto-img"
                                                                                                                value={item_one.question.questions_analysis ? item_one.question.questions_analysis : '无'}>
                                                                                                            </RenderHtml>
                                                                                                        </div>
                                                                                                        <Space>
                                                                                                            <InputNumber
                                                                                                                value={Array.isArray(item_one?.answerQuestion) && item_one.answerQuestion[0]?.score !== undefined
                                                                                                                    ? item_one.answerQuestion[0]?.score
                                                                                                                    : ""}

                                                                                                                onChange={(value) => setInputNumberScore(value)} // 监听分数输入变化
                                                                                                                placeholder="请输入分数"
                                                                                                                style={{ width: '110px' }}
                                                                                                                min={0}
                                                                                                            />
                                                                                                            <Button
                                                                                                                type="primary"
                                                                                                                onClick={() => handleScoreSubmit(item_one)}                                                                                        >
                                                                                                                打分
                                                                                                            </Button>
                                                                                                        </Space>
                                                                                                    </div>
                                                                                                </div>
                                                                                            ) : (
                                                                                                // 判断题
                                                                                                <Radio.Group value={
                                                                                                    item_one?.answerQuestion?.find((answerItem: any) => answerItem.questionId == queItem.id)?.answer
                                                                                                    || ''
                                                                                                }>
                                                                                                    {queItem.questions_options.map(
                                                                                                        (item_4: any, index_4: number) => {
                                                                                                            return (
                                                                                                                <div className="answer_item" key={index_4}>
                                                                                                                    <Radio
                                                                                                                        value={String.fromCharCode(
                                                                                                                            64 + Number(index_4 + 1),
                                                                                                                        )}
                                                                                                                    >
                                                                                                                        {String.fromCharCode(64 + Number(index_4 + 1))}
                                                                                                                    </Radio>
                                                                                                                    <div className="radio_content">
                                                                                                                        {item_4.content}
                                                                                                                    </div>
                                                                                                                </div>
                                                                                                            );
                                                                                                        },
                                                                                                    )}
                                                                                                </Radio.Group>
                                                                                            )}
                                                                                        </div>
                                                                                        <div className='Answer'>

                                                                                            {item_one.question.groupQuestions.map((item: any, index: number) => {
                                                                                                return (
                                                                                                    <div  >
                                                                                                        {item.id === queItem.id && item.questions_type !== 3 && (
                                                                                                            <>
                                                                                                                <div className="" onClick={() => console.log('Clicked queItem:', item.questions_type)} >
                                                                                                                    <span>正确答案：</span>
                                                                                                                    <span className='fs' >{item.questions_answers ? item.questions_answers : '无'}</span>

                                                                                                                </div>
                                                                                                                <div className="answer_item">
                                                                                                                    <span>答案解析：</span>
                                                                                                                    <RenderHtml cname="auto-img" value={item?.questions_analysis ? item?.questions_analysis : '无'} />
                                                                                                                </div>

                                                                                                                <span ><span style={{ color: 'red' }} >*</span> 得分：</span>
                                                                                                                <InputNumber
                                                                                                                    disabled={true}
                                                                                                                    value={item_one?.answerQuestion?.[index]?.score ?? 0}
                                                                                                                    placeholder="请输入分数"
                                                                                                                />
                                                                                                            </>
                                                                                                        )}
                                                                                                    </div>

                                                                                                );
                                                                                            })}
                                                                                        </div>

                                                                                    </div>
                                                                                );

                                                                            })}


                                                                        </div>

                                                                    ) : (
                                                                        // 判断题
                                                                        <div>
                                                                            <Radio.Group value={item_one.answerQuestion && item_one.answerQuestion.answer ? item_one.answerQuestion.answer[0] : undefined}
                                                                            >

                                                                                <Radio.Group
                                                                                    value={item_one?.answerQuestion?.[0]?.answer} // 选中的值
                                                                                >
                                                                                    {item_one.question.questions_options.map(
                                                                                        (item_4: any, index_4: number) => {
                                                                                            const optionValue = String.fromCharCode(64 + Number(index_4 + 1)); // A, B, C, D...

                                                                                            return (
                                                                                                <div className="answer_item" key={index_4}>
                                                                                                    <Radio value={optionValue}>
                                                                                                        {optionValue}
                                                                                                    </Radio>
                                                                                                    <div className="radio_content">
                                                                                                        {item_4.content}
                                                                                                    </div>
                                                                                                </div>
                                                                                            );
                                                                                        }
                                                                                    )}
                                                                                </Radio.Group>

                                                                            </Radio.Group>
                                                                            <div className='Answer' >
                                                                                <div className='Answer_item' >
                                                                                    <span>正确答案：</span>
                                                                                    <span className='fs' >{item_one.question.questions_answers ? item_one.question.questions_answers : '无'}</span>
                                                                                </div>
                                                                                <div className="answer_item" >
                                                                                    <span>答案解析：</span>
                                                                                    <RenderHtml cname="auto-img"
                                                                                        value={item_one.question.questions_analysis ? item_one.question.questions_analysis : '无'}>
                                                                                    </RenderHtml>
                                                                                </div>
                                                                                <Space>
                                                                                    <span ><span style={{ color: 'red' }} >*</span> 得分：</span>
                                                                                    <InputNumber
                                                                                        disabled={true}
                                                                                        value={item_one?.answerQuestion?.[0]?.score ?? 0}
                                                                                        placeholder="请输入分数"
                                                                                    />
                                                                                </Space>
                                                                            </div>
                                                                        </div>

                                                                    )}
                                                                </div>
                                                            </div>
                                                        );
                                                    })}
                                                </div>

                                            </div>
                                        </div>
                                    );
                                })
                            }

                        </div>
                    </Col>
                    <Col span={6}>
                        {/* 批阅记录 */}
                        <ReviewRecord
                            Cicismlist={Cicismlist}
                            RecordSelection={RecordSelection}
                            ondelete={ondelete}
                            butncard={butncard}
                            userinfo={userinfo}
                        />
                    </Col>
                </Row>
            </div >

            <Modal
                title={selectedType == 'Picture' ? '图片预览' : '音频'}
                open={visible}
                footer={null}
                onCancel={handleCancelTUP}>
                {selectedType === 'Picture' && (
                    <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        alignItems: 'center'
                    }}>
                        <Image.PreviewGroup>
                            {currentImage.map((image, index) => (
                                <div key={index} style={{ marginBottom: '10px' }}>
                                    <Image width={300} src={image} />
                                </div>
                            ))}
                        </Image.PreviewGroup>
                    </div>
                )}
                {selectedType === 'Audio' && (
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'center',
                            flexDirection: 'column',
                            alignItems: 'center'
                        }}>
                        {audios.map((audio, index) => (
                            <div key={index}>
                                <audio controls>
                                    <source src={audio} type="audio/mp3" />
                                    Your browser does not support the audio element.
                                </audio>
                            </div>
                        ))}
                    </div>
                )}
            </Modal>

            <Modal title='添加批阅记录' open={isModalOpen} onOk={handleOk} onCancel={handleCancel}>
                <p>{selectedText}</p>
                <TextArea
                    rows={4}
                    value={textAreaValue} // 绑定值
                    onChange={handleTextAreaChange} // 监听输入变化
                    placeholder="请输入批阅记录"
                />
            </Modal>
        </div >
    );
};

export default ExamPage;
