.CustomModal {
  .auto-img {
    z-index: 999;
    max-height: 100px;
    overflow: hidden;

    img {
      max-width: 500px;
      height: auto;
      vertical-align: bottom;
    }
  }

  .ant-modal-content {
    .ant-modal-body {
      .Tables {
        .ant-table-tbody {
          .ant-input {
            width: 130px !important;
            height: 33px;
            background: #FFFFFF;
            border-radius: 1px;
            border: 1px solid #CBCBCB;
          }

          .times {
            display: flex;
            align-items: baseline;

            .ant-select {
              width: 60px !important;
              height: 28px;
              
            }
          }
        }

      }

      .searchbox {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;

        .Itemflex {
          display: flex;
          align-items: center;

          .fz {
            margin-left: 6px;
          }
        }

        .custom_input {
          .ant-input {
            z-index: 9999;
            width: 100px !important;
            height: 28px;
            background: #FFFFFF;
            border-radius: 1px;
            border: 1px solid #CBCBCB;
          }
        }

        .ant-input {
          width: 170px !important;
          height: 28px;
          background: #FFFFFF;
          border-radius: 1px;
          border: 1px solid #CBCBCB;
        }

        >input.ant-input {
          width: 200px !important;
          display: inline-block;
          margin-right: 20px;
        }

        >.ant-select {
          width: 25%;
          margin-right: 20px;
        }
      }



      .ant-table-body {
        overflow-x: hidden;

        .ant-table-row {
          .ant-table-cell {
            p {
              margin-bottom: 0;
            }

            .spcialDom {
              max-height: 100px;
              overflow: hidden;

              img {
                max-width: 50px;
                height: auto;
                vertical-align: bottom;
              }
            }
          }
        }
      }
    }
  }
}
