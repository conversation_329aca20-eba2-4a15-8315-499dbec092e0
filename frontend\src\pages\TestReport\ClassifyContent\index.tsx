import examManageApis from '@/api/exam';
import { Button, Table, Tabs } from 'antd';
import dayjs from 'dayjs';
import { FC, useEffect, useState } from 'react';
import { history } from 'umi';
import './index.less';

interface IContentProp {
  /** tablist */
  tabList: any[];
}

const ClassifyContent: FC<IContentProp> = ({ tabList }) => {
  // 选中的tab
  const [selectTab, setSelectTab] = useState<string>(tabList?.[0]?.id);
  // 分页
  const [page, setPage] = useState<number>(1);
  const [size, setSize] = useState<number>(10);
  const [total, setTotal] = useState<number>(0);
  //  测验列表
  const [testList, setTestList] = useState<any[]>([]);

  useEffect(() => {
    if (tabList?.length > 0) {
      setSelectTab(tabList?.[0]?.id);
      getTabExamList(tabList?.[0]?.id, page, size);
    }
  }, [tabList]);

  // 获取对应tab的list
  const getTabExamList = (id: string, page: number, size: number) => {
    examManageApis
      .fetchExamReport({ page, size, classifyId: id })
      .then((res: any) => {
        if (res?.status == 200) {
          setTestList(res?.data?.data || []);
          setTotal(res?.data?.totalCount || 0);
        }
      });
  };

  // tab切换
  const handleChangeTab = (id: string) => {
    if (id !== selectTab) {
      setSelectTab(id);
      getTabExamList(id, 1, 10);
    }
  };
  // 分页变化
  const handlePageSizeChange = (page: number, size: number) => {
    getTabExamList(selectTab, page, size);
  };

  // 查看报告详情
  const handleToDetail = (item: any) => {
    history.push(
      `/reportdetail?id=${item.id}&name=${item.name}&subTime=${item.submitDate}&level=${item.difficulty ?? '无'}&answerTime=${item.answerMillisecond}&rank=${item.ranking}&total=${item.totalScore}&defeatThan=${item.defeatThan}&avg=${item.avgScore}&totalRanking=${item.totalRanking}&score=${item.score}&classifyId=${selectTab}`,
    );
  };

  // 格式化时间 传入得时间：毫秒
  const formatTime = (time: number) => {
    const seconds = Math.floor(time / 1000);
    const minutes = Math.floor(seconds / 60);
    return {
      m: minutes,
      s: seconds % 60,
    };
  };

  const columns = [
    {
      title: '试卷名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '作答时间',
      dataIndex: 'submitDate',
      key: 'submitDate',
      render: (text: number) => {
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '用时',
      dataIndex: 'answerMillisecond',
      key: 'answerMillisecond',
      render: (text: number) => {
        const time = formatTime(text);
        return `${time.m}分钟${time.s}秒`;
      },
    },
    {
      title: '正确率',
      dataIndex: 'scoringRate',
      key: 'scoringRate',
    },
    {
      title: '全站排行',
      dataIndex: 'rankAll',
      key: 'rankAll',
      render(_: any, record: any) {
        return `${record.ranking}/${record.totalRanking}`;
      },
    },
    {
      title: '报告',
      dataIndex: 'report',
      key: 'report',
      render: (_: any, record: any) => (
        <Button type="link" onClick={() => handleToDetail(record)}>
          查看报告
        </Button>
      ),
    },
  ];
  return (
    <div className="special_training_report_container">
      <div className="tab_container">
        <div style={{ overflow: 'hidden' }}>
          <Tabs
            activeKey={selectTab}
            tabPosition="top"
            style={{ height: 48 }}
            items={tabList.map((item, i) => {
              return {
                label: item.name,
                key: item.id,
              };
            })}
            onChange={handleChangeTab}
          />
        </div>
        {/* {tabList?.map((item) => {
          return (
            <div
              className={`tab_item ${
                selectTab === item.id ? 'tab_item_active' : ''
              }`}
              key={item.id}
              onClick={() => handleChangeTab(item.id)}
            >
              {item.name}
            </div>
          );
        })} */}
      </div>
      <div className="list_container">
        <Table
          columns={columns}
          dataSource={testList}
          pagination={{ onChange: handlePageSizeChange }}
        />
      </div>
    </div>
  );
};

export default ClassifyContent;
