import { Reducer } from 'redux';
import { Effect } from 'dva';
import { querymapinfo} from '@/api/questionset';

export interface ICourseMapModelState {
  mapinfo: any;
}

export interface CourseMapType {
  namespace: 'coursemap';
  state: ICourseMapModelState;
  effects: {
    fetchMapInfo: Effect;
  };

  reducers: {
    changemapinfo: Reducer<any>;
  };
}

const CourseMapType: CourseMapType = {
  namespace: 'coursemap',

  state: {
    mapinfo: {},
  },

  effects: {
    *fetchMapInfo({ payload }, { call, put }) {
      const { params } = payload;      
      const res = yield call(querymapinfo, params);
      yield put({
        type: 'changemapinfo',
        payload: { mapinfo: { ...res.data } },
      });
    },
  },

  reducers: {
    changemapinfo: (state: ICourseMapModelState, { payload }) => {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default CourseMapType;
