.favorites {
  .ant-picker {
    z-index: 99;
    width: 200 !important;
  }
}


.auto-img {



  img {
    max-width: 448px;
    // height: auto;
    vertical-align: bottom;
  }
}


.formyangs {
  padding-bottom: 30px;
  border-bottom: 1px dashed #c5c5c5;

  .ant-picker {
    z-index: 99;
    width: 200 !important;
  }
}

.ant-modal-body {
  .top {

    .answers {
      margin: 15px 0;
      margin-left: 10px;

      .answer_item {
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        margin-bottom: 10px;

        .radio_content {
          font-size: 14px;
          display: flex;

        }

        .spcialDom {
          flex: 1 1;

          img {
            max-width: 50px;
            height: auto;
            vertical-align: bottom;
          }
        }


      }

      .blanks {
        >span {
          margin-right: 10px;
        }
      }

    }

    .subject {

      .type {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 14px;
        color: #2A2A2A;
        line-height: 20px;
      }

      .subject_answers {

        .answer_item {
          display: flex;
          //   align-items: flex-start;
          //   flex-direction: row;
          margin-bottom: 10px;



          .spcialDom {
            flex: 1 1;

            img {
              max-width: 50px;
              height: auto;
              vertical-align: bottom;
            }
          }


        }
      }

    }



    .footer {
      width: 100%;
      background: #F7F9FA;
      border-radius: 4px;
      padding: 10px;

      .aws {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 14px;
        color: #2A2A2A;
        line-height: 20px;
      }

      .blus {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 14px;
        color: #549CFF;
      }

      .analysis {
        margin-top: 20px;
        color: #525252;
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
      }
    }
  }


}
