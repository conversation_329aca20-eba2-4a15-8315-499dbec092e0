import React, { FC, useState, useEffect } from 'react';
import { Modal, Tree, message, Tabs } from 'antd';
import type { DataNode } from 'antd/es/tree';
import { reqCourseChapterApi } from '@/api/questionset'
import type { TreeDataNode, TreeProps } from 'antd';
import CourseItem from './courseItem'

interface ChapterModalProps {
    modalVisible: boolean;
    onCloseModal: (val?: any) => void;
}
const ChapterModal: FC<ChapterModalProps> = ({
    modalVisible,
    onCloseModal,
}) => {
    const [chapterVisible, setChapterVisible] = useState<boolean>(false)

    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
    const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
    const [chapterList, setChapterList] = useState<any>([])
    const [selectCourse, setSelectCourse] = useState<any>({})
    const [chapterInfo, setChapterInfo] = useState<any>([])
    const [activeKey, setActiveKey] = useState<string>('1');
    const onChangeTabKey = (tabKey: any) => {
        setActiveKey(tabKey)
    }
    const onCheck: TreeProps['onCheck'] = (checkedKeys: any, info) => {
        setCheckedKeys(checkedKeys);
        const checkedNodes = info.checkedNodes
        // 将checkedNodes数组更具parentId组成树形数组
        const treeData = buildTree(checkedNodes)
        const chapter = treeData.map((item: any) => {
            return {
                key: item.id,
                name: item.name_,
                children: item.children?.map((chi: any) => {
                    return {
                        key: chi.id,
                        name: chi.name_
                    }
                })
            }
        })
        setChapterInfo(chapter ?? [])
    };

    const buildTree = (data: any) => {
        const idMap: any = {};
        const tree: any = [];
        data.forEach((item: any) => {
            idMap[item.id] = { ...item, children: [] };
        });
        data.forEach((item: any) => {
            const node = idMap[item.id];
            if (item.parentId === null || item.parentId === undefined || item.parentId == '') {
                tree.push(node);
            } else {
                const parent = idMap[item.parentId];
                if (parent) {
                    parent.children.push(node);
                }
            }
        });
        return tree;
    }

    // 获取章节
    const getChapterList = async () => {
        reqCourseChapterApi({
            courseId: selectCourse.id,
            status: selectCourse.courseStatus,
            courseSemester: selectCourse.courseSemester,
            courseType: selectCourse.courseType
        }).then((res: any) => {
            console.log(res)
            if (res?.status == 200) {
                // 去掉树形数组第三级
                let chapterData = res.data.map((item: any, i: number) => {
                    return {
                        ...item,
                        name_: item.name,
                        name: `第${i + 1}章 ${item.name}`,
                        children: item.children.map((child: any, i2: number) => {
                            return {
                                ...child,
                                name_: child.name,
                                name: `${i + 1}.${i2 + 1} ${child.name}`,
                                children: []
                            }
                        })
                    }
                })
                console.log(res.data, chapterData)
                setChapterList(chapterData)
            }
        })
    }
    return (
        <>
            <Modal
                width={'90%'}
                wrapClassName='chapter-modal-wrapper'
                title="选择课程章节"
                open={modalVisible}
                onCancel={() => {
                    onCloseModal(null)
                    setSelectCourse({})
                }}
                onOk={() => {
                    if (!selectCourse.id) {
                        message.error('请选择课程')
                        return
                    }
                    setChapterVisible(true)
                    getChapterList()
                }}
                okText="下一步"
            >
                <Tabs activeKey={activeKey} onChange={onChangeTabKey}>
                    <Tabs.TabPane tab="班级课" key="1">
                        <CourseItem
                            activeKey={activeKey}
                            emitSelectCourse={(val: any) => {
                                setSelectCourse(val)
                            }}
                        />
                    </Tabs.TabPane>
                    <Tabs.TabPane tab="公开课" key="2">
                        <CourseItem
                            activeKey={activeKey}
                            emitSelectCourse={(val: any) => {
                                setSelectCourse(val)
                            }}
                        />
                    </Tabs.TabPane>
                    {/* <Tabs.TabPane tab="微课" key="3">
                        <CourseItem
                        activeKey={activeKey}
                        emitSelectCourse={(val: any) => {
                            setSelectCourse(val)
                        }}
                        />
                    </Tabs.TabPane> */}
                    <Tabs.TabPane tab="培训课" key="4">
                        <CourseItem
                            activeKey={activeKey}
                            emitSelectCourse={(val: any) => {
                                setSelectCourse(val)
                            }}
                        />
                    </Tabs.TabPane>
                </Tabs>
            </Modal>
            <Modal
                width={600}
                wrapClassName='chapter-modal-tree-wrapper'
                title="选择课程章节"
                open={chapterVisible}
                onCancel={() => {
                    setChapterVisible(false)
                    setCheckedKeys([])
                }}
                onOk={() => {
                    // 判断是否选择了章节
                    if (checkedKeys.length == 0) {
                        message.error('请选择课程章节')
                        return
                    }
                    onCloseModal(chapterInfo)
                    setChapterVisible(false)
                }}
                okText="确定"
                cancelText="上一步"
            >
                <p className='course-name'>{selectCourse.name}</p>
                <Tree
                    checkable
                    expandedKeys={expandedKeys}
                    onExpand={(expandedKeysValue) => setExpandedKeys(expandedKeysValue)}
                    autoExpandParent={autoExpandParent}
                    checkedKeys={checkedKeys}
                    onCheck={onCheck}
                    fieldNames={{ title: 'name', key: 'id' }}
                    treeData={chapterList}
                />
            </Modal>
        </>
    );
};

export default ChapterModal;