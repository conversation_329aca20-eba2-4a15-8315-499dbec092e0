import paperManageApis from '@/api/Contract';
import TestSystemHeader from '@/components/TestSystemHeader';
import { FC, useEffect, useMemo, useRef, useState } from 'react';
import { history, useLocation, useSelector } from 'umi';
import useCountdown from '../AnswerPage/hook/useCountDown';
import FilePreview from './FilePreview';
import './index.less';
enum ETab {
  'notice' = 0,
  'rule' = 1,
  'guide' = 2,
}

const TimeUnit: any = {
  second: 1,
  minute: 60,
};

const ExamInstructions: FC = () => {
  const location: any = useLocation();
  const btnList = [
    {
      key: '0',
      name: '考生须知',
      address: '',
      stringValue: '',
    },
    {
      key: '1',
      name: '考场规则',
      address: '',
      stringValue: '',
    },
    {
      key: '2',
      name: '操作指南',
      address: '',
      stringValue: '',
    },
  ];
  const [showTabList, setShowTabList] = useState(btnList);
  const [activeTabItem, setActiveTabItem] = useState<any>();
  //   获取信息
  const [instructInfo, setInstructInfo] = useState<any>();
  const pageInfo = useRef<any>();
  // 格式化文件地址
  const formatUrl = (url: string) => {
    //
    if (url.indexOf('http') === -1) {
      if (process.env.NODE_ENV === 'development') {
        url = `http://**************${url}`;
      } else {
        url = `${location.origin}${url}`;
      }
    }
    return url;
  };
  //   解析配置的考试须知信息
  const getJSONInfo = (str: string) => {
    const obj = JSON.parse(str);
    return obj;
  };
  // 添加按钮
  const getBtnItem = (
    value: string,
    key: string,
    tabKey: 'notice' | 'rule' | 'guide',
  ) => {
    const itemBtn: any = {
      ...btnList[ETab[tabKey]],
    };
    itemBtn[key] = value;
    return itemBtn;
  };
  const getInstructInfo = (id: string) => {
    paperManageApis.getVoById(id).then((res: any) => {
      if (res?.status == 200) {
        setInstructInfo(res?.data);
        pageInfo.current = res?.data;
        const tabs: any[] = [];
        if (res?.data?.notice) {
          const noticObj = getJSONInfo(res?.data?.notice);
          if (noticObj?.address) {
            const realUrl = formatUrl(noticObj?.address);
            const itemBtn = getBtnItem(realUrl, 'address', 'notice');
            tabs.push(itemBtn);
          }
          if (noticObj?.stringValue) {
            const itemBtn = getBtnItem(
              noticObj?.stringValue,
              'stringValue',
              'notice',
            );
            tabs.push(itemBtn);
          }
        }
        if (res?.data?.rule) {
          const ruleObj = getJSONInfo(res?.data?.rule);
          if (ruleObj?.address) {
            const realUrl = formatUrl(ruleObj?.address);
            const itemBtn = getBtnItem(realUrl, 'address', 'rule');
            tabs.push(itemBtn);
          }
          if (ruleObj?.stringValue) {
            const itemBtn = getBtnItem(
              ruleObj?.stringValue,
              'stringValue',
              'rule',
            );
            tabs.push(itemBtn);
          }
        }
        if (res?.data?.guide) {
          const guideObj = getJSONInfo(res?.data?.guide);
          if (guideObj?.address) {
            const realUrl = formatUrl(guideObj?.address);
            const itemBtn = getBtnItem(realUrl, 'address', 'guide');
            tabs.push(itemBtn);
          }
          if (guideObj?.stringValue) {
            const itemBtn = getBtnItem(
              guideObj?.stringValue,
              'stringValue',
              'guide',
            );
            tabs.push(itemBtn);
          }
        }
        setActiveTabItem(tabs[0]);
        setShowTabList(tabs);
      }
    });
  };
  useEffect(() => {
    if (location?.query?.testId) {
      getInstructInfo(location.query?.testId);
    }
  }, [location?.query?.testId]);
  const handleToAnswer = () => {
    if (pageInfo.current?.explainFile) {
      history.replace(`/examrules?testId=${location.query?.testId}`);
      return;
    }
    history.replace(`/answerPage?testId=${location.query?.testId}`);
  };
  //#region 倒计时
  const partTimeLeft = useMemo(() => {
    if (!instructInfo?.readTime) {
      return 0;
    }
    const readTime = instructInfo?.readTime;
    const time = readTime?.time * TimeUnit[readTime?.unit || 'second'];
    return time;
  }, [instructInfo?.readTime]);
  const [_, partCountDown] = useCountdown({
    leftTime: (partTimeLeft || 0) * 1000,
    onEnd: () => {
      handleToAnswer();
    },
  });
  //#endregion
  const { title, logoUrl, isShow } = useSelector<any, any>(
    state => state.themes,
  );
  return (
    <div className="instruction_container">
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
           background: '#ffffff',
          boxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.07)'
        }}
        onClick={() => {
          window.location.href = '/exam/#/testsystem';
        }}
        className="header_clickable"
      >
        <span className='icon_box'>
          <img
            src={logoUrl || require('@/images/login/default_logo.png')}
            style={{
              height: '32px'
            }}
          />
        </span>
        <TestSystemHeader title="招警联考测试系统" />
      </div>
      <div className="content_container">
        <div className="instruction_info">
          <div className="test_name">{instructInfo?.name}</div>
          <div className="time_down">
            <div className="time_label">倒计时</div>
            <div className="time_item">{partCountDown?.minutes}</div>
            <div className="time_gap">:</div>
            <div className="time_item">{partCountDown?.seconds}</div>
          </div>
          <div className="tab_list">
            {showTabList.map((item) => {
              return (
                <div
                  key={item.key}
                  className={`tab_item ${activeTabItem?.key === item.key ? 'active' : ''
                    }`}
                  onClick={() => {
                    setActiveTabItem(item);
                  }}
                >
                  {item.name}
                </div>
              );
            })}
          </div>
          <div className="detail">
            {activeTabItem?.address ? (
              <FilePreview url={activeTabItem?.address} />
            ) : (
              <div className="detail_content">{activeTabItem?.stringValue}</div>
            )}
          </div>
          <div className="btn" onClick={handleToAnswer}>
            我已阅读
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExamInstructions;
