@import '../themes/variables.less';
ul, ol, li {
  margin: 0;
}
img {
  vertical-align: top;
}
.uf-exam-layout-wrapper {
  position: relative;
  height: 100%;
  &.exam-npu-container {
    padding-top: 4.375rem;
    background: url("~@/images/login/app-bg.jpg") no-repeat;
    background-size: 100% auto;
    // background-color:#0546d2;
    .uf-exam-layout-content {
      height: 100%;
    }
  }
  .uf-exam-layout-content {
    height: calc(100vh - 52px);
    display: flex;
    background-color: #F7F9FA;

    .uf-exam-left-part {
      width: 180px;
      
      height: 100%;
      flex-shrink: 0;
      background: white;
      box-shadow: none;
      border-right: 2px #F7F9FA solid;
      .ant-menu{
        padding-top: 12px;
        font-size: 16px;
        height: 100%;
        .ant-menu-item::after{
          border: 0;
        }
      }
      .ant-menu-item {
        padding-right: 0;
        margin: 0 13px;
        border-radius: 8px;
        width: calc(100% - 25px)!important;
        &.ant-menu-item-selected {
          background-color: var(--third-color)!important;
        }
        &:not(.ant-menu-item-selected) a {
          color: #525252;
        }
      }
      .ant-menu-root.ant-menu-inline {
        border: none;
      }
    }
    .uf-exam-right-part{
      overflow-x: hidden;
      height: 100%;
      position: relative;
      flex: 1;
      // box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.07);
      // margin: 24px;
      // background: #ffffff;
      .ant-input, .ant-select .ant-select-selector {
        border-color: #CBCBCB;
      }
      .ant-btn-link{
        color: #525252;
        &:hover{
          color:var(--primary-color)
        }
        &[disabled]{
          color: rgba(0, 0, 0, 0.25)
        }
      }
    }
  }
}
body .ant-input, body .ant-input-affix-wrapper, 
body .ant-btn:not(.ant-btn-round, .ant-btn-circle),
body .ant-select:not(.ant-select-customize-input) .ant-select-selector{
  border-radius: 6px;
}
body .ant-pagination-item, body .ant-pagination-options-quick-jumper input{
  border-radius: 5px;
}
