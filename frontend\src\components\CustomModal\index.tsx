import examManageApis from '@/api/exam';
import examType from '@/types/examType';
import { Button, Form, Input, message, Modal, Select, Switch, Table } from 'antd';
import React, { useEffect, useState } from 'react';
import RenderHtml from '../renderHtml';
import "./index.less";
import { useLocation } from 'umi'
import Contract from '@/api/Contract';
const { Option } = Select;


interface params {
    title: any,
    selectkeys?: any,
    visible: boolean,
    userData: any,  // 传递过来的表单数据
    updateData: (newUserData: any) => void;  // 更新数据的回调
    // callback: (data: any) => void,
    callback: () => void,
    onclose: () => void,
}



const CustomModal: React.FC<params> = ({ title, visible, callback, userData, updateData, onclose }) => {
    const [form] = Form.useForm(); // Ant Design 的表单实例
    const [dataSource, setDataSource] = useState(userData); // 本地状态存储父组件传来的数据

    // 当 userData 更新时，同步到本地的 dataSource
    useEffect(() => {
        setDataSource(userData);
    }, [userData]);

    // 更新
    const DeleteAPI = async (data: any) => {
        const res = await Contract.testSave(data);
        if (res.status === 200) {
            updateData(res.data);  // 通知父组件更新数据
            localStorage.setItem("userData", JSON.stringify(res.data));  // 将对象转化为字符串
        }
        message.success(res?.message);
    };

    // 定义表格列
    const columns = [
        {
            title: '部分',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '每题读题时长',
            dataIndex: 'readTime',
            key: 'readTime',
            render: (text: string | number | readonly string[] | undefined, record: any) => {
                return (
                    <div className="times">
                        <Input
                            type="number"
                            defaultValue={record.readTime?.time || ''}
                            onChange={(e) => handleInputChange(e.target.value, record.id, 'readTime')}
                            placeholder="输入读题时长"
                        />
                        <Select
                            defaultValue={record.readTime?.unit || 'minute'}
                            onChange={(value) => handleSelectChange(value, record.id, 'readTime')}
                            style={{ marginLeft: '10px' }}
                        >
                            <Option value="minute">分</Option>
                            <Option value="second">秒</Option>
                        </Select>
                    </div>
                );
            },
        },
        {
            title: '每题作答时长',
            dataIndex: 'answerTime',
            key: 'answerTime',
            render: (text: string | number | readonly string[] | undefined, record: any) => (
                <div className="times">
                    <Input
                        type="number"
                        defaultValue={record.answerTime?.time || ''}
                        onChange={(e) => handleInputChange(e.target.value, record.id, 'answerTime')}
                        placeholder="输入作答时长"
                    />
                    <Select
                        defaultValue={record.answerTime?.unit || 'minute'}
                        onChange={(value) => handleSelectChange(value, record.id, 'answerTime')}
                        style={{ marginLeft: '10px' }}
                    >
                        <Option value="minute">分</Option>
                        <Option value="second">秒</Option>
                    </Select>
                </div>
            ),
        },
        {
            title: '该部分答题最低总时长',
            dataIndex: 'minTime',
            key: 'minTime',
            render: (text: string | number | readonly string[] | undefined, record: any) => (
                <div className="times">
                    <Input
                        type="number"
                        defaultValue={record.minTime?.time || ''}
                        onChange={(e) => handleInputChange(e.target.value, record.id, 'minTime')}
                        placeholder="输入最低时长"
                    />
                    <Select
                        defaultValue={record.minTime?.unit || 'minute'}
                        onChange={(value) => handleSelectChange(value, record.id, 'minTime')}
                        style={{ marginLeft: '10px' }}
                    >
                        <Option value="minute">分</Option>
                        <Option value="second">秒</Option>
                    </Select>
                </div>
            ),
        },
        {
            title: '该部分答题最高总时长',
            dataIndex: 'maxTime',
            key: 'maxTime',
            render: (text: string | number | readonly string[] | undefined, record: any) => (
                <div className="times">
                    <Input
                        type="number"
                        defaultValue={record.maxTime?.time || ''}
                        onChange={(e) => handleInputChange(e.target.value, record.id, 'maxTime')}
                        placeholder="输入最高时长"
                    />
                    <Select
                        defaultValue={record.maxTime?.unit || 'minute'}
                        onChange={(value) => handleSelectChange(value, record.id, 'maxTime')}
                        style={{ marginLeft: '10px' }}
                    >
                        <Option value="minute">分</Option>
                        <Option value="second">秒</Option>
                    </Select>
                </div>
            ),
        },
        {
            title: '部分内跨顺序答题',
            dataIndex: 'isSkips',
            key: 'isSkip',
            render: (checked: any, record: any) => (
                <Switch
                    checked={record.isSkip}
                    onChange={(checked) => handleChange(checked, record.id, 'isSkip')}
                    checkedChildren="允许"
                    unCheckedChildren="禁止"
                />
            ),
        },
    ];

    const handleChange = (value: any, key: any, field: any) => {
        const newData = [...dataSource?.part];
        const index = newData.findIndex((item) => item.id === key);
        if (index > -1) {
            newData[index][field] = value;
            setDataSource((prevDataSource: any) => ({
                ...prevDataSource,
                part: newData,
            }));
        }
    };

    // 计算总时间（分钟），支持 minTime 和 maxTime
    const calculateTotalTimeInMinutes = (field: string, dataSource: any) => {
        let totalTimeInMinutes = 0;
        dataSource.forEach((item: any) => {
            let timeField = null;
            if (field === 'minTime') {
                timeField = item?.minTime;
            } else if (field === 'maxTime') {
                timeField = item?.maxTime;
            }

            if (timeField) {
                if (timeField?.unit === 'minute') {
                    totalTimeInMinutes += timeField?.time * 1; // 单位是分钟
                } else if (timeField?.unit === 'second') {
                    totalTimeInMinutes += timeField?.time / 60; // 单位是秒，转换成分钟
                }
            }
        });
        return parseFloat(totalTimeInMinutes.toFixed(2));
    };

    // 处理 Input 改变的函数
    const handleInputChange = (value: any, key: any, field: any) => {
        const newData = [...dataSource?.part];
        const index = newData.findIndex((item) => item.id === key);
        if (index > -1) {
            const currentReadTime = newData[index][field];
            newData[index][field] = {
                time: value, // 更新为新的输入时间
                unit: currentReadTime?.unit ? currentReadTime.unit : 'minute',
            };

            // 计算总时长
            const formattedTotalTime = calculateTotalTimeInMinutes(field, newData);

            // 更新表单字段值
            if (field === 'minTime') {
                form.setFieldsValue({ Shortest: formattedTotalTime });
            } else if (field === 'maxTime') {
                form.setFieldsValue({ answeringTime: formattedTotalTime });
            }
            // 更新当前项的时间字段
            const readTime = {
                time: value,
                unit: currentReadTime?.unit ? currentReadTime.unit : 'minute',
            };
            newData[index][field] = readTime;
            setDataSource((prevDataSource: any) => ({
                ...prevDataSource,
                part: newData,
            }));
        }
    };

    // 处理 Select 改变的函数
    const handleSelectChange = (value: any, key: any, field: any) => {
        const newData = [...dataSource?.part];
        const index = newData.findIndex((item) => item.id === key);
        if (index > -1) {
            const currentReadTime = newData[index][field];
            console.log(value, key, field, currentReadTime, 'unit');

            newData[index][field] = {
                time: currentReadTime?.time,
                unit: value,
            };
            // 计算总时长
            const formattedTotalTime = calculateTotalTimeInMinutes(field, newData);
            // 更新表单字段值
            if (field === 'minTime') {
                form.setFieldsValue({ Shortest: formattedTotalTime });
            } else if (field === 'maxTime') {
                form.setFieldsValue({ answeringTime: formattedTotalTime });
            }
            // 只更新 time
            const readTime = {
                time: currentReadTime?.time,  // 保持原来的 unit 不变
                unit: value,
            };

            newData[index][field] = readTime;
            setDataSource((prevDataSource: any) => ({
                ...prevDataSource,
                part: newData,
            }));
        }
    };


    const confirm = () => {

        form
            .validateFields()
            .then((values) => {
                // console.log("表单数据: ", values); // 打印表单中的数据
                const newDataSource = {
                    ...dataSource,
                    // isCrossPart: values.CrossSection,
                    answerMinute: values.answeringTime,
                    minSubmitMinute: values.Shortest,
                    submitTipMinute: values.submitTipMinutes * 1,
                };
                // 然后更新 state
                setDataSource(newDataSource);
                DeleteAPI(newDataSource)
                callback();
            })
            .catch((error) => {
                message.error("表单验证失败，请检查输入");
            });
    }

    const handleSwitchChange = (checked: boolean) => {

        setDataSource((prevDataSource: any) => ({
            ...prevDataSource,
            isCrossPart: checked,
        }));
    };

    const close = () => {
        onclose();
    }

    return (
        <Modal
            title={title}
            visible={visible}
            onCancel={close}
            width={1250}
            className='CustomModal'
            footer={[
                <Button
                    type='primary'
                    onClick={confirm}
                >
                    确定
                </Button>,
                <Button
                    onClick={close}
                >
                    取消
                </Button>,
            ]}
        >

            <Form
                className='searchbox'
                form={form}
                layout="inline"
                style={{ marginBottom: 16 }}
            >
                <Form.Item
                    label="试卷总答题时长"
                    name="answeringTime"
                    initialValue={dataSource?.answerMinute}
                >
                    <Input placeholder="请输入试卷总答题时长" addonAfter="分" />


                </Form.Item>

                {/* 最短交卷时长 */}
                <Form.Item
                    label="最短交卷时长"
                    name="Shortest"
                    initialValue={dataSource?.minSubmitMinute}
                >
                    <Input placeholder="请输入最短交卷时长" addonAfter="分" />

                </Form.Item>

                <Form.Item
                    className='custom_input'
                    label="交卷提示:测验结束前"
                    name="submitTipMinutes"
                    initialValue={dataSource?.submitTipMinute}
                >
                    <Input placeholder="请输入时长" addonAfter="分" />
                </Form.Item>

                <Form.Item
                    label="跨部分答题"
                    name="CrossSection"
                    valuePropName="checked"
                    initialValue={dataSource?.isCrossPart}
                >
                    <div>
                        {dataSource?.isCrossPart}
                    </div>
                    <Switch
                        checked={dataSource?.isCrossPart}
                        checkedChildren="允许"
                        unCheckedChildren="禁止"
                        onChange={handleSwitchChange}
                    />
                </Form.Item>
            </Form>

            <Table
                className='Tables'
                columns={columns}
                dataSource={dataSource?.part}
                pagination={false}
                // rowKey="key"
                rowKey="id"
            />

        </Modal >
    )
}

export default CustomModal