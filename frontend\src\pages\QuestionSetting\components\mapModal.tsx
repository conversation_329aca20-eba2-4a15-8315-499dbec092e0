import React, { FC, useState, useEffect } from 'react';
import { Modal, Tabs, message, Tree } from 'antd'
import { ProColumns, ProTable, ProTableProps } from '@ant-design/pro-components';
import { reqMapApi, getTree } from '@/api/questionset'
import dayjs from 'dayjs'
import type { TreeDataNode, TreeProps } from 'antd';

interface MapModalProps {
	modalVisible: boolean;
	onCloseModal: (val?: any) => void;
	emitSelectRow: (val: any) => void;
	mapInfo: any[]
}

const MapModal: FC<MapModalProps> = ({
	modalVisible,
	onCloseModal,
	emitSelectRow,
	mapInfo,
}) => {
	const [tabKey, setTabKey] = useState<string>('1');
	const [total, setTotal] = useState<number>(0);
	const [pagination, setPagination] = useState<any>({
		current: 1,
		pageSize: 10,
	})
	const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
	const [selectedRows, setSelectedRows] = useState<any[]>([]);

	// 下一步的弹窗
	const [chapterVisible, setChapterVisible] = useState<boolean>(false)
	const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
	const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
	const [chapterList, setChapterList] = useState<any>([])
	const [chapterInfo, setChapterInfo] = useState<any>([])
	const [nodesVos, setNodesVos] = useState<any>([])

	// 使用 useEffect 在 mapInfo 或 modalVisible 变化时回显数据
	useEffect(() => {
		if (modalVisible && mapInfo.length > 0) {
			const keys = mapInfo.map(item => item.mapId);
			setSelectedRowKeys([keys[keys.length - 1]]); // 选中最后一个元素
			setSelectedRows([mapInfo[keys.length - 1]]); // 选中最后一个元素
		}
		
	}, [ mapInfo, modalVisible, tabKey]);

	const columns: ProColumns<any>[] = [
		{
			title: '名称',
			fieldProps: {
				placeholder: `请输入地图名称`,
			},
			formItemProps: {
				label: null,
			},
			dataIndex: 'mapName',
			colSize: 1.5,
		},
		{
			title: '适用课程',
			dataIndex: 'courseName',
			hideInSearch: true,
		},
		{
			title: '学科专业',
			dataIndex: 'subjectName',
			hideInSearch: true,
		},
		{
			title: '创建人',
			dataIndex: 'teacherName',
			hideInSearch: true,
		},
		{
			title: "创建时间",
			dataIndex: 'createDate',
			key: 'createDate',
			render: (text: any) => dayjs(text).format('YYYY-MM-DD HH:mm:ss') || '-'
		},
	]

	const tableConfig: ProTableProps<any, any> = {
		pagination: {
			total: total,
			current: pagination.current,
			pageSize: pagination.pageSize,
			pageSizeOptions: ['50', '100', '500', '1000', '2000'],
			showSizeChanger: true,
			showQuickJumper: true,
			hideOnSinglePage: false,
			// showTotal: (total: number) => `共 ${total} 条`,
			defaultPageSize: pagination.pageSize,
			position: ['bottomCenter'],
			size: 'small',
		},
		options: false,
		rowKey: 'id',
		search: {
			span: 4,
			showHiddenNum: true,
			labelWidth: 'auto',
			className: 'in-pro-table-search'
		},
		className: 'in-pro-table',
		columns,
		params: {
			tabKey
		},
		async request(
			// 第一个参数 params 查询表单和 params 参数的结合
			// 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范
			// T & {} https://procomponents.ant.design/components/table#request
			params: any,
		) {
			try {
				const {
					current,
					pageSize,
					...rest
				} = params;
				const { data: { results, total } } = await reqMapApi({
					page: current,
					size: pageSize,
					field: params.tabKey == 1 ? 0 : 3,
					sort: true,
					isShow: params.tabKey == 2 ? 2 : null,
					name: params.mapName
				});
				setTotal(total)
				return {
					data: results,
					// success 请返回 true，
					// 不然 table 会停止解析数据，即使有数据
					success: true,
					// 不传会使用 data 的长度，如果是分页一定要传
					total,
				};
			} catch (error) {
				return {
					data: [],
					success: false,
				};
			}
		},
		scroll: {
			x: true,
		},
		onChange: (
			pagination: any,
		) => {
			setPagination({
				...pagination,
			});
		},
		tableAlertRender: false,
		rowSelection: {
			type: 'radio', // 单选模式
			columnWidth: 80,
			selectedRowKeys,
			onChange(keys, rows) {
				setSelectedRowKeys(keys);
				setSelectedRows(rows);
			},
		},
	};

	// 获取知识点
	const getChapterList = async () => {
		getTree({ mapId: selectedRows[0].id }).then((res: any) => {
			if (res?.status == 200) {
				const { nodesVos, relationVos } = res?.data
				setNodesVos(nodesVos)
				const newNodes = nodesVos.map((item: any) => {
					return {
						id: item.nodeId,
						name: item.entity,
						isroot: JSON.parse(item.valueMap).data.isroot,
						children: []
					}
				})
				const newRelationVos = relationVos.map((item: any) => {
					return {
						source: item.source,
						target: item.target,
						isnew: JSON.parse(item.data).isnew,
					}
				}).filter((item: any) => !item.isnew)

				// 创建节点映射表
				const nodeMap = new Map<string, any>();
				newNodes.forEach((node: any) => {
					nodeMap.set(node.id, node);
				});

				// 根据关系构建树形结构
				newRelationVos.forEach((relation: any) => {
					const parent = nodeMap.get(relation.source);
					const child = nodeMap.get(relation.target);
					if (parent && child) {
						parent.children.push(child);
					}
				});

				// 找到根节点
				let rootNodes: any[] = [];
				newNodes.forEach((node: any) => {
					if (node.isroot) {
						rootNodes.push(node);
					}
				});
				// 设置章节列表
				setChapterList(rootNodes);
			}
		})
	}
	const onCheck: TreeProps['onCheck'] = (checkedKeys: any, info) => {
		setCheckedKeys(checkedKeys);
		// const checkedNodes = info.checkedNodes
		// // 将checkedNodes数组更具parentId组成树形数组
		// const treeData = buildTree(checkedNodes)
		// const chapter = treeData.map((item: any) => {
		// 	return {
		// 		key: item.id,
		// 		name: item.name_,
		// 		children: item.children?.map((chi: any) => {
		// 			return {
		// 				key: chi.id,
		// 				name: chi.name_
		// 			}
		// 		})
		// 	}
		// })
		// setChapterInfo(chapter ?? [])
	};

	const buildTree = (data: any) => {
		const idMap: any = {};
		const tree: any = [];
		data.forEach((item: any) => {
			idMap[item.id] = { ...item, children: [] };
		});
		data.forEach((item: any) => {
			const node = idMap[item.id];
			if (item.parentId === null || item.parentId === undefined || item.parentId == '') {
				tree.push(node);
			} else {
				const parent = idMap[item.parentId];
				if (parent) {
					parent.children.push(node);
				}
			}
		});
		return tree;
	}

	const getSelectedRootNodes = (chapterList: any[], checkedKeys: React.Key[]) => {
		const traverse = (nodes: any[]) => {
			const result: any[] = [];
			for (const node of nodes) {
				if (checkedKeys.includes(node.id)) {
					result.push(node);
				}
				if (node.children && node.children.length > 0) {
					result.push(...traverse(node.children));
				}
			}
			return result;
		};

		return traverse(chapterList);
	};

	return (
		<>
			<Modal
				width={'80%'}
				wrapClassName='map-modal-wrapper'
				title="选择课程地图"
				open={modalVisible}
				onCancel={() => {
					onCloseModal()
					setSelectedRowKeys([])
					setSelectedRows([])
				}}
				onOk={() => {
					if (selectedRowKeys.length > 0) {
						// const row = selectedRows[0]
						// emitSelectMap({
						// 	mapId: row.id,
						// 	mapName: row.mapName
						// })
						// onCloseModal()
						// setSelectedRowKeys([])
						// setSelectedRows([])

						setChapterVisible(true)
						getChapterList()
					} else {
						message.error('请选择图谱!')
						return
					}
				}}
				okText="下一步"
			>
				<Tabs activeKey={tabKey} onChange={(key: string) => setTabKey(key)}>
					<Tabs.TabPane tab="我的地图" key="1">
						<ProTable {...tableConfig} />
					</Tabs.TabPane>
					<Tabs.TabPane tab="已发布地图" key="2">
						<ProTable {...tableConfig} />
					</Tabs.TabPane>
				</Tabs>
			</Modal>

			<Modal
				width={600}
				wrapClassName='chapter-modal-tree-wrapper'
				title="选择知识点"
				open={chapterVisible}
				onCancel={() => {
					setChapterVisible(false)
					setCheckedKeys([])
				}}
				onOk={() => {
					if (checkedKeys.length == 0) {
						message.error('请选择课程章节')
						return
					}
					// 获取选中的根节点
					const newNodes = getSelectedRootNodes(chapterList, checkedKeys)?.filter((item: any) => !item.children?.length)?.map((item: any) => {
						return {
							key: item.id,
							name: item.name,
						}
					})
					onCloseModal()
					setChapterVisible(false)
					const row = selectedRows[0]
					emitSelectRow([{
						mapId: row.id,
						mapName: row.mapName,
						knowledge: newNodes
					}])
				}}
				okText="确定"
				cancelText="上一步"
			>
				<p className='course-name'>{selectedRows[0]?.mapName}</p>
				<Tree
					checkable
					expandedKeys={expandedKeys}
					onExpand={(expandedKeysValue) => setExpandedKeys(expandedKeysValue)}
					checkedKeys={checkedKeys}
					onCheck={onCheck}
					fieldNames={{ title: 'name', key: 'id' }}
					treeData={chapterList}
				/>
			</Modal>
		</>
	);
};

export default MapModal;