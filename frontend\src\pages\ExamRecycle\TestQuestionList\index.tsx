import examManageApis from '@/api/exam';
import { IconFont } from '@/components';
import RenderHtml from '@/components/renderHtml';
import examType from '@/types/examType';
import { QuestionCircleFilled } from '@ant-design/icons';
import { Button, Checkbox, message, Modal, Select, Table } from 'antd';
import { TableRowSelection } from 'antd/lib/table/interface';
import moment from 'moment';
import { FC, useEffect, useState } from 'react';
import './index.less';

const TestQuestionList: FC<{ isSuper: boolean; isTeacher: boolean }> = ({
  isSuper,
  isTeacher,
}) => {
  // 选择的数据
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  //#region 列表数据与操作
  const [recycleList, setRecycleList] = useState<any>([]);
  //分页
  const [querypage, setQuerypage] = useState({
    page: 1,
    size: 10,
    total: 0,
  });
  //时间排序
  const [timeSort, setTimeSort] = useState<string | number>('1');
  // 获取列表
  const getRecycleList = (pageIndex: number, pageSize: number, sort = 0) => {
    examManageApis
      .queryRecyleList({
        page: pageIndex,
        size: pageSize,
        sortBy: 1,
        sort: sort,
      })
      .then((res: any) => {
        if (res?.status == 200) {
          const { data, totalCount } = res?.data;
          setRecycleList(data || []);
          setQuerypage({
            page: pageIndex,
            size: pageSize,
            total: totalCount || 0,
          });
          setSelectedRowKeys([]);
        }
      });
  };

  useEffect(() => {
    getRecycleList(1, 10);
  }, []);

  //刷新
  const handleRefresh = () => {
    setSelectedRowKeys([]);
    getRecycleList(1, 10, timeSort);
  };

  //还原
  const handleReduction = () => {
    if (selectedRowKeys.length === 0) {
      message.warn('请选择要还原的题目');
      return;
    }
    examManageApis.restoreRecyle(selectedRowKeys as string[]).then((res) => {
      if (res?.data) {
        message.success('还原成功');
        handleRefresh();
        //   onRefresh?.()
      } else {
        message.error('失败');
      }
    });
  };

  // 彻底删除
  const handleDelComplete = () => {
    if (selectedRowKeys.length === 0) {
      message.warn('请选择要删除的题目');
      return;
    }
    Modal.confirm({
      content: '试题删除后将无法恢复，您确认要彻底删除所选的题目吗？',
      title: '删除确认',
      icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
      onOk: () => {
        examManageApis
          .deleteTopics(selectedRowKeys, { deleteType: false })
          .then((res) => {
            if (res?.data) {
              message.success('删除完成');
              setSelectedRowKeys([]);
              handleRefresh();
            } else {
              message.error('删除失败');
            }
          });
      },
    });
  };

  // 清空
  const handleClearAll = () => {
    Modal.confirm({
      content: '确认清空整个回收站的题目吗？',
      title: '清空',
      icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
      onOk: () => {
        examManageApis.clearAllRecyle().then((res) => {
          if (res?.data) {
            message.success('清空完成');
            handleRefresh();
          } else {
            message.error('清空失败');
          }
        });
      },
    });
  };
  //#endregion
  //#region 表的操作
  // 选择数据
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };
  //
  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  // 自定义分页展示页面
  const showTotal = (total: any, range: any) => {
    return `共${total}条`;
  };
  //页码切换和
  const paginationChange = (current: any, size: any) => {
    // const { name, date, college } = form.getFieldsValue();
    getRecycleList(current, size, timeSort);
  };
  //#endregion
  // 表头
  const recycleColumns: any = [
    {
      title: '题目类型',
      //   width: '7%',
      dataIndex: 'questions_type',
      key: 'questions_type',
      ellipsis: true,
      render: (item: any, record: any) => (
        <div>{examType.optionType_[Number(item)]}</div>
      ),
    },
    {
      title: '题目',
      //   width: '15%',
      dataIndex: 'questions_content',
      key: 'questions_content',
      ellipsis: true,
      render: (value: any) => (
        <RenderHtml cname="auto-img" value={value}></RenderHtml>
      ),
    },
    {
      title: '答案',
      //   width: '5%',
      dataIndex: 'questions_answers',
      key: 'questions_answers',
      ellipsis: true,
      // render: (value: any, record: any) => <div>{value?.join(',')}</div>,
      render: (value: any) =>
        value?.map((item: any) => (
          // <div
          //   dangerouslySetInnerHTML={createMarkup(item)}
          //   className="spcialDom"
          // ></div>
          <RenderHtml cname="auto-img" value={item}></RenderHtml>
        )),
    },
    {
      title: '解析',
      //   width: '10%',
      dataIndex: 'questions_analysis',
      key: 'questions_analysis',
      ellipsis: true,
      render: (value: any) => (
        <RenderHtml cname="auto-img" value={value}></RenderHtml>
      ),
    },
    // {
    //   title: '难度',
    // //   width: '5%',
    //   dataIndex: 'questions_difficulty',
    //   key: 'questions_difficulty',
    //   ellipsis: true,
    // },
    // {
    //   title: '知识点',
    // //   width: '8%',
    //   dataIndex: 'knowledge_points',
    //   key: 'knowledge_points',
    //   ellipsis: true,
    //   render: (item: any, record: any) => {
    //     let arr: any = [];
    //     if (item?.length) {
    //       item.forEach((item_: any) => {
    //         arr.push(item_.entity);
    //       });
    //     }
    //     return arr.join(',');
    //   },
    // },
    // {
    //   title: '正确率',
    // //   width: '5%',
    //   dataIndex: 'accuracy',
    //   key: 'accuracy',
    //   ellipsis: true,
    // },
    // {
    //   title: '适用层次',
    // //   width: '10%',
    //   dataIndex: 'questions_level',
    //   key: 'questions_level',
    //   ellipsis: true,
    //   render: (item: any, record: any) =>
    //     item?.map((item_: any) => {
    //       return examType.levelType_[Number(item_)] || '' + ' ';
    //     }),
    // },
    // {
    //   title: '适用院系/部门',
    // //   width: '10%',
    //   dataIndex: 'questions_major',
    //   key: 'questions_major',
    //   ellipsis: true,
    //   render: (item: any, record: any) => codeToName(item),
    // },
    // {
    //   title: '适用课程',
    // //   width: '10%',
    //   dataIndex: 'questions_lesson',
    //   key: 'questions_lesson',
    //   ellipsis: true,
    // },
    // {
    //   title: '创建人',
    // //   width: '8%',
    //   dataIndex: 'add_username',
    //   key: 'add_username',
    //   ellipsis: true,
    // },
    // {
    //   title: '创建时间',
    // //   width: '12%',
    //   dataIndex: 'creator_time',
    //   key: 'creator_time',
    //   ellipsis: true,
    //   render: (item: any, record: any) => {
    //     // 格式化时间
    //     return dayjs(item).format('YYYY-MM-DD HH:mm:ss');
    //   },
    // },
    {
      title: '删除时间',
      dataIndex: 'reclamationTime',
      key: 'reclamationTime',
      align: 'center',
      render: (text: any) => moment(text).format('YYYY-MM-DD HH:mm:ss') || '-',
      //   render: (item: any, record: any) => {
      //     // 格式化时间
      //     return dayjs(item).format('YYYY-MM-DD HH:mm:ss');
      //   },
    },
    {
      title: '删除人',
      dataIndex: 'reclamationUserName',
      key: 'reclamationUserName',
      align: 'center',
    },
  ];
  return (
    <div className="recycle-exam-container">
      <div className="top-btn-container">
        <div className="btn-list">
          <div>
            <Checkbox
              indeterminate={(() => {
                if (selectedRowKeys.length) {
                  if (selectedRowKeys.length == recycleList.length) {
                    return false;
                  }
                  return true;
                }
                return false;
              })()}
              checked={
                selectedRowKeys.length === recycleList.length &&
                recycleList.length > 0
              }
              disabled={!recycleList.length}
              onChange={(e) => {
                if (e.target.checked) {
                  setSelectedRowKeys(recycleList.map((item: any) => item.id));
                } else {
                  setSelectedRowKeys([]);
                }
              }}
            >
              全部
            </Checkbox>
          </div>
          <div className="btn-item">
            <Button type="text" onClick={handleRefresh}>
              <IconFont type="iconshuaxin" />
              刷新
            </Button>
          </div>
          <div className="line-vertical" />
          <div className="btn-item">
            <Button
              disabled={!isSuper && !isTeacher}
              type="text"
              onClick={handleReduction}
            >
              <IconFont type="iconhuifushanchu" />
              还原
            </Button>
          </div>
          <div className="btn-item">
            <Button type="text" disabled={!isSuper} onClick={handleDelComplete}>
              <IconFont type="iconchedishanchu" />
              彻底删除
            </Button>
          </div>
          <div className="btn-item">
            <Button type="text" disabled={!isSuper} onClick={handleClearAll}>
              <IconFont type="iconqingkong" />
              清空
            </Button>
          </div>
        </div>
        <div className="time-select">
          <Select
            style={{ width: 120 }}
            defaultValue="0"
            onChange={(value: string) => {
              getRecycleList(1, 10, value);
              setTimeSort(value);
            }}
          >
            <Select.Option value="1">
              删除时间
              <IconFont type="iconchangjiantou-shang" />
            </Select.Option>
            <Select.Option value="0">
              删除时间
              <IconFont type="iconchangjiantou-xia" />
            </Select.Option>
          </Select>
        </div>
      </div>

      <Table
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        // style={{ marginTop: '20px' }}
        rowKey={(e: any) => e.id}
        dataSource={recycleList}
        columns={recycleColumns}
        pagination={{
          pageSize: querypage.size,
          current: querypage.page,
          total: querypage.total,
          showQuickJumper: true,
          showTotal: showTotal,
          onChange: paginationChange,
        }}
      />
    </div>
  );
};

export default TestQuestionList;
