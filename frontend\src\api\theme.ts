import HTTP from '@/api/index';
import { AxiosRequestConfig } from 'axios';

const http = (url: string, config?: AxiosRequestConfig) =>
  HTTP(url, config)
    .then(res => res.data)
    .catch(err => err);

namespace themeService {
  export const fetchSysSetting = () =>
    http(`/unifiedplatform/v1/setting/no/authorization`);

  /**
   * 获取全局参数配置
   */
  export const fetchParameterConfig = () => {
    return http(
      `/unifiedplatform/v1/app/app/module/parameter/list?moduleCode=MyTeaching`,
    );
  };

  /**
   * 获取全局参数配置
   */
  export const fetchParameterConfigs = () => {
    return http(`/unifiedplatform/v1/app/app/module/parameter/list`, {
      method: 'post',
      // data: ['mooc_kcgl', 'spoc_kcgl', 'MyTeaching'],
      data: ['mooc_kcgl', 'spoc_kcgl', 'wdkc', 'microcourse_kcgl', 'classreview_kcgl', 'LearningPortal'],
    });
  };
  export const fetchUserPermissionsV2 = () =>
    http(`/unifiedplatform/v1/user/rolemodule`);

  export const fetchHeaderList = () => 
    http(`/unifiedplatform/v1/navigationsettings/user/navigation?type=2`)

  export function reqMessageList(data: any) {
    return HTTP(`/unifiedplatform/v1/message/notify/self/list`,{
      method: 'GET',
      params: data
    });
  }
  
  export function reqUnreadMessageCount() {
    return HTTP(`/unifiedplatform/v1/message/notify/unread/count`,{
      method: 'GET',
    });
  }
  
  export function updateMessageRead(data: any) {
    return HTTP(`/unifiedplatform/v1/message/read`,{
      method: 'GET',
      params: data
    });
  }
}

export default themeService;
