.ContractManagements {
  #components-table-demo-drag-sorting tr.drop-over-downward td {
    border-bottom: 2px dashed #1890ff;
  }

  #components-table-demo-drag-sorting tr.drop-over-upward td {
    border-top: 2px dashed #1890ff;
  }

  .ant-table-expanded-row td {
    padding: 8px !important;
    /* 调整内边距 */
    width: auto !important;
    /* 取消固定宽度 */
  }

  /* ContractManagement.css */
  .drag-handle-column {
    width: 30px !important;
    text-align: center;
  }

  .drag-handle-column td {
    padding: 8px !important;
  }

  /* 设置拖拽列的全局样式 */
  .ant-table td:first-child {
    width: 30px !important;
    /* 拖拽列的宽度 */
    text-align: center;
    /* 垂直居中对齐 */
  }


  margin: 40px 20px;

  .button_zuh {
    margin-bottom: 16px;
  }

  .ant-modal-content {
    .icon_box {
      width: 100%;
      height: 100%;
      display: flex;
      -ms-flex-pack: start;
      justify-content: flex-start;
      -ms-flex-align: center;
      align-items: center;
      -ms-flex-wrap: wrap;
      flex-wrap: wrap;
    }
  }

}
