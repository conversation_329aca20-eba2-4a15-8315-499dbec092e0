/*
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-06-22 16:24:11
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-06-29 11:51:07
 */
tinymce.PluginManager.add('kityformula-editor', function (editor, url) {
    var pluginName = '插入数学公式';
    var baseURL = tinymce.baseURL + '/plugins/kityformula-editor/kityFormula.html';
    editor.on('dblclick', function () {
        var sel = editor.selection.getContent();
        var path = /\<img(.*?)src="data:image\/png;base64,[A-Za-z0-9+/=]*"(.*?)data-latex="(.*?)" \/>/g;
        var path2 = /data-latex="(.*?)"/g;

        if (sel.search(path) == 0) {
            sel.replace(path2, function ($0, $1) {
                var param = encodeURIComponent($1);
                openDialog(param);
                return $0;
            });
        };
    });

    var openDialog = function (param) {
        return editor.windowManager.openUrl({
            title: pluginName,
            size: 'large',
            width: 785,
            height: 475,
            url: param ? baseURL + "?c=" + param : baseURL,
            buttons: [
                {
                    type: 'cancel',
                    text: 'Close'
                },
                {
                    type: 'custom',
                    text: 'Save',
                    name: 'save',
                    primary: true
                },
            ],
            onAction: function (api, details) {
                switch (details.name) {
                    case 'save':
                        api.sendMessage("save");
                        break;
                    default:
                        break;
                };
            }
        });
    };

    editor.ui.registry.getAll().icons.picturetitle || editor.ui.registry.addIcon('kityformula-editor', '<svg t="1714121527177" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2514" width="20" height="20"><path d="M131.614118 659.215059l-66.861177 0.963765L63.608471 567.296l124.687058-1.807059 32.165647 83.606588L353.942588 70.595765h650.962824v92.943059h-584.282353L249.494588 904.975059a12.047059 12.047059 0 0 1-23.009882 1.626353L131.674353 659.215059z m328.041411 215.04l147.094589-270.697412-142.757647-296.839529h388.818823l53.970824 139.324235-78.848 36.020706-31.924706-82.642824H605.123765l99.44847 206.607059-95.171764 175.284706h187.572705l31.503059-74.872471 77.763765 38.671059-54.392471 129.144471h-392.131764z" fill="#2c2c2c" p-id="2515"></path></svg>');

    editor.ui.registry.addButton('kityformula-editor', {
        icon: 'kityformula-editor',
        tooltip: pluginName,
        onAction: function () {
            openDialog();
        }
    });
    editor.ui.registry.addMenuItem('kityformula-editor', {
        icon: 'kityformula-editor',
        text: pluginName,
        onAction: function () {
            openDialog();
        }
    });
    return {
        getMetadata: function () {
            return {
                name: pluginName,
                url: "http://hgcserver.gitee.io",
            };
        }
    };
});