import React, { useEffect, useRef, useState } from "react";
import { Col, Row, Button, Radio, Checkbox, message, Empty } from "antd";
import { LeftCircleFilled } from "@ant-design/icons";
import './index.less';
import { useLocation, history } from "umi";
import Contract from "@/api/Contract";
import RenderHtml from "@/components/renderHtml";
import examType from "@/types/examType";
import { convertToChinaNum } from '@/utils';
const Answer = () => {

    const location = useLocation();

    const [Record, setRecord] = useState('')
    const [titleName, settitleName] = useState('')
    const [Data, setData] = useState([]);
    const [SelectTopic, setSelectTopic] = useState({});
    const [SelectTopicname, setSelectTopicname] = useState('');
    const [SelectId, setSelectId] = useState();
    const [detailIndex, setdetailIndex] = useState(1);
    const [property, setproperty] = useState('预览');
    const [classifyId, setclassifyId] = useState('预览');
    const [processedDetaiList, setprocessedDetails] = useState([]);
    // 初始化题目引用
    const questionRefs = useRef<any>({});
    const [sectionId, setsectionId] = useState('')
    const [groupQuestionId, setgroupQuestionId] = useState('')
    let globalIndex = 1;  // 初始化全局计数器
    let globalIndexs = 1;  // 初始化全局计数器
    let globalIndexflase = 0;  // 初始化全局计数器

    useEffect(() => {
        const state = location.state
        if (state) {
            setRecord(state.listVoByAnswerId)
            settitleName(state.titleName)
            setSelectId(state.SelectId)
            setdetailIndex(state.detailIndex)
            setproperty(state.property)
            setclassifyId(state.classifyId)

        }

        if (state.property == '预览') {
            console.log('预览');
            getVoByIdAPI(state.listVoByAnswerId)
        } else {
            listVoByAnswerIdAPI(state.listVoByAnswerId, state.SelectId)
        }


    }, [location]);

    const getVoByIdAPI = async (id: any) => {
        const res = await Contract.getVoById(id);
        if (res.status === 200) {
            const userData = res.data;
            setSelectTopic(userData);
            setData(res.data)
        }
    };

    const listVoByAnswerIdAPI = async (id: any, selectId: any) => { //详情
        const res = await Contract.listVoByAnswerId(id);
        if (res.status === 200) {
            setData(res.data)
            const data = res.data;
            const processedDetails: any[] | ((prevState: never[]) => never[]) = [];
            const allDetails = data.reduce((acc: string | any[], item: { details: any; }) => acc.concat(item.details || []), []);

            allDetails.forEach((detail: { question: { questions_type: number; groupQuestions: any[]; }; }) => {
                processedDetails.push(detail);
                if (detail?.question?.questions_type === 5 && Array.isArray(detail?.question?.groupQuestions)) {

                    detail.question.groupQuestions.forEach((groupQuestion) => {
                        processedDetails.push(groupQuestion);
                    });
                }
            });
            setprocessedDetails(processedDetails)
            const matchedItem = res.data.find((item: { details: any[]; }) =>
                item.details.some(detail => detail.question.id == selectId)
            );
            if (matchedItem) {
                const matchedName = matchedItem.name;
                const matchedDetail = matchedItem.details.find((detail: { question: { id: any; }; }) => detail.question.id == selectId);
                setSelectTopicname(matchedName);
                setSelectTopic(matchedDetail);
            }
        }
    };

    const collectAPI = async (id: any) => { //收藏
        const res = await Contract.collect(id);
        message.success(res?.message);
    };

    const handleCollect = () => {
        // classifyId
        if (SelectTopic.question === 5) {
            collectAPI({ id: SelectTopic.question.id, parentQuestionsContent: SelectTopic.questions_content, classifyId: classifyId })
        } else {
            collectAPI({ id: SelectTopic.question.id, parentQuestionsContent: '', classifyId: classifyId })
        }
    }

    const butncard = (item: any, detailIndex: any, items: any) => {
        setSelectTopic(item)
        setSelectTopicname(items.name)
        setdetailIndex(detailIndex)
    }

    const [groupID, getgroupID] = useState()

    const questionRefsA = useRef<{ [key: string]: HTMLDivElement | null }>({});
    // yul_scflow
    const questionRefsB = useRef<{ [key: string]: HTMLDivElement | null }>({});
    // 滚动到指定问题

    const scrollToElementB = (id: string) => {
        const targetElement = questionRefsB.current[id]; // 获取目标元素
        const innerContainer = document.querySelector('.yul_scflow'); // 子滚动容器
        const outerContainer = document.querySelector('.scflow'); // 父滚动容器

        if (targetElement && innerContainer && outerContainer) {
            // 获取目标元素相对于视口的矩形
            const targetRect = targetElement.getBoundingClientRect();

            // 获取父容器和子容器的视口矩形
            const innerRect = innerContainer.getBoundingClientRect();
            const outerRect = outerContainer.getBoundingClientRect();

            // 计算目标居中位置所需的偏移量
            const innerOffsetTop =
                targetRect.top - innerRect.top + innerContainer.scrollTop - (innerRect.height / 2 - targetRect.height / 2);
            const outerOffsetTop =
                targetRect.top - outerRect.top + outerContainer.scrollTop - (outerRect.height / 2 - targetRect.height / 2);

            // 滚动两个容器到指定位置
            innerContainer.scrollTo({
                top: innerOffsetTop,
                behavior: 'smooth',
            });
            outerContainer.scrollTo({
                top: outerOffsetTop,
                behavior: 'smooth',
            });
        } else {
            console.error('目标元素或容器未找到');
        }
    };



    const scrollToElement = (id: string) => {
        setTimeout(() => {
            const targetElement = questionRefsA.current[id]; // 获取目标元素
            const container = document.querySelector('.new_scflow'); // 子滚动容器
            const parentContainer = document.querySelector('.topic.collect'); // 父容器

            if (targetElement && container && parentContainer) {
                const targetRect = targetElement.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();

                // 计算子容器滚动的偏移量
                const offsetTop = container.scrollTop + (targetRect.top - containerRect.top);

                // 父容器滚动到子容器的目标位置
                parentContainer.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth',
                });
            } else {
                console.error('目标元素或容器未找到。');
            }
        }, 0); // 延迟执行，确保布局已完成
    };

    const butnQuestions = (detail: any, item: any, detailIndex: any, items: any) => {
        // setdetailIndex(calculateIndexUntilMatch(Data, detail))
        setdetailIndex(detailIndex)
        getgroupID(item.id)
        setSelectTopic(detail)
        setSelectTopicname(items.name)
        scrollToElement(item.id)
    }

    const calculateIndexUntilMatch = (Data: string | any[], SelectTopic: { question?: any; }) => {
        let index = 0;
        let matched = false;


        for (let i = 0; i < Data.length; i++) {
            const dataItem = Data[i];


            for (let j = 0; j < dataItem.details.length; j++) {
                const detail = dataItem.details[j];
                const question = detail.question;


                if (question.id === SelectTopic.question.id) {
                    matched = true;
                    break; // 找到匹配项，跳出循环
                }

                if (question.questions_type === 5) {
                    index += question.groupQuestions.length;
                } else {
                    index += 1;
                }
            }

            if (matched) {
                break;
            }
        }

        return index;
    }


    const butnscroll = (value: any) => {
        const questionId = value.id;  // 获取题目的 id
        setsectionId(value.id)
        const questionElement = questionRefs.current[questionId];
        if (questionElement) {
            const container = document.querySelector('.scflow');
            const rect = questionElement.getBoundingClientRect();
            if (container) {
                const containerScrollTop = container.scrollTop;
                const offset = 300;
                const scrollTop = containerScrollTop + rect.top - offset;

                container.scrollTo({
                    top: scrollTop,
                    behavior: 'smooth',
                });
            }
        }
    };

    const butnscrollsc = (value: any, index: any, groupQuestion: any,) => {
        setgroupQuestionId('')
        const questionId = value.id;  // 获取题目的 id
        setsectionId(value.id)
        const questionElement = questionRefs.current[questionId];
        if (questionElement) {
            const container = document.querySelector('.scflow');
            const rect = questionElement.getBoundingClientRect();
            if (container) {
                const containerScrollTop = container.scrollTop;
                const offset = 300;
                const scrollTop = containerScrollTop + rect.top - offset;

                container.scrollTo({
                    top: scrollTop,
                    behavior: 'smooth',
                });
            }
        }
    };

    const butnQuestionsB = (value: any, index: any, groupQuestion: any,) => {
        scrollToElementB(groupQuestion.id)
        setsectionId('')
        setgroupQuestionId(groupQuestion?.id)
    }

    return (

        <div className="answer">
            {/* 顶部导航 */}
            <header className='header'>
                <a className='comeback' onClick={() => window.history.back()} >
                    <LeftCircleFilled style={{
                        color: '#CBCBCB', width: '20px',
                        height: '20px', fontSize: '32px',
                        marginRight: '10px',
                    }} />
                    <div className='fs'> {property === '预览' ? titleName : '返回上一级'}</div>
                </a>
                <div className='statisticsContainer'>
                    {property === '预览' ? null : titleName}
                </div>
                <div style={{ marginRight: '20px' }}>
                    {/* <Button onClick={handleCollect} type="primary">
                        收藏试题
                    </Button> */}
                </div>
            </header>


            {property === '预览' ? (
                <div className="Content" style={{ padding: '28px 20px' }} >
                    <Row gutter={16}>
                        <Col span={6} >
                            <div className='card' style={{ height: 'calc(100vh - 100px)', overflow: 'scroll' }}  >
                                <div className='bt_card'>
                                    {Array.isArray(Data?.part) && Data.part.length > 0 ? (


                                        Data.part.map((item: { name: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; questionList: any[]; }, index: React.Key | null | undefined) => (
                                            <div key={index}>
                                                <div className='select_title'>
                                                    <div className='title_sz'>{item?.name}</div>
                                                </div>
                                                <div className="bt_flex">
                                                    {item?.questionList?.flatMap((items_one: any, index: any) => {

                                                        let currentIndex = globalIndex;
                                                        if (items_one.questions_type !== 5) {
                                                            currentIndex = globalIndex++;
                                                        }

                                                        const buttons = [];
                                                        if (items_one.questions_type !== 5) {
                                                            buttons.push(
                                                                <Button
                                                                    onClick={() => butnscrollsc(items_one, index, '')}
                                                                    key={currentIndex}
                                                                    className={`bts ${!items_one?.answerQuestion
                                                                        ? ""
                                                                        : items_one?.answerQuestion?.isRead
                                                                            ? "border-blue"
                                                                            : "border-red"
                                                                        }`}
                                                                >
                                                                    <span className={detailIndex === currentIndex + 1 ? "blues" : ""}>
                                                                        {currentIndex}
                                                                    </span>
                                                                </Button>
                                                            );
                                                        }


                                                        if (items_one?.questions_type === 5) {
                                                            items_one?.groupQuestions?.forEach((groupQuestion: any) => {
                                                                const groupIndex = globalIndex++;
                                                                buttons.push(
                                                                    <Button
                                                                        onClick={() => butnQuestionsB(items_one, index, groupQuestion)}
                                                                        key={groupIndex}
                                                                        className="bts border-green"
                                                                    >
                                                                        <span className={detailIndex === groupIndex + 1 ? "blues" : ""}>
                                                                            {groupIndex}
                                                                        </span>
                                                                    </Button>
                                                                );
                                                            });
                                                        }

                                                        return buttons;
                                                    })}
                                                </div>
                                            </div>
                                        ))

                                    ) : (
                                        <Empty description="没有试题" />
                                    )}
                                </div>
                            </div>
                        </Col>
                        <Col span={18} >
                            <div className="topic Preview scflow " style={{}}  >
                                <div className="topic_title" >
                                    {SelectTopicname}
                                </div>


                                <div className=""  >
                                    {
                                        SelectTopic?.part?.map((item: any, index: number) => {
                                            return (
                                                <div className="topic_Content"
                                                    key={index}
                                                >
                                                    <div className="contents">
                                                        <div
                                                            className="type"
                                                            style={{ width: '100%' }}
                                                        >
                                                            {item.name}
                                                        </div>
                                                        <div>
                                                            {Array.isArray(item.questionList) && item.questionList.length > 0 ? (
                                                                <div className="">
                                                                    {item.questionList.map((item: any, qIndex: number) => {
                                                                        const globalIndextwo = globalIndexs++;

                                                                        const questionId = item.id;

                                                                        if (item.questions_type !== 5) {
                                                                            const globalIndexsie = globalIndexflase++;
                                                                        }
                                                                        return (
                                                                            <div>
                                                                                <div className={sectionId === item.id ? "other-class" : ""}

                                                                                    key={qIndex}
                                                                                    ref={(el) => questionRefs.current[questionId] = el}
                                                                                >
                                                                                    <div
                                                                                        className={sectionId === item.id ? "other-class" : "contents"}
                                                                                    >
                                                                                        <div style={{ marginRight: '10px' }} >
                                                                                            {
                                                                                                item.questions_type == '5' && globalIndexflase === 0
                                                                                                    ? globalIndexflase + 1 + '. '
                                                                                                    : globalIndexflase + '. '
                                                                                            }


                                                                                            (
                                                                                            <span>{`${examType.optionType_
                                                                                            [item.questions_type]}题`}</span>
                                                                                            )
                                                                                        </div>

                                                                                        <RenderHtml
                                                                                            cname="auto-img"
                                                                                            value={item.questions_content}
                                                                                        ></RenderHtml>
                                                                                    </div>

                                                                                </div>
                                                                                <div
                                                                                    className='answers'
                                                                                    ref={el => {
                                                                                        if (el) {
                                                                                            console.log('answers宽度:', el.offsetWidth);
                                                                                            const radioGroup = el.querySelector('.ant-radio-group') as HTMLElement;
                                                                                            const checkboxGroup = el.querySelector('.ant-checkbox-group') as HTMLElement;
                                                                                            if (el.offsetWidth < 400) {
                                                                                                if (radioGroup) {
                                                                                                    radioGroup.style.display = 'flex';
                                                                                                    radioGroup.style.flexDirection = 'row';
                                                                                                    radioGroup.style.gap = '40px';
                                                                                                }
                                                                                                if (checkboxGroup) {
                                                                                                    checkboxGroup.style.display = 'flex';
                                                                                                    checkboxGroup.style.flexDirection = 'row';
                                                                                                    checkboxGroup.style.gap = '40px';
                                                                                                }
                                                                                            } else {
                                                                                                if (radioGroup) {
                                                                                                    radioGroup.style.display = 'block';
                                                                                                    radioGroup.style.flexDirection = '';
                                                                                                    radioGroup.style.gap = '';
                                                                                                }
                                                                                                if (checkboxGroup) {
                                                                                                    checkboxGroup.style.display = 'block';
                                                                                                    checkboxGroup.style.flexDirection = '';
                                                                                                    checkboxGroup.style.gap = '';
                                                                                                }
                                                                                            }
                                                                                        }
                                                                                    }}
                                                                                >
                                                                                    {item.questions_type === 0 ? ( //单选
                                                                                        <div className="radio_group" >
                                                                                            <Radio.Group value={item.questions_answers[0]}>
                                                                                                {item.questions_options.map(
                                                                                                    (item_0: any, index_0: number) => {
                                                                                                        return (
                                                                                                            <div className="answer_item" key={index_0}>
                                                                                                                <Radio
                                                                                                                    value={String.fromCharCode(
                                                                                                                        64 + Number(index_0 + 1),
                                                                                                                    )}
                                                                                                                >
                                                                                                                    {String.fromCharCode(64 + Number(index_0 + 1))}
                                                                                                                </Radio>
                                                                                                                <RenderHtml
                                                                                                                    cname="radio_content spcialDom"
                                                                                                                    value={item_0.content}
                                                                                                                ></RenderHtml>
                                                                                                            </div>
                                                                                                        );
                                                                                                    },
                                                                                                )}
                                                                                            </Radio.Group>
                                                                                        </div>
                                                                                    ) : item.questions_type === 1 ? ( //多选
                                                                                        <Checkbox.Group value={item.questions_answers}>
                                                                                            {item.questions_options.map(
                                                                                                (item_1: any, index_1: number) => {
                                                                                                    return (
                                                                                                        <div className="answer_item" key={index_1}>
                                                                                                            <Checkbox
                                                                                                                value={String.fromCharCode(
                                                                                                                    64 + Number(index_1 + 1),
                                                                                                                )}
                                                                                                            >
                                                                                                                {String.fromCharCode(64 + Number(index_1 + 1))}
                                                                                                            </Checkbox>
                                                                                                            <RenderHtml
                                                                                                                cname="spcialDom"
                                                                                                                value={item_1.content}
                                                                                                            ></RenderHtml>
                                                                                                        </div>
                                                                                                    );
                                                                                                },
                                                                                            )}
                                                                                        </Checkbox.Group>
                                                                                    ) : item.questions_type === 2 ? ( // 填空题
                                                                                        item.questions_options.map((item_2: any, index_2: number) => {
                                                                                            const blankAnswer =
                                                                                                item_2?.answerRange === 2
                                                                                                    ? `${item_2.answerMin}~${item_2.answerMax}`
                                                                                                    : item_2.content;

                                                                                            return (
                                                                                                <div className="answer_item blanks" key={index_2}>
                                                                                                    <span>{`第${index_2 + 1}空：`}</span>
                                                                                                    <RenderHtml
                                                                                                        cname="spcialDom"
                                                                                                        value={blankAnswer}
                                                                                                    ></RenderHtml>
                                                                                                </div>
                                                                                            );
                                                                                        })
                                                                                    ) : item.questions_type === 3 ? ( // 主观题
                                                                                        <div className="answer_item" key={index}>
                                                                                            <span>解析：</span>
                                                                                            <RenderHtml
                                                                                                cname="spcialDom"
                                                                                                value={item.questions_analysis}
                                                                                            ></RenderHtml>
                                                                                        </div>
                                                                                    ) : item.questions_type === 5 ? ( // 题组
                                                                                        <div className="yul_scflow" style={{ height: '100%' }}>

                                                                                            {item?.groupQuestions?.map((queItem: any, index_4: any) => {
                                                                                                const globalIndexsie = globalIndexflase++; // 每次递增
                                                                                                return (
                                                                                                    <div className="content_row" key={queItem.id} ref={(el) => (questionRefsB.current[queItem.id] = el)} >
                                                                                                        <div className="content">
                                                                                                            <div className="type" style={{ display: 'flex', alignItems: ' baseline' }}>

                                                                                                                <div className={groupQuestionId === queItem.id ? "other-class" : "contents"} >
                                                                                                                    <span style={{ fontSize: '15px' }} >{globalIndexsie + 1}. </span>
                                                                                                                    {/* {index_4 + 1}. */}
                                                                                                                    (
                                                                                                                    <span>{`${examType.optionType_[queItem.questions_type]
                                                                                                                        }题`}</span>
                                                                                                                    )
                                                                                                                    <RenderHtml
                                                                                                                        cname="auto-img"
                                                                                                                        value={queItem.questions_content}
                                                                                                                    // 
                                                                                                                    ></RenderHtml>
                                                                                                                </div>
                                                                                                            </div>

                                                                                                        </div>
                                                                                                        {queItem.fileList?.length > 0 && (
                                                                                                            <div className="fileList_">
                                                                                                                <span>题目附件：</span>
                                                                                                                <div>
                                                                                                                    {queItem.fileList.map((item: any, index: number) => {
                                                                                                                        return (
                                                                                                                            <a
                                                                                                                                href={item.attachmentSource}
                                                                                                                                key={index}
                                                                                                                                target={item.attachmentSource}
                                                                                                                                title={item.attachmentName || ''}
                                                                                                                            >
                                                                                                                                {item.attachmentName || ''}
                                                                                                                            </a>
                                                                                                                        );
                                                                                                                    })}
                                                                                                                </div>
                                                                                                            </div>
                                                                                                        )}
                                                                                                        {queItem.hasAttachment?.length > 0 && (
                                                                                                            <div className="fileList_upload">
                                                                                                                <span>上传附件：</span>
                                                                                                                <div>
                                                                                                                    {queItem.hasAttachment.map((item: any, index: number) => {
                                                                                                                        return (
                                                                                                                            <div key={index}>
                                                                                                                                <span>{item.name}</span>
                                                                                                                                <span>{item.required && `(必传*)`}</span>
                                                                                                                            </div>
                                                                                                                        );
                                                                                                                    })}
                                                                                                                </div>
                                                                                                            </div>
                                                                                                        )}
                                                                                                        <div className="answers">
                                                                                                            {queItem.questions_type === 0 ? ( //单选
                                                                                                                <Radio.Group value={queItem?.questions_answers?.[0] || null}>
                                                                                                                    {queItem.questions_options.map(
                                                                                                                        (item_0: any, index_0: number) => {
                                                                                                                            return (
                                                                                                                                <div className="answer_item" key={index_0}>
                                                                                                                                    <Radio
                                                                                                                                        value={String.fromCharCode(
                                                                                                                                            64 + Number(index_0 + 1),
                                                                                                                                        )}
                                                                                                                                    >
                                                                                                                                        {String.fromCharCode(64 + Number(index_0 + 1))}
                                                                                                                                    </Radio>
                                                                                                                                    <RenderHtml
                                                                                                                                        cname="radio_content auto-img"
                                                                                                                                        value={item_0.content}

                                                                                                                                    ></RenderHtml>
                                                                                                                                </div>
                                                                                                                            );
                                                                                                                        },
                                                                                                                    )}
                                                                                                                </Radio.Group>
                                                                                                            ) : queItem.questions_type === 1 ? ( //多选
                                                                                                                <Checkbox.Group value={queItem.questions_answers}>
                                                                                                                    {queItem.questions_options.map(
                                                                                                                        (item_1: any, index_1: number) => {
                                                                                                                            return (
                                                                                                                                <div className="answer_item" key={index_1}>
                                                                                                                                    <Checkbox
                                                                                                                                        value={String.fromCharCode(
                                                                                                                                            64 + Number(index_1 + 1),
                                                                                                                                        )}
                                                                                                                                    >
                                                                                                                                        {String.fromCharCode(64 + Number(index_1 + 1))}
                                                                                                                                    </Checkbox>
                                                                                                                                    <RenderHtml
                                                                                                                                        cname="auto-img"
                                                                                                                                        value={item_1.content}

                                                                                                                                    ></RenderHtml>
                                                                                                                                </div>
                                                                                                                            );
                                                                                                                        },
                                                                                                                    )}
                                                                                                                </Checkbox.Group>
                                                                                                            ) : queItem.questions_type === 2 ? ( // 填空题
                                                                                                                queItem.questions_options.map(
                                                                                                                    (item_2: any, index_2: number) => {
                                                                                                                        const dataRange = `${item_2?.answerMin}~${item_2?.answerMax}`;
                                                                                                                        return (
                                                                                                                            <div className="answer_item blanks" key={index_2}>
                                                                                                                                <span>{`第${index_2 + 1}空：`}</span>
                                                                                                                                {/* <span>{`${item_2.content || dataRange}`}</span> */}
                                                                                                                                <RenderHtml
                                                                                                                                    cname="auto-img"

                                                                                                                                    value={item_2.content || dataRange}
                                                                                                                                ></RenderHtml>
                                                                                                                            </div>
                                                                                                                        );
                                                                                                                    },
                                                                                                                )
                                                                                                            ) : queItem.questions_type === 3 ? ( // 主观题
                                                                                                                <div className="answer_item">
                                                                                                                    <span>解析：</span>
                                                                                                                    {/* <RenderHtml
                                                                                                                          cname="auto-img"
                                                                                                                          value={queItem.questions_analysis}
                                                                                                                          
                                                                                                                      ></RenderHtml> */}
                                                                                                                </div>
                                                                                                            ) : (
                                                                                                                // 判断题
                                                                                                                <Radio.Group value={queItem?.questions_answers?.[0] || null}>
                                                                                                                    {queItem.questions_options.map(
                                                                                                                        (item_4: any, index_4: number) => {
                                                                                                                            return (
                                                                                                                                <div className="answer_item" key={index_4}>
                                                                                                                                    <Radio
                                                                                                                                        value={String.fromCharCode(
                                                                                                                                            64 + Number(index_4 + 1),
                                                                                                                                        )}
                                                                                                                                    >
                                                                                                                                        {String.fromCharCode(64 + Number(index_4 + 1))}
                                                                                                                                    </Radio>
                                                                                                                                    <div className="radio_content">
                                                                                                                                        {item_4.content}
                                                                                                                                    </div>
                                                                                                                                </div>
                                                                                                                            );
                                                                                                                        },
                                                                                                                    )}
                                                                                                                </Radio.Group>
                                                                                                            )}
                                                                                                        </div>
                                                                                                    </div>
                                                                                                );
                                                                                            })}


                                                                                        </div>
                                                                                    ) : (
                                                                                        // 判断题
                                                                                        <Radio.Group value={item.questions_answers[0]}>
                                                                                            {item.questions_options.map(
                                                                                                (item_4: any, index_4: number) => {
                                                                                                    return (
                                                                                                        <div className="answer_item" key={index_4}>
                                                                                                            <Radio
                                                                                                                value={String.fromCharCode(
                                                                                                                    64 + Number(index_4 + 1),
                                                                                                                )}
                                                                                                            >
                                                                                                                {String.fromCharCode(64 + Number(index_4 + 1))}
                                                                                                            </Radio>
                                                                                                            <div className="radio_content">
                                                                                                                {item_4.content}
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    );
                                                                                                },
                                                                                            )}
                                                                                        </Radio.Group>
                                                                                    )}
                                                                                </div>

                                                                            </div>
                                                                        );
                                                                    })}
                                                                </div>
                                                            ) : (
                                                                <Empty />
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })
                                    }
                                </div>
                            </div>
                        </Col>
                    </Row>
                </div>
            ) : (
                <div className="Content" style={{ padding: '24px 20px' }} >
                    <Row gutter={16}>
                        <Col span={18} >
                            <div className="topic collect" style={{ height: 'calc(100vh - 100px)', overflow: 'scroll' }} >
                                <div className="topic_title" >
                                    {SelectTopicname}
                                </div>
                                <div className="topic_Content" >
                                    <div className="content">
                                        <div
                                            className="type"
                                        >
                                            {convertToChinaNum(detailIndex)},
                                            {
                                                `${examType.optionType_
                                                [SelectTopic.question?.questions_type]}题
                                        `}
                                        </div>
                                        <RenderHtml
                                            cname="auto-img"
                                            value={SelectTopic.question?.questions_content}
                                        ></RenderHtml>
                                    </div>
                                    <div className="answers">
                                        {SelectTopic.question?.questions_type === 0 ? ( //单选
                                            <Radio.Group value={SelectTopic.question?.questions_answers[0]}>
                                                {SelectTopic.question?.questions_options.map(
                                                    (item_0: any, index_0: number) => {
                                                        return (
                                                            <div className="answer_item" key={index_0}>
                                                                <Radio
                                                                    value={String.fromCharCode(64 + Number(index_0 + 1))}
                                                                >
                                                                    {String.fromCharCode(64 + Number(index_0 + 1))}
                                                                </Radio>
                                                                <RenderHtml
                                                                    cname="radio_content auto-img"
                                                                    value={item_0.content}

                                                                ></RenderHtml>
                                                            </div>
                                                        );
                                                    },
                                                )}
                                            </Radio.Group>
                                        ) : SelectTopic.question?.questions_type === 1 ? ( //多选
                                            <Checkbox.Group value={SelectTopic.question?.questions_answers}>
                                                {SelectTopic.question?.questions_options.map(
                                                    (item_1: any, index_1: number) => {
                                                        return (
                                                            <div className="answer_item" key={index_1}>
                                                                <Checkbox
                                                                    value={String.fromCharCode(64 + Number(index_1 + 1))}
                                                                >
                                                                    {String.fromCharCode(64 + Number(index_1 + 1))}
                                                                </Checkbox>
                                                                <RenderHtml
                                                                    cname="auto-img"
                                                                    value={item_1.content}

                                                                ></RenderHtml>
                                                            </div>
                                                        );
                                                    },
                                                )}
                                            </Checkbox.Group>
                                        ) : SelectTopic.question?.questions_type === 2 ? ( // 填空题
                                            SelectTopic.question?.questions_options.map((item_2: any, index_2: number) => {
                                                const dataRange = `${item_2?.answerMin}~${item_2?.answerMax}`;
                                                return (
                                                    <div className="answer_item blanks" key={index_2}>
                                                        <span>{`第${index_2 + 1}空：`}</span>
                                                        <RenderHtml cname="auto-img" value={item_2.content || dataRange}></RenderHtml>
                                                    </div>
                                                );
                                            })
                                        ) : SelectTopic.question?.questions_type === 3 ? ( // 主观题
                                            <div className="answer_item">
                                                <span>解析：</span>
                                                <RenderHtml
                                                    cname="auto-img"
                                                    value={SelectTopic.question?.questions_analysis}
                                                // 
                                                ></RenderHtml>
                                            </div>
                                        ) : SelectTopic.question?.questions_type === 5 ? ( // 主观题
                                            <div className="new_scflow"  >
                                                {SelectTopic.question?.groupQuestions?.map((queItem: any, index_0: any) => {
                                                    return (

                                                        <div className="content_row" key={queItem.id}
                                                            ref={(el) => {
                                                                if (el) questionRefsA.current[queItem.id] = el;
                                                                else delete questionRefsA.current[queItem.id]; // 移除无效引用
                                                            }}
                                                        >
                                                            <div className="content">
                                                                <div onClick={() => console.log(queItem, '11111')}
                                                                    className={groupID === queItem.id ? "other-class" : ""}
                                                                    style={{ display: 'flex', alignItems: ' baseline' }}
                                                                >
                                                                    <span>{calculateIndexUntilMatch(Data, SelectTopic) + index_0 + 1} ,</span>
                                                                    (
                                                                    <span>  {`${examType.optionType_[queItem.questions_type]
                                                                        }题`}</span>
                                                                    )
                                                                </div>

                                                            </div>
                                                            {queItem.fileList?.length > 0 && (
                                                                <div className="fileList_">
                                                                    <span>题目附件：</span>
                                                                    <div>
                                                                        {queItem.fileList.map((item: any, index: number) => {
                                                                            return (
                                                                                <a
                                                                                    href={item.attachmentSource}
                                                                                    key={index}
                                                                                    target={item.attachmentSource}
                                                                                    title={item.attachmentName || ''}
                                                                                >
                                                                                    {item.attachmentName || ''}
                                                                                </a>
                                                                            );
                                                                        })}
                                                                    </div>
                                                                </div>
                                                            )}
                                                            {queItem.hasAttachment?.length > 0 && (
                                                                <div className="fileList_upload">
                                                                    <span>上传附件：</span>
                                                                    <div>
                                                                        {queItem.hasAttachment.map((item: any, index: number) => {
                                                                            return (
                                                                                <div key={index}>
                                                                                    <span>{item.name}</span>
                                                                                    <span>{item.required && `(必传*)`}</span>
                                                                                </div>
                                                                            );
                                                                        })}
                                                                    </div>
                                                                </div>
                                                            )}
                                                            <div className="answers" >
                                                                {queItem.questions_type === 0 ? ( //单选
                                                                    <Radio.Group value={queItem?.questions_answers?.[0] || null}>
                                                                        {queItem.questions_options.map(
                                                                            (item_0: any, index_0: number) => {
                                                                                return (
                                                                                    <div className="answer_item" key={index_0}>
                                                                                        <Radio
                                                                                            value={String.fromCharCode(
                                                                                                64 + Number(index_0 + 1),
                                                                                            )}
                                                                                        >
                                                                                            {String.fromCharCode(64 + Number(index_0 + 1))}
                                                                                        </Radio>
                                                                                    </div>
                                                                                );
                                                                            },
                                                                        )}
                                                                    </Radio.Group>
                                                                ) : queItem.questions_type === 1 ? ( //多选
                                                                    <Checkbox.Group value={queItem.questions_answers}>
                                                                        {queItem.questions_options.map(
                                                                            (item_1: any, index_1: number) => {
                                                                                return (
                                                                                    <div className="answer_item" key={index_1}>
                                                                                        <Checkbox
                                                                                            value={String.fromCharCode(
                                                                                                64 + Number(index_1 + 1),
                                                                                            )}
                                                                                        >
                                                                                            {String.fromCharCode(64 + Number(index_1 + 1))}
                                                                                        </Checkbox>
                                                                                        <RenderHtml
                                                                                            cname="auto-img"
                                                                                            value={item_1.content}

                                                                                        ></RenderHtml>
                                                                                    </div>
                                                                                );
                                                                            },
                                                                        )}
                                                                    </Checkbox.Group>
                                                                ) : queItem.questions_type === 2 ? ( // 填空题
                                                                    queItem.questions_options.map(
                                                                        (item_2: any, index_2: number) => {
                                                                            const dataRange = `${item_2?.answerMin}~${item_2?.answerMax}`;
                                                                            return (
                                                                                <div className="answer_item blanks" key={index_2}>
                                                                                    <span>{`第${index_2 + 1}空：`}</span>
                                                                                    {/* <span>{`${item_2.content || dataRange}`}</span> */}
                                                                                    <RenderHtml
                                                                                        cname="auto-img"

                                                                                        value={item_2.content || dataRange}
                                                                                    ></RenderHtml>
                                                                                </div>
                                                                            );
                                                                        },
                                                                    )
                                                                ) : queItem.questions_type === 3 ? ( // 主观题
                                                                    <div className="answer_item">
                                                                        <span>解析：</span>
                                                                        <RenderHtml
                                                                            cname="auto-img"
                                                                            value={queItem.questions_analysis}
                                                                        ></RenderHtml>
                                                                    </div>
                                                                ) : (
                                                                    // 判断题
                                                                    <Radio.Group value={queItem?.questions_answers?.[0] || null}>
                                                                        {queItem.questions_options.map(
                                                                            (item_4: any, index_4: number) => {
                                                                                return (
                                                                                    <div className="answer_item" key={index_4}>
                                                                                        <Radio
                                                                                            value={String.fromCharCode(
                                                                                                64 + Number(index_4 + 1),
                                                                                            )}
                                                                                        >
                                                                                            {String.fromCharCode(64 + Number(index_4 + 1))}
                                                                                        </Radio>
                                                                                        <div className="radio_content">
                                                                                            {item_4.content}
                                                                                        </div>
                                                                                    </div>
                                                                                );
                                                                            },
                                                                        )}
                                                                    </Radio.Group>
                                                                )}
                                                            </div>

                                                            <div className="footer" style={{ marginBottom: '20px' }}  >
                                                                <div style={{ display: 'flex' }} >
                                                                    <span style={{ display: 'flex' }} className="aws">正确答案：
                                                                        <span className="blus" >
                                                                            <RenderHtml
                                                                                cname="auto-img"
                                                                                value={queItem?.questions_answers ? queItem?.questions_answers : '无'}
                                                                            ></RenderHtml>
                                                                        </span>
                                                                    </span>
                                                                    {SelectTopic?.answerQuestion?.find((item: { questionId: any; }) => item.questionId == queItem?.id)?.answer ? (
                                                                        <span className="aws" style={{ marginLeft: '40px', display: 'flex', flexWrap: 'wrap' }}>
                                                                            你的答案： <RenderHtml cname="auto-img"
                                                                                value={SelectTopic?.answerQuestion.find((item: { questionId: any; }) => item.questionId == queItem?.id)?.answer}></RenderHtml>
                                                                        </span>

                                                                    ) : (
                                                                        <span className="aws" style={{ marginLeft: '40px' }}>
                                                                            你的答案： 暂无答案
                                                                        </span>
                                                                    )}
                                                                </div>
                                                                <div className="analysis">
                                                                    解析:
                                                                    <RenderHtml
                                                                        value={queItem?.questions_analysis}
                                                                    ></RenderHtml>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>

                                        ) : (
                                            // 判断题
                                            <Radio.Group value={SelectTopic.question?.questions_answers[0]}>
                                                {SelectTopic.question?.questions_options.map(
                                                    (item_4: any, index_4: number) => {
                                                        return (
                                                            <div className="answer_item" key={index_4}>
                                                                <Radio
                                                                    value={String.fromCharCode(64 + Number(index_4 + 1))}
                                                                >
                                                                    {String.fromCharCode(64 + Number(index_4 + 1))}
                                                                </Radio>
                                                                <div className="radio_content">{item_4.content}</div>
                                                            </div>
                                                        );
                                                    },
                                                )}
                                            </Radio.Group>
                                        )}
                                    </div>
                                    {SelectTopic.question?.questions_type !== 5 && (
                                        <div className="footer">
                                            <div style={{ display: 'flex' }} >
                                                <span style={{ display: 'flex', flexWrap: 'wrap', minWidth: '77px' }} className="aws">正确答案：<span className="blus" >
                                                    <RenderHtml
                                                        cname="auto-img"
                                                        value={
                                                            SelectTopic.question?.questions_answers && SelectTopic.question?.questions_answers.length > 0 && SelectTopic.question.questions_answers[0] !== null
                                                                ? SelectTopic.question.questions_answers
                                                                : '无'
                                                        }
                                                    ></RenderHtml>
                                                </span></span>
                                                {SelectTopic?.answerQuestion?.[0]?.answer ? (
                                                    <span className="aws" style={{ marginLeft: '40px', display: 'flex', flexWrap: 'wrap' }}  >
                                                        你的答案：<RenderHtml cname="auto-img"
                                                            value={SelectTopic.answerQuestion[0].answer}>
                                                        </RenderHtml> </span>
                                                ) : (
                                                    <span className="aws" style={{ marginLeft: '40px' }}  >你的答案： 暂无答案</span>
                                                )}
                                            </div>
                                            <div className="analysis">
                                                解析:
                                                <RenderHtml
                                                    value={SelectTopic.question?.questions_analysis}
                                                ></RenderHtml>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </Col>
                        <Col span={6} >
                            <div className='card' style={{ height: 'calc(100vh - 100px)' }}  >
                                <div className="bt_card">
                                    {(() => {
                                        let globalIndex = 0;
                                        return Data?.map((item, index) => (
                                            <div key={index}>
                                                <div className="select_title">
                                                    <div className="title_sz">{item?.name}</div>
                                                </div>
                                                <div className="bt_flex">
                                                    {item?.details.flatMap((items_one: any) => {
                                                        let currentIndex = globalIndex; // 定义 currentIndex，初始值为 globalIndex
                                                        if (items_one.question.questions_type !== 5) {
                                                            currentIndex = globalIndex++; // 如果不是类型 5，则自增 globalIndex
                                                        }

                                                        const buttons = [];
                                                        if (items_one.question.questions_type !== 5) { // 只有类型不为 5 时生成按钮
                                                            buttons.push(
                                                                <Button
                                                                    onClick={() => butncard(items_one, currentIndex + 1, item)} // 使用 currentIndex
                                                                    key={currentIndex}
                                                                    className={`bts ${!items_one?.answerQuestion
                                                                        ? ""
                                                                        : items_one?.answerQuestion?.isRead
                                                                            ? "border-blue"
                                                                            : "border-red"
                                                                        }`}
                                                                >
                                                                    <span className={detailIndex === currentIndex + 1 ? "blues" : ""}>
                                                                        {currentIndex + 1}
                                                                    </span>
                                                                </Button>
                                                            );
                                                        }

                                                        if (items_one?.question?.questions_type == 5) {
                                                            items_one?.question?.groupQuestions?.forEach((groupQuestion: any) => {
                                                                const groupIndex = globalIndex++; // 固定 groupQuestions 的初始索引值
                                                                buttons.push(
                                                                    <Button
                                                                        onClick={() => butnQuestions(items_one, groupQuestion, groupIndex + 1, item)} // 使用 groupIndex
                                                                        key={groupIndex}
                                                                        className="bts border-green"
                                                                    >
                                                                        <span className={detailIndex === groupIndex + 1 ? "blues" : ""}>
                                                                            {groupIndex + 1}
                                                                        </span>
                                                                    </Button>
                                                                );
                                                            });
                                                        }

                                                        return buttons;
                                                    })}
                                                </div>
                                            </div>
                                        ));
                                    })()}
                                </div>
                            </div>
                        </Col>
                    </Row>
                </div>
            )
            }
        </div >
    );
};

export default Answer;
