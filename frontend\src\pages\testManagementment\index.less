.testManagementment {

  .clexs {
    display: flex;
    align-items: center;
  }


  .disabled {
    position: relative;

    :not(:last-child) {
      &::after {
        content: '';
        display: inline-block;
        width: 1px;
        height: 16px;
        background: #d8d8d8;
        right: 0;
        top: 8px;
        position: absolute;
      }
    }
  }

  .ant-select {
    width: 150px !important;
  }

  .ant-input {
    width: 200px !important;
  }


  .iconfs {
    font-size: 16px;
    cursor: pointer
  }

  .Imagefs {
    width: 16px;
    height: 16px;
    background-color: red;
    margin-bottom: 10px;
  }

  .Menufs {
    height: 100%;
    overflow-y: auto;
    // -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* 隐藏滚动条，但仍允许滚动 */
  .ant-menu {
    -ms-overflow-style: none;
    /* IE 10+ */
    scrollbar-width: none;
    /* Firefox */
  }

  .Menufs::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari 和 Opera */
  }



}
