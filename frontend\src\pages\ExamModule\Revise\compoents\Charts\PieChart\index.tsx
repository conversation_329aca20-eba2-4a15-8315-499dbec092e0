import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface Props {
    title: string;
    data: number[];
    colors: string[];
}

const RingChart = (props: Props) => {
    const { title, data, colors } = props;
    const chartRef = useRef<HTMLDivElement>(null); // 创建 chartRef 引用

    useEffect(() => {
        // 确保 chartRef 不为空
        if (chartRef.current) {
            // 初始化 echarts 图表
            const chart = echarts.init(chartRef.current);

            // 获取容器宽高，动态计算半径
            const containerWidth = chartRef.current.offsetWidth;
            const containerHeight = chartRef.current.offsetHeight;
            const radius = Math.min(containerWidth, containerHeight) / 2 * 0.8; // 半径为容器较小边长的一部分

            const tickCount = 60; // 刻度线数量
            const tickLength = radius * 0.1; // 刻度线长度（半径的10%）
            const tickSpacing = radius * 0.04; // 刻度线与饼图之间的距离（半径的5%）
            const tickColor = colors.length > 0 ? colors[0] : '#d8d8d8'; // 使用传入的第一个颜色值

            // 动态生成刻度线
            const tickLines = Array.from({ length: tickCount }).map((_, i) => {
                const angle = (i / tickCount) * 360;
                const x1 = Math.cos((angle * Math.PI) / 180) * (radius + tickSpacing);
                const y1 = Math.sin((angle * Math.PI) / 180) * (radius + tickSpacing);
                const x2 = Math.cos((angle * Math.PI) / 180) * (radius + tickSpacing + tickLength);
                const y2 = Math.sin((angle * Math.PI) / 180) * (radius + tickSpacing + tickLength);
                return {
                    type: 'line',
                    shape: { x1, y1, x2, y2 },
                    style: {
                        stroke: tickColor, // 刻度线颜色
                        lineWidth: 1,
                    },
                };
            });

            // 配置项
            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: function (params: { name: any; value: any; }) {
                        // 自定义提示框内容，显示 name 和 value
                        return `${params.value}%`;
                    },
                },
                legend: {
                    top: '0%',
                    left: 'center',
                },
                graphic: [
                    {
                        type: 'group',
                        left: 'center',
                        top: 'center',
                        children: [
                            ...tickLines,
                            {
                                type: 'text',
                                left: 'center',
                                top: 'center',
                                style: {
                                    text: title, // 要显示的文字
                                    textAlign: 'center',
                                    fill: '#000', // 文字颜色
                                    fontSize: 17,
                                    lineHeight: 22,
                                },
                            },
                        ],
                    },
                ],
                series: [
                    {
                        type: 'pie',
                        radius: [`${(radius * 0.7).toFixed(0)}px`, `${(radius).toFixed(0)}px`], // 动态内外环半径
                        center: ['50%', '50%'],
                        data:
                            data.length > 0
                                ? data.map((value, index) => ({
                                    value,
                                    itemStyle: {
                                        color: colors[index % colors.length], // 设置颜色交替
                                    },
                                }))
                                : [],
                        label: {
                            show: false,
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: 40,
                                    fontWeight: 'bold',
                                },
                            },
                        },
                        labelLine: {
                            show: false,
                        },
                    },
                ],
            };

            chart.setOption(option);

            // 监听窗口大小变化，动态调整图表
            const resizeHandler = () => chart.resize();
            window.addEventListener('resize', resizeHandler);

            return () => {
                window.removeEventListener('resize', resizeHandler);
                chart.dispose();
            };
        }
    }, [data, title, colors]); // 每次 data 或 title 变化时重新渲染图表

    return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
};

export default RingChart;
