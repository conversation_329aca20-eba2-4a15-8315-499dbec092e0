namespace loginTypes {
  export interface ILoginReq {
    loginName: string;
    password: string;
  }

  export interface IPermission {
    code: string;
    moduleFeatures: {
      [propName: string]: string[];
    };
  }

  /**
   *模块的 --- 全局参数
   *
   * @export
   * @interface IGlobalParam
   */
  export interface IGlobalParam {
    code: string
    id: string
    isSystem: boolean
    moduleID: string
    name: string
    parameterType: number
    value: string // "true" | "false"
  }

  export interface ISetting {
    title: string;
    theme: string;
    logoUrl: string;
    themeColor: string;
  }
}

export default loginTypes;
