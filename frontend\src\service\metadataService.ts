import http from '@/http/http';
import { head } from '@umijs/deps/compiled/got';

interface IHiveResponseList<T = object | string> {
  page: number;
  size: number;
  total: number;
  results: Array<T>;
}

namespace MetadataService {
  /**
   * 获取元数据配置类型
   * @param page
   * @param size
   * @param code
   */
  export const fetchTypeList = ({
    page,
    size,
    code,
  }: MetadataTypes.TypeQuery) =>
    http<MetadataTypes.Type[]>(`/v3.0/resource-define/type`, {
      method: 'GET',
      params: {
        page,
        size,
        code,
      },
    });
  /**
   * 获取对应类型的元数据字段
   * @param EntityType
   * @param Type
   * @param ResourceType
   */
  export const fetchConfigFields = ({
    EntityType,
    Type,
    ResourceType,
  }: MetadataTypes.ConfigFieldQuery) =>
    http<MetadataTypes.Field[]>('/metadata/config/fields', {
      method: 'GET',
      params: {
        EntityType,
        Type,
        ResourceType,
      },
    });
  /**
   * 更新元数据字段
   * @param params
   */
  export const updateConfigFields = (
    params: MetadataTypes.UpdateConfigFieldQuery,
  ) =>
    http('/metadata/config/fields', {
      method: 'POST',
      body: JSON.stringify(params),
    });

  /**
   * 获取对应类型的上传元数据字段
   * @param EntityType
   * @param Type
   * @param ResourceType
   */
  export const fetchUploadConfigFields = ({
    EntityType,
    Type,
    ResourceType,
  }: MetadataTypes.ConfigFieldQuery) =>
    http<MetadataTypes.Field[]>('/metadata/config/fields/upload', {
      method: 'GET',
      params: {
        EntityType,
        Type,
        ResourceType,
      },
    });
  /**
   * 更新上传元数据字段
   * @param params
   */
  export const updateUploadConfigFields = (
    params: MetadataTypes.UpdateConfigFieldQuery,
  ) =>
    http('/metadata/config/fields/upload', {
      method: 'POST',
      body: JSON.stringify(params),
    });

  /**
   * new api
   */

  /**
   * 获取metadata types
   */
  export const fetchMetadataTypes = () =>
    http<MetadataTypes.typeResData>(`/metadata/config/type`, {
      method: 'GET',
    });

  /**
   * 获取元数据字段list
   * @param params
   */
  export const fetchMetadataFields = (params: {
    Type: string;
    ResourceType: string;
    FieldType?: string;
  }) =>
    http<MetadataTypes.Field[]>(`/metadata/config/get/fields`, {
      method: 'GET',
      params,
    });
  /**
   * 更新元数据配置只读
   * @param params
   */
  export const updateMetadataReadOnly = (params: {
    id: number;
    isReadOnly: number;
  }) =>
    http(`/metadata/config/update/read-only`, {
      method: 'POST',
      params,
    });
  /**
   * 更新元数据配置必填
   * @param params
   */
  export const updateMetadataRequired = (params: {
    id: number;
    isRequired: number;
  }) =>
    http(`/metadata/config/update/required`, {
      method: 'POST',
      params,
    });
  /**
   * 更新元数据配置是否展示
   * @param params
   */
  export const updateMetadataDisplay = (params: {
    id: number;
    isShow: number;
  }) =>
    http(`/metadata/config/update/display`, {
      method: 'POST',
      params,
    });
  /**
   * 更新元数据配置是否多选
   * @param params
   */
  export const updateMetadataMultiSelect = (params: {
    id: number;
    isChoice: number;
  }) =>
    http(`/metadata/config/update/multiple/choice`, {
      method: 'POST',
      params,
    });
  /**
   * 创建资源类型
   * @param params
   */
  export const addHiveMetadataType = (params: MetadataTypes.HiveMetadataType) =>
    http(`/hive/metadata/resourse/type/insert`, {
      method: 'POST',
      body: JSON.stringify(params),
    });
  /**
   * 更新资源类型
   * @param params
   */
  export const updateHiveMetadataType = (
    params: MetadataTypes.HiveMetadataType,
  ) =>
    http(`/hive/metadata/resourse/type/update`, {
      method: 'PUT',
      body: JSON.stringify(params),
    });
  /**
   * 查询hive元数据type list
   * @param params
   */
  export const fetchHiveMetadataTypeList = (params: {
    code?: string;
    name?: string;
    page: number;
    size: number;
  }) =>
    http<API.PageList<MetadataTypes.HiveMetadataType[]>>(
      `/hive/metadata/resourse/type/list`,
      {
        method: 'GET',
        params,
      },
    );
  /**
   * 查询hive元数据 field list
   * @param params
   */
  export const fetchHiveMetadataFieldList = (params: { code: string }) =>
    http<MetadataTypes.HiveEntityData>(`/hive/metadata/resourse/type/detail`, {
      method: 'GET',
      params,
    });
  /**
   * 添加hive元数据字段
   * @param params
   */
  export const addHiveMetadataField = (params: MetadataTypes.HiveEntity) =>
    http(`/hive/metadata/base/metadata/field/insert`, {
      method: 'POST',
      body: JSON.stringify(params),
    });
  /**
   * 更新hive元数据字段
   * @param params
   */
  export const updateHiveMetadataField = (params: MetadataTypes.HiveEntity) =>
    http(`/hive/metadata/base/metadata/field/update`, {
      method: 'PUT',
      body: JSON.stringify(params),
    });
  /**
   * 删除hive元数据字段
   * @param params
   */
  export const deleteHiveMetadataField = (params: {
    id: string[];
    type_id: string;
  }) =>
    http(`/hive/metadata/base/metadata/field/delete`, {
      method: 'DELETE',
      body: JSON.stringify(params),
    });

  /**
   * 添加hive元数据 index
   * @param params
   */
  export const addHiveMetadataFieldIndex = (params: MetadataTypes.HiveEntity) =>
    http(`/hive/metadata/base/metadata/add/index`, {
      method: 'POST',
      body: JSON.stringify(params),
    });
  /**
   * 删除hive元数据 index
   * @param params
   */
  export const removeHiveMetadataFieldIndex = (
    params: MetadataTypes.HiveEntity,
  ) =>
    http(`/hive/metadata/base/metadata/remove/index`, {
      method: 'POST',
      body: JSON.stringify(params),
    });

  /**
   * 添加hive元数据 index
   * @param params
   */
  export const addHiveMetadataSearchIndex = (
    params: MetadataTypes.HiveEntity,
  ) =>
    http(`/hive/metadata/base/metadata/add/search/index`, {
      method: 'POST',
      body: JSON.stringify(params),
    });
  /**
   * 删除hive元数据 index
   * @param params
   */
  export const removeHiveMetadataSearchIndex = (
    params: MetadataTypes.HiveEntity,
  ) =>
    http(`/hive/metadata/base/metadata/remove/search/index`, {
      method: 'POST',
      body: JSON.stringify(params),
    });
  /**
   * 添加业务元数据字段
   * @param params
   */
  export const addMetadataField = (params: MetadataTypes.Field) =>
    http(`/metadata/config/insert`, {
      method: 'POST',
      body: JSON.stringify(params),
    });
  /**
   * 修改元数据
   * @param params
   */
  export const updateMetadataField = (params: MetadataTypes.Field) =>
    http(`/metadata/config/update`, {
      method: 'POST',
      body: JSON.stringify(params),
    });
  /**
   * 删除元数据字段
   * @param params
   */
  export const deleteMetadataField = (params: { id: number }) =>
    http(`/metadata/config/delete`, {
      method: 'POST',
      params,
    });
  /**
   * 同步上传元数据
   * @param params
   */
  export const syncMetadataUploadField = (params: {
    id: number;
    isUpload: number;
  }) =>
    http(`/metadata/config/update/upload`, {
      method: 'POST',
      params,
    });
}
export default MetadataService;
