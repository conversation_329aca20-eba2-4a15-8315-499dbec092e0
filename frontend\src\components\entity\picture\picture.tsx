import React, { FC, useRef, useEffect, useState } from 'react';

const Picture: FC<Entity.IBaseEntity> = ({ src, onError, onListener }) => {
  const imgNode = useRef<HTMLImageElement>(null);
  const imgParentNode = useRef<HTMLDivElement>(null);
  const [style, setStyle] = useState({});
  const onLoad = () => {
    if (imgNode && imgNode.current && imgParentNode && imgParentNode.current) {
      if (
      imgNode.current.clientWidth / imgNode.current.clientHeight >
      imgParentNode.current.clientWidth / imgParentNode.current.clientHeight)
      {
        setStyle({
          width: '100%'
        });
      } else {
        setStyle({
          height: '100%'
        });
      }
    }
  };
  // useEffect(() => {
  //   window.addEventListener('resize', onLoad);
  //   return () => {
  //     window.removeEventListener('resize', onLoad);
  //   };
  // }, []);
  return (
    <div className="entity-img" ref={imgParentNode}>
      <img
      src={src}
      // style={style}
      // onLoad={onLoad}
      onError={onError}
      ref={imgNode}
      alt=""
      onLoad={() => {
        onListener && onListener('ended');
      }} />
      
    </div>);

};

export default Picture;