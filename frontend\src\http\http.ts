import { request, history, formatMessage } from 'umi';
import { RequestOptionsInit, ResponseError } from 'umi-request/types';
import { IResponse } from '@/types/requsetTypes';
import { message } from 'antd';
interface ICustomOption {
  isSleep?: boolean;
  ifLoading?: boolean;
  ifHideError?: boolean;
}

type Http = <T = any>(
  url: string,
  option?: RequestOptionsInit & ICustomOption,
) => // sleep?:boolean,
Promise<IResponse<T> | undefined>;

const errorHandler = (error: ResponseError<IResponse<any>>) => {
  if (
    error.data &&
    !error.data.success &&
    error.data.error &&
    error.data.error.title &&
    !(error.request?.options as any)?.ifHideError
  ) {
    message.error(error.data.error.title);
  }
  if (error.response && error.response.status !== 200) {
    if (error.response.status === 401) {
      window.parent.postMessage(JSON.stringify({ action: 'login' }), window.location.origin);
      window.location.replace(
        `/unifiedlogin/v1/loginmanage/login/direction?redirect_url=${encodeURIComponent(
          window.location.href,
        )}`,
      );
    } else {
      // message.error(
      //   formatMessage(
      //     {
      //       id: 'http-error',
      //       defaultMessage: `请求错误，错误码${error.response.status}`,
      //     },
      //     {
      //       code: error.response.status,
      //     },
      //   ),
      // );
    }
  }
};
let configSafe:any =null;
const http: Http = async (url, option) => {
  let flag = false;
  const orignalMethod:any = option?.method
  // const orignalUrl:any = url;
  // console.log('原来的',orignalMethod,orignalUrl)
  // configSafe = await getconfig(); //这种方式浏览器多线程会导致重复访问多次情况 最好放在初始化里仅调用一次
  configSafe = JSON.parse((window as any).sessionStorage.getItem('configSafe'));
  // console.log('rman',configSafe)
  // 安全模式：将所有非安全方法转换为post请求，并在路由后面添加/safeproxy，此规则为前后端约定
  if (
    configSafe?.SAFE_MODE &&
    option?.method &&
    // !configSafe._safeMethodList.includes(option.method.toLowerCase())
    configSafe._jsonMethod.includes(orignalMethod.toLowerCase())
  ) {
    // url = url.split('/').slice(0,2).join('/')+`/safeproxy`;
    flag = true
    // console.log('进来了',url)
    option.method = configSafe._safeMethod;
    option.headers = {
      'Content-Type': 'application/json',
      // 'realurl': orignalUrl, 
      'realmethod': orignalMethod, 
      ...option.headers,
    };
  }
  // console.log('新的',url,option)
  return request(url, { ...option, errorHandler,flag:flag });
};

export default http;
