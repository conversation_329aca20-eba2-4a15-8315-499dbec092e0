.answer {

  background: #F7F9FA;

  .auto-img {
    overflow: hidden;

    img {
      max-width: 100%;
      height: auto;
      vertical-align: bottom;
      transform: scale(1);
      transform-origin: center;
    }
  }

  .header {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #E3E3E3;
    justify-content: space-between;
    align-items: center;

    .comeback {
      padding-left: 34px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .fs {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #4A4F64;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }

    .statisticsContainer {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 20px;
      color: #282C3C;
      line-height: 30px;
      margin-right: 100px;
    }
  }

  .scflow {
    overflow-y: scroll;
    height: calc(100vh - 100px);
  }

  .new_scflow {
    // height: calc(100vh - 591px);
    // height: 232px;
    overflow-y: auto;
    /* 仅允许子容器滚动 */
    position: relative;

    /* 隐藏滚动条 */
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
  }

  .new_scflow::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Edge */
  }

  .yul_scflow {
    overflow-y: auto;
    position: relative;

    /* 隐藏滚动条 */
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* Internet Explorer 10+ */
  }

  .new_scflow::-webkit-scrollbar {
    display: none;
  }


  .other-class {
    display: flex;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #549CFF;
  }

  .contents {
    // display: flex;
  }

  .Content {


    .collect {
      padding: 20px 174px;
      height: calc(100vh - 100px);
      // overflow: scroll;
      overflow: hidden;
      /* 父容器禁止滚动 */
      position: relative;
      /* 隐藏滚动条 */
      scrollbar-width: none;
      /* Firefox */
      -ms-overflow-style: none;
      /* Internet Explorer 10+ */
    }

    .Preview {
      padding-top: 20px;
      padding-bottom: 20px;
      padding-left: 100px;
      padding-right: 100px;
    }

    .topic {

      background: #FFFFFF;
      border-radius: 10px;



      .contents {
        display: flex;
        -ms-flex-direction: row;
        flex-direction: row;
        -ms-flex-align: start;
        align-items: flex-start;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        margin-bottom: 20px;
      }

      .topic_title {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        font-size: 14px;
        color: #2A2A2A;
        line-height: 20px;
      }

      .tiao {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #2A2A2A;
        line-height: 24px;
        margin: 20px 0;
      }

      .topic_Content {
        .type {
          // font-family: PingFangSC, PingFang SC;
          // font-weight: 600;
          // font-size: 14px;
          // color: #2A2A2A;
          // line-height: 20px;
          margin: 20px 0;
        }

        .types {
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 14px;
          color: #2A2A2A;
          line-height: 20px;
          margin: 20px 0;
        }

        .answers {
          margin: 15px 0;
          margin-left: 10px;
          // display: flex;
          display: inline-block;

          .ant-radio-group {
            display: flex;
            gap: 40px;
          }

          .ant-checkbox-group {
            display: flex;
            gap: 40px;
          }

          .answer_item {
            display: flex;
            align-items: flex-start;
            flex-direction: row;
            margin-bottom: 10px;

            .radio_content {
              font-size: 14px;
            }

            .spcialDom {
              flex: 1 1;

              img {
                max-width: 50px;
                height: auto;
                vertical-align: bottom;
              }
            }
          }

          .blanks {
            >span {
              margin-right: 10px;
            }
          }
        }

        .footer {
          width: 100%;
          background: #F7F9FA;
          border-radius: 4px;
          padding: 10px;

          .aws {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 14px;
            color: #2A2A2A;
            line-height: 20px;
          }

          .blus {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 14px;
            color: #549CFF;
          }

          .analysis {
            margin-top: 20px;
            color: #525252;
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
          }
        }
      }
    }

    .card::-webkit-scrollbar {
      display: none;
    }

    .card {
      background: #FFFFFF;
      border-radius: 10px;
      padding: 20px;
      position: relative;
      width: 100%;
      // height: calc(100vh - 82px);

      // height: calc(100vh - 100px);
      overflow: auto;

      .bt_card {
        .select_title {
          border-bottom: 1px dashed #C5C5C5;
          // margin: 20px 0;
          padding-bottom: 10px;

          .title_sz {
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #2A2A2A;
          }
        }

        .bt_flex {
          display: flex;
          flex-wrap: wrap;
          max-width: 100%;
          margin: 19px auto;

          .blues {
            color: #549CFF;
            font-size: 17px;
          }

          .border-blue {
            color: #549CFF;
            border: 1px solid #549CFF;
            /* 蓝色边框 */
          }

          .border-red {
            color: #DD0F0F;
            border: 1px solid #DD0F0F;
            /* 红色边框 */
          }

          .bts {
            width: 36px;
            height: 36px;
            line-height: 40px;
            margin: 7px;
            border-radius: 6px;
            text-align: center;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

    }
  }
}
