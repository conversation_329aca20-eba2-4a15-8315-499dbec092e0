import React, { useEffect, useRef, useState } from "react";
import { useLocation, useSelector } from 'umi';
import Entity from '@/components/entity/entity';

const Entitys: React.FC = () => {
    const location = useLocation();
    const [srcType, setsrcType] = useState('');
    const [userInfo, setUserInfo] = useState<any>(null);
    useEffect(() => {
        // 从 localStorage 获取 userInfo
        const storedUserInfo = JSON.parse(localStorage.getItem('userinfo') || '{}');
        setUserInfo(storedUserInfo);
    }, []);
    useEffect(() => {
        const fileUrl = new URLSearchParams(location.search);
        const fileType = fileUrl.get('fileUrl'); // 获取 param1
        const srl = `http://172.16.151.202${fileType}`;
        setsrcType(srl)
    }, [srcType]);

    return (
        <div style={{ height: '100vh' }} >
            {
                srcType != '' && (
                    <Entity
                        src={srcType}
                        userInfo={userInfo}
                        type="document"
                    />
                )
            }
        </div>
    );
}

export default Entitys;