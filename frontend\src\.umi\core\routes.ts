// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'D:/公司项目/云上川大/exammanage/frontend/node_modules/umi/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@/components/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/",
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'layout__guideLayout__index' */'@/layout/guideLayout/index'), loading: LoadingComponent}),
    "routes": [
      {
        "path": "/",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__index' */'@/pages/index'), loading: LoadingComponent}),
        "title": "home.title",
        "redirect": "/exam",
        "exact": true
      },
      {
        "path": "/exam",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__index' */'@/pages/index'), loading: LoadingComponent}),
        "title": "home.title",
        "routes": [
          {
            "exact": true,
            "path": "/exam/topicManage",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__topicManagement' */'@/pages/topicManagement'), loading: LoadingComponent}),
            "title": "home.title"
          },
          {
            "exact": true,
            "path": "/exam/paperManage",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__paperManagement' */'@/pages/paperManagement'), loading: LoadingComponent}),
            "title": "home.title"
          },
          {
            "exact": true,
            "path": "/exam/testManagementment",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__testManagementment' */'@/pages/testManagementment'), loading: LoadingComponent}),
            "title": "home.title"
          },
          {
            "exact": true,
            "path": "/exam/ContractManagement",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ContractManagement' */'@/pages/ContractManagement'), loading: LoadingComponent}),
            "title": "home.title"
          },
          {
            "exact": true,
            "path": "/exam/recycle",
            "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ExamRecycle' */'@/pages/ExamRecycle'), loading: LoadingComponent}),
            "title": "home.title"
          }
        ]
      },
      {
        "exact": true,
        "path": "/FavoritesPage",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__FavoritesPage' */'@/pages/FavoritesPage'), loading: LoadingComponent}),
        "title": "home.title"
      },
      {
        "path": "/ExamModule/Details",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ExamModule__Details' */'@/pages/ExamModule/Details'), loading: LoadingComponent}),
        "title": "home.title",
        "exact": true
      },
      {
        "path": "/questionSetting",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__QuestionSetting' */'@/pages/QuestionSetting'), loading: LoadingComponent}),
        "title": "home.title",
        "exact": true
      },
      {
        "path": "/ExamModule/Revise",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ExamModule__Revise' */'@/pages/ExamModule/Revise'), loading: LoadingComponent}),
        "title": "home.title",
        "exact": true
      },
      {
        "path": "/ExamModule/Detailspage",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ExamModule__Revise__compoents__Detailspage' */'@/pages/ExamModule/Revise/compoents/Detailspage'), loading: LoadingComponent}),
        "title": "home.title",
        "exact": true
      },
      {
        "path": "/testManagementment/Test",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__testManagementment__TestManagementPage' */'@/pages/testManagementment/TestManagementPage'), loading: LoadingComponent}),
        "title": "home.title",
        "exact": true
      },
      {
        "exact": true,
        "path": "/entitys",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__testManagementment__TestManagementPage__Entitys' */'@/pages/testManagementment/TestManagementPage/Entitys'), loading: LoadingComponent}),
        "title": "home.title"
      },
      {
        "path": "/ExamModule/Detailspage",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ExamModule__Revise__compoents__Detailspage' */'@/pages/ExamModule/Revise/compoents/Detailspage'), loading: LoadingComponent}),
        "title": "home.title",
        "exact": true
      },
      {
        "path": "/ExamModule/Answer",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ExamModule__Revise__compoents__Answer' */'@/pages/ExamModule/Revise/compoents/Answer'), loading: LoadingComponent}),
        "title": "home.title",
        "exact": true
      },
      {
        "path": "/ExamModule/ExamDetails",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ExamModule__Details__compoents__ExamDetails' */'@/pages/ExamModule/Details/compoents/ExamDetails'), loading: LoadingComponent}),
        "title": "home.title",
        "exact": true
      },
      {
        "path": "/topic/manage",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__newTopic' */'@/pages/newTopic'), loading: LoadingComponent}),
        "title": "home.title",
        "exact": true
      },
      {
        "path": "/paper/manage",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__newPaper' */'@/pages/newPaper'), loading: LoadingComponent}),
        "title": "home.title",
        "exact": true
      },
      {
        "path": "/group/manage",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__NewQuestionGroup' */'@/pages/NewQuestionGroup'), loading: LoadingComponent}),
        "title": "题组",
        "exact": true
      },
      {
        "path": "/stuexam",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__StartExam' */'@/pages/StartExam'), loading: LoadingComponent}),
        "title": "开始测验",
        "exact": true
      },
      {
        "path": "/answerPage",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__AnswerPage' */'@/pages/AnswerPage'), loading: LoadingComponent}),
        "title": "答题",
        "exact": true
      },
      {
        "path": "/testsystem",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__TestSystem' */'@/pages/TestSystem'), loading: LoadingComponent}),
        "title": "测验系统",
        "exact": true
      },
      {
        "path": "/testreport",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__TestReport' */'@/pages/TestReport'), loading: LoadingComponent}),
        "title": "报告",
        "exact": true
      },
      {
        "path": "/reportdetail",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__TestReport__TestReportDetail' */'@/pages/TestReport/TestReportDetail'), loading: LoadingComponent}),
        "title": "报告详情",
        "exact": true
      },
      {
        "path": "/examinstructions",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ExamInstructions' */'@/pages/ExamInstructions'), loading: LoadingComponent}),
        "title": "考试说明",
        "exact": true
      },
      {
        "path": "/examrules",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__ExamInstructions__ExamRulePage' */'@/pages/ExamInstructions/ExamRulePage'), loading: LoadingComponent}),
        "title": "考试规则",
        "exact": true
      },
      {
        "path": "/reportdetailpage",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__TestReport__TestReportDetailPage' */'@/pages/TestReport/TestReportDetailPage'), loading: LoadingComponent}),
        "title": "报告详情页",
        "exact": true
      },
      {
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__404' */'@/pages/404'), loading: LoadingComponent}),
        "title": "404",
        "exact": true
      }
    ]
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
