.exam_title_container {
  width: 360px;
  height: 100%;
  overflow: auto;
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  .title_item_box_container {
    .item_box_title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 16px;
      color: #2a2a2a;
    }
    .split_line {
      margin-top: 20px;
      width: 100%;
      height: 1px;
      border: 1px dashed #c5c5c5;
    }
    .checkboxs {
      padding: 20px 0;
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      .checkbox_item {
        width: 36px;
        height: 36px;
        background: #ffffff;
        border-radius: 6px;
        border: 1px solid #c5c5c5;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 500;
        font-size: 18px;
        cursor: pointer;
      }
      .checked {
        // background: var(--primary-color);
        // border: 1px solid var(--primary-color);
        // background: #549CFF;
        // border: 1px solid #549CFF;
        background: #ffe254;
        border: 1px solid #ffe254;
        color: #fff;
      }
      .filled {
        background: #549cff;
        border: 1px solid #549cff;
        color: #fff;
      }
    }
  }
}
