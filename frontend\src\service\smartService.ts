import http from '../http/http';
import SmartTypes from '@/types/smartTypes';

namespace SmartService {
  // 发送知识点分析流程
  export const sendpointsFlow = (data: string[]) =>
    http(`/entity/knowledge/point/analysis`, {
      method: 'POST',
      data: data,
    });
  // 发送智能分析流程
  export const sendFlow = (data: any) =>
    http<SmartTypes.LyricsRes>(`/intelligent/sendflow2`, {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  // 发送智能标签流程
  export const sendFlow2 = (data: any) =>
    http<SmartTypes.LyricsRes>(`/intelligent/sendflow2`, {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 获取字幕
   * @param contentid
   */
  export const querySubtitle = (contentid: string) =>
    http<SmartTypes.LyricsRes>(`/smart/subtitles/select`, {
      method: 'POST',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_smart_subtitles_',
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 获取语音
   * @param contentid
   */
  export const queryVoice = (contentid: string) =>
    http<SmartTypes.LyricsRes>(`/smart/voice/select`, {
      method: 'POST',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_smart_voice_',
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  // 部分更新语音
  export const updatepartVoice = (contentid: string, metadata: any) =>
    http(`/smart/voice/part/update`, {
      method: 'POST',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_smart_voice_',
        metadata,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 查询片段
   * @param contentid
   */
  export const querySequencemeta = (contentid: string) =>
    http<SmartTypes.SequenceRes>(`/cata/sequencemeta/select`, {
      method: 'POST',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_cata_sequencemeta',
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   *
   * @param contentid
   * @param metadata
   */
  export const addSequencemeta = (
    contentid: string,
    metadata: SmartTypes.SequenceMeta[],
  ) =>
    http(`/cata/sequencemeta/add?contentId=${contentid}`, {
      method: 'POST',
      body: JSON.stringify([
        {
          type: 'model_sobey_cata_sequencemeta',
          metadata,
        },
      ]),
      headers: {
        'Content-Type': 'application/json',
      },
    });

  export const modifySection = (frame: boolean, metadata: any) => 
    http("/rman/v1/cata/sequencemeta/part/update", {
      method: "POST",
      params: { frame },
      body: JSON.stringify(metadata),
      headers: {
        'Content-Type': 'application/json',
      },
    })
  /**
   *
   * @param contentid
   * @param metadata
   */
  export const updateSequencemeta = (
    contentid: string,
    metadata: SmartTypes.SequenceMeta[],
  ) =>
    http(`/cata/sequencemeta/update`, {
      method: 'POST',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_cata_sequencemeta',
        metadata,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  // 部分更新知识点
  export const updatepartSequencemeta = (contentid: string, metadata: any) =>
    http(`/cata/sequencemeta/part/update`, {
      method: 'POST',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_cata_sequencemeta',
        metadata,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  // 删除
  export const deleteSequencemeta = (
    contentid: string,
    guid: string | undefined,
  ) =>
    http(`/cata/sequencemeta/delete`, {
      method: 'DELETE',
      body: JSON.stringify({
        contentid,
        metadataType: 'model_sobey_cata_sequencemeta',
        guid_: guid,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });
  /**
   * 获取播放地址
   * @param contentId
   */
  export const fetchDisplayPath = (contentId: string) =>
    http<SmartTypes.FileEntity>(`/search/auth/display/path`, {
      method: 'POST',
      params: {
        contentId,
      },
    });

  /**
   * 生成成品片段
   */
  export const createProduct = (
    contentId: string,
    data: { saveName: string; inPoint: any; outPoint: any }[],
    params: {
      beginId?: string;
      endId?: string;
      colorImprove?: string;
      noiseReduce?: boolean;
      transitionEffect: string;
      isSubtitle?: boolean;
    },
  ) =>
    http<any>(`/entity/cutsave/${contentId}`, {
      method: 'POST',
      body: JSON.stringify(data),
      params,
    });

  /**
   * 获取成品片段
   */
  export const getFinishProduct = (contentId: string) =>
    http<any>('/search/relation/cutvideo', {
      method: 'GET',
      params: {
        contentId,
        tt: new Date().getTime(),
      },
    });


  /**
   * 获取片段拼接目录
   */
   export const getsplicingdirectory = (entityContentId: string) =>
   http<any>('/rman/v1/folder/fragment/splicing/directory', {
     method: 'GET',
     params: {
      entityContentId,
     },
   });

  /**
   * 合成成品片段(没有片头片尾)
   * @param contentId
   * @param data
   * @param params
   */
  export const mergeProduct = (
    contentId: string,
    data: {
      inPoint: any;
      outPoint: any;
    }[],
    params: {
      newName: string;
      colorImprove?: string;
      noiseReduce?: boolean;
      transitionEffect: string;
      isSubtitle?: boolean;
    },
  ) =>
    http<any>(`/entity/mergesave/${contentId}`, {
      method: 'POST',
      body: JSON.stringify(data),
      params: params,
    });
  /**
   * 合成成品片段（包含片头片尾）
   * @param data
   * @param params
   */
  export const mergeProduct2 = (
    data: {
      contentId: string;
      inPoint: any;
      outPoint: any;
    }[],
    params: {
      newName: string;
      colorImprove?: string;
      noiseReduce?: boolean;
      transitionEffect: string;
      isSubtitle?: boolean;
    },
  ) =>
    http<any>(`/entity/muticlip/mergesave`, {
      method: 'POST',
      body: JSON.stringify(data),
      params,
    });

  /**
   * 查询地图节点
   * 
   * @param name
   */
  export const fetchMapList = (name?:any) =>
    http<any>(`/learn/knowledge/queryPage`, {
      method: 'GET',
      params:name?{name:name}:{}
    });
  /**
   * 查询当前地图下所有节点
   * 
   * @param params
   */
  export const fetchNodeList = (id:any,name?:string) =>
    http<any>(`/learn/course/initAll`, {
      method: 'GET',
      params:{
        nodeId:id
      }
    });
  /**
   * 检索节点
   * 
   * @param params
   */
  export const fetchSingleNode = (name:string,id:number) =>
    http<any>(`/learn/course/query`, {
      method: 'GET',
      params:{
        name:name,
        nodeId:id,
        type:2
      }
    });
  /**
   * 更新节点
   * 
   * @param data
   */
  export const updateKnowledge = (data:any) =>
    http<any>(`/learn/knowledgeNode/batch/update`, {
      method: 'POST',
      data:data
    });
  /**
   * 获取素材已绑定的知识点
   * 
   * @param data
   */
  export const fetchBindKnowledges = (data:any) =>
    http<any>(`/learn/knowledgeVideo/cutvideo/bind`, {
      method: 'POST',
      data:data
    });
  /**
   * 批量解绑知识点
   * 
   * @param data
   */
  export const removeKnowledges = (data:any) =>
    http<any>(`/learn/knowledgeVideo/batchRemove`, {
      method: 'POST',
      data:data
    });
}

export default SmartService;
