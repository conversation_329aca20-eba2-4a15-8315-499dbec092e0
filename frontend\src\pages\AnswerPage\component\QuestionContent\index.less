.exam_content_container {
  flex: 1;
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 10px;
  overflow: auto;
  padding: 20px 30px 20px 100px;
  display: flex;
  flex-direction: column;
  .content_header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    .part_text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 24px;
      color: #2a2a2a;
      display: flex;
      align-items: center;
      .part_time {
        margin-left: 10px;
        // width: 28px;
        padding: 0 4px;
        height: 28px;
        background: rgba(241, 84, 74, 0.1);
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: DINAlternate, DINAlternate;
        font-weight: bold;
        font-size: 18px;
        color: #f1544a;
        &:first-child {
          margin-left: 15px;
          margin-right: 10px;
        }
      }
    }
    .header_btns {
      display: flex;
      align-items: center;
      gap: 20px;
      .btn_item {
        width: 104px;
        height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid var(--primary-color);
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: var(--primary-color);
        cursor: pointer;
      }
      .primary {
        background: var(--primary-color);
        color: #fff;
      }
    }
  }

  .start_test {
    margin-top: 20px;
    display: flex;
    align-items: center;

    .start_text {
      font-weight: 600;
      font-size: 14px;
      color: #2a2a2a;
    }
    .read_time {
      width: 180px;
      height: 38px;
      background: rgba(241, 84, 74, 0.1);
      border-radius: 6px;
      font-weight: 500;
      font-size: 14px;
      color: #f1544a;
      margin-left: 10px;
      padding: 0 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .read_left {
        width: 28px;
        height: 28px;
        background: #ffffff;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .test_content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .test_title_desc {
      display: flex;
    }
    .answer_content {
      flex: 1;
      overflow: auto;
    }
  }

  .answer-item {
    display: flex;
    // align-items: center;
  }
  .radio-content {
    font-size: 14px;
  }
  .group_content_container {
    display: flex;
    .group_item_order {
      margin-right: 3px;
    }
  }
}
