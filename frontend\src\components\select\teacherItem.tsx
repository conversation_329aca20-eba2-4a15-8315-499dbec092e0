import React, { FC, useEffect, useState, useRef } from 'react';
import { Form, Select } from 'antd';
import contentListApis from '@/service/contentListApis';
import { subInfoApis } from '@/service/subInfoApis';
const { Option } = Select;
interface IBasicItemProps {
  required: boolean;
  message: string;
  label: string;
  labelCol?: any;
  edit?: boolean;
  name: string;
  multiple: boolean;
  myShared?: boolean;
  selectKeys?: string[]; // 选中老师的code数组
  onChange?: (node:any)=> void; // 回调函数
}
const TeacherItem: FC<IBasicItemProps> = props => {
  const {
    required,
    message,
    label,
    name,
    multiple,
    labelCol,
    selectKeys,
    edit = true,
    myShared
  } = props;
  const [teacherList, setTeacherList] = useState<any>([]);
  const [keyword, setKeyword] = useState<string>('');
  const [page, setPage] = useState<number>(1);
  const [totalPage, setTotalPage] = useState<number>(1);
  const [selectnode, setSelectnode] = useState<any>([]);
  let teacherSelect = useRef([]);

  useEffect(() => {
    getTeacherList();
  }, [keyword, page]);

  const getTeacherList = () => {
    contentListApis
      .getPagingData(
        myShared?
        `sourceTpye=0&school=EDU&roleCodes=r_teacher,r_course_manager,admin_S1,r_resource_manager,r_sys_manager&pageIndex=${page}&pageSize=30${
          keyword ? '&keyWord=' + keyword : ''
        }`:
        `sourceTpye=0&school=EDU&roleCodes=r_teacher&pageIndex=${page}&pageSize=30${
          keyword ? '&keyWord=' + keyword : ''
        }`
      )
      .then(async (res: any) => {
        if (res && (res.errorCode === 'success' || res.errorCode === '200')) {
          try {
            if (
              selectKeys &&
              selectKeys.length > 0 &&
              teacherSelect.current?.length <= 0
            ) {
              await getSelectTeachers(selectKeys);
            }
            setTotalPage(res.extendMessage.pageTotal);
            if (page === 1) {
              let arr = unique(
                [
                  ...teacherSelect.current,
                  ...res.extendMessage.results,
                ],
                'fieldCode',
              );
              setTeacherList(arr);
            } else {
              let arr = unique(
                [
                  ...teacherSelect.current,
                  ...teacherList,
                  ...res.extendMessage.results,
                ],
                'fieldCode',
              );
              setTeacherList(arr);
            }
          } catch (e) {
            throw e;
          }
        }
      });
  };

  const getSelectTeachers = (selectKeys: string[]) => {
    return new Promise((resolve, reject) => {
      subInfoApis.selectUsers(selectKeys).then((res: any) => {
        if (res) {
          let arr = res.map((item: any) => {
            return {
              extendedValue:item.org,
              fieldCode: item.id,
              fieldValue: item.name,
            };
          });
          teacherSelect.current = arr || [];
          resolve(arr);
        } else {
          reject();
        }
      });
    });
  };

  const unique = (arr: any, val: string) => {
    const res = new Map();
    return arr.filter(
      (item: any) => !res.has(item[val]) && res.set(item[val], 1),
    );
  };

  const handleSearch = (value: string) => {
    setPage(1);
    setKeyword(value);
  };

  const handleChange = (value: string, node: any) => {
    if(props.onChange){
      props.onChange(node)
    }
    if (!value || value.length) {
      setPage(1);
      setKeyword('');
    }
  };
  const handleScroll = (e: any) => {
    const { target } = e;
    const total = target.scrollTop + target.offsetHeight;
    const scrollHeight = target.scrollHeight;
    if (
      total >= scrollHeight - 1 &&
      total < scrollHeight + 1 &&
      page < totalPage
    ) {
      setPage(page + 1);
    }
  };
  return (
    <Form.Item
      label={label}
      name={name}
      rules={[{ required: required, message: message }]}
      labelCol={labelCol}
    >
      {!edit ? (
        <div
          style={{
            lineHeight: '32px',
            wordBreak: 'break-all',
            whiteSpace: 'break-spaces',
          }}
        >
          {teacherList
            .filter((item: any) => selectKeys?.includes(item?.fieldCode))
            .map((item: any) => item.fieldValue+(item.extendedValue ? `（${item.extendedValue}）` :''))
            // .map((item: any) => item.fieldValue+(`（${item.extendedValue}，${''+item.fieldCode}）`))
            .join(',')
            }
        </div>
      ) : multiple ? (
        <Select
          mode="multiple"
          showSearch
          // style={required ? { width: 200 } : { width: 200 }}
          placeholder={message}
          allowClear={true}
          // labelInValue
          filterOption={false}
          onSearch={handleSearch}
          onChange={handleChange}
          onPopupScroll={handleScroll}
        >
          {teacherList.map((item: any) => (
            <Option value={item.fieldCode} 
              // key={'teacher' + item.fieldCode} name={item.fieldValue}
              key={item.fieldCode}
              >
              <span title={`${item.fieldValue}${`（${item.extendedValue}，${''+item.fieldCode}）`}`}>
                {item.fieldValue}{`（${item.extendedValue}，${item.fieldCode}）` }
              </span>
            </Option>
          ))}
        </Select>
      ) : (
        <Select
          showSearch
          // style={required ? { width: 200 } : { width: 200 }}
          placeholder={message}
          allowClear={true}
          filterOption={false}
          onSearch={handleSearch}
          onChange={handleChange}
          onPopupScroll={handleScroll}
        >
          {teacherList.map((item: any) => (
            <Option value={item.fieldCode} key={'teacher' + item.fieldCode}>
              {/* {item.fieldValue}{item.extendedValue ? `（${item.extendedValue}）` :'' }{``+item.fieldCode} */}
              <span title={`${item.fieldValue}${`（${item.extendedValue}，${''+item.fieldCode}）`}`}>
                {item.fieldValue}{`（${item.extendedValue}，${item.fieldCode}）` }
              </span>
            </Option>
          ))}
        </Select>
      )}
    </Form.Item>
  );
};

export default TeacherItem;
