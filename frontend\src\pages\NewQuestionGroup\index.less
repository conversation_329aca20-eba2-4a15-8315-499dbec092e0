.question_group_container {
  .header_container {
    .header {
      padding: 0 40px;
      width: 100%;
      height: 60px;
      background: #549cff10;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      .header_left {
        a {
          width: 32px;
          height: 22px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #919191;
          line-height: 22px;
          span:first-child {
            margin-right: 10px;
          }
          span:last-child {
            margin-right: 60px;
          }
        }
        > span {
          width: 60px;
          height: 28px;
          font-size: 20px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: var(--primary-color);
          line-height: 28px;
        }
      }
      .header_right {
        .ant-btn {
          border-radius: 4px;
        }
        // .ant-btn:first-child{
        //   margin-right: 20px;
        // }
      }
    }
  }
  .group_content {
    width: 100%;
    height: calc(100vh - 80px);
    overflow-y: auto;
  }
  .divider {
    width: 100%;
    height: 1px;
    // border-style: ;
    // border: 1px dashed #c5c5c5;
    background-image: linear-gradient(
      to right,
      #fff 35%,
      #c5c5c5 0%
    ); /* 35%设置虚线点x轴上的长度 */
    background-position: bottom; /* top配置上边框位置的虚线 */
    background-size: 10px 1px; /* 第一个参数设置虚线点的间距；第二个参数设置虚线点y轴上的长度 */
    background-repeat: repeat-x;
  }
  .question_item {
    width: 100%;
    margin-top: 51px;

    .question_content {
      padding: 0 12%;
    }
  }

  .form_item_top {
    width: 100%;
    padding: 0 12%;
    margin-top: 40px;
    .form_item_header {
      position: relative;
      display: flex;
      align-items: center;
      .tag {
        margin-right: 10px;
        display: inline-block;
        width: 4px;
        height: 23px;
        background: var(--primary-color);
      }
      .opt_box {
        position: absolute;
        right: 66px;
        .addBlanks {
          margin-left: 20px;
        }
      }
    }
    .form_item_body {
      padding: 20px 66px 0 66px;
      .form_item_content {
        display: flex;
        justify-content: space-between;
      }
      .ant-row {
        display: flex;
        justify-content: space-between;
        .ant-form-item-label {
          width: 20%;
          text-align: right;
        }
        .tagItem {
          .ant-form-item-control-input {
            border: 1px solid #d9d9d9;
            .ant-form-item-control-input-content {
              display: flex;
              flex-direction: row;
              flex-wrap: wrap;
              .ant-tag {
                padding: 0px 5px;
                margin: 0 0 0 5px;
              }
              .tag-input {
                width: 80px;
                padding: 0px 5px;
                margin: 0 0 0 5px;
              }
            }
          }
        }
      }
    }
    .enclosure {
      &.hidden {
        height: 0;
        overflow: hidden;
      }
      padding: 0 0 0 66px;
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      .ant-upload {
        width: 0;
        overflow: hidden;
      }
      > .label {
        white-space: nowrap;
      }
      > span {
        display: flex;
        .ant-upload-list {
          margin-left: 10px;
          display: flex;
          flex-wrap: wrap;
          flex-direction: row;
          .ant-upload-list-item {
            margin: 0 !important;
          }
        }
      }
    }
  }
}
