import React, { FC, useState, useImperativeHandle } from 'react';
import ResourceModal from '@/components/ResourceModal';
import { getTreebylevel, resourceDetail } from '@/api/questionset';
import { queryResourceLabel } from '@/api/questionset';
import { getUuid } from '@/utils';

interface ResourseUploadProps {
  resourseRef: any;
  emitResourse: (val: any) => void;
}
const ResourseUpload: FC<ResourseUploadProps> = ({ resourseRef, emitResourse }) => {
  // 是否是附件
  const [isAttachment, setIsAttachment] = useState<boolean>(false);
  const [modalLoading, setModalLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalTreeData, setModalTreeData] = useState<ResourceModal.treeItem[]>([]);
  const [courseResources, setCourseResources] = useState<any[]>([]);
  useImperativeHandle(resourseRef, () => {
    return {
      open
    }
  });
  const open = () => {
    showModal('resources');
    // setIsAttachment(true);
  }
  //显示Modal
  const showModal = (type: string) => {
    setModalLoading(true);
    setModalVisible(true);
    const getTreeData = () => {
      getTreebylevel().then((res: any) => {
        if (res && res.success) {
          let treeData = forTree(res.data, []);
          setModalTreeData(treeData);
          setTimeout(() => {
            setModalLoading(false);
            setModalVisible(true);
          }, 100);
        } else {
          console.error(res);
        }
      });
    };
    // 遍历目录树
    const forTree = (tree: any, parentsKeys: string[]) => {
      return tree.map((item: any) => {
        return {
          key: item.id,
          parentsKeys,
          title: item.name,
          path: item.path,
          children: item.children ?
            forTree(item.children, [...parentsKeys, item.code]) :
            []
        };
      });
    };

    getTreeData();
    // setResType(type);
  };

  // 获取附件列表
  const getAttachmentList = (
    files: MicroCourse.IFileEntity[],
    contentIds: string[])
    : MicroCourse.IAttachmentFile[] =>
    files.map((item, index) => {
      const fileInfo = getFileItem(item);
      return {
        extraData: item.entityName,
        fileState: 'ready',
        fileSize: fileInfo.fileSize,
        filePath: fileInfo.displayPath,
        fileLength: 0,
        fileGUID: getUuid(),
        notSynced: true,
        resourceId: contentIds?.[index]
      };
    });

  // 获取文件信息
  const getFileItem = (
    data: any)
    : MicroCourse.IFileItem => {
    const { fileGroups } = data;
    const preIndex = fileGroups.findIndex(
      (file: any) => file.typeCode === 'previewfile');

    const souIndex = fileGroups.findIndex(
      (file: any) => file.typeCode === 'sourcefile');

    if (data.type.includes("document")) {
      return fileGroups[souIndex].fileItems[0];
    }
    return preIndex > -1 ?
      fileGroups[preIndex].fileItems[0] :
      fileGroups[souIndex].fileItems[0];
  };

  const getResourceLabel = (ids: string | string[] | any[], resource_link?: any) => {
    const idL = typeof ids === "string" ? [ids] : typeof ids[0] === "string" ? ids : ids.map((item: any) => item.contentId);
    return queryResourceLabel(idL).then((res: any) => {
      if (res.status === 200) {
        if (typeof ids === "string" || typeof ids?.[0] === "string") {
          return res.data;
        }
        if (typeof ids[0] !== "string") {
          const temp = ids.map((resource: any, index: number) => ({
            ...resource,
            ...(res.data[index] ?? {})
          }));
          setCourseResources(resource_link ? temp.concat(resource_link.map((item: any) => JSON.parse(item)).filter((item: any) => item.type === 0)) : temp);
        }
      }
    });
  };

  const resourceModalConfirm = (resource: any[]) => {
    if (isAttachment) {
      if (resource.length > 0) {
        const promise: any[] = [];
        resource.forEach((item) => {
          promise.push(resourceDetail(item.contentId, 4));
        });
        Promise.all(promise).then((res) => {
          const files = res.filter((r) => r.success && r.data).map((r) => r.data);
          const attachmentList = getAttachmentList(files, resource.map((item: any) => item.contentId));
          console.log(attachmentList);

          // dispatch({
          //   type: 'microCourse/updateCourseProps',
          //   payload: {
          //     attachResources: [
          //       {
          //         groupType: 'other',
          //         status: 'ready',
          //         groupName: 'attachmentgroup',
          //         fileItems: courseData.attachResources[0].fileItems.concat(
          //           attachmentList)

          //       }]

          //   }
          // });
          // form.setFieldsValue({
          //   attachmentResources: courseData.attachResources[0].fileItems.concat(
          //     attachmentList)

          // });
        });
      }
    } else {
      // dispatch({
      //   type: 'microCourse/updateCourseProps',
      //   payload: {
      //     courseResources: resource,
      //   },
      // });
      getResourceLabel(resource.map((item) => item.contentId)).then((data: any) => {
        const newResources = resource.map((item, index: number) => ({ ...item, fileGUID: getUuid(), ...(data?.[index] ?? {}), nowKey: Date.now() }));
        setCourseResources([...courseResources, ...newResources]);
        emitResourse([...courseResources, ...newResources])
      });
    }
    setModalVisible(false);
  };
  return (
    <ResourceModal
      treeData={modalTreeData}
      visible={modalVisible}
      onConfirm={resourceModalConfirm}
      onCancel={() => setModalVisible(false)}
      onShowDetail={(id, detail) => {
        // setEntityPreview({
        //   id: id,
        //   name: detail.name,
        //   type: detail.type
        // });
        // setEntityModalVisible(true);
      }}
      fileType={isAttachment ? undefined : ['biz_sobey_video']}
      multi />
  );
};

export default ResourseUpload;