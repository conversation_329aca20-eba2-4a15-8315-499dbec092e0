.special_training_container {
  width: 100%;
  margin: 20px 0;
  display: flex;
  justify-content: center;
  .content {
    width: 100%;
  }
  .tab_container {
    display: flex;
    width: 100%;
    border-radius: 6px;
    overflow: hidden;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #2a2a2a;
    background-color: #fff;
    cursor: pointer;
    // antd的tab
    .ant-tabs-tab-btn {
      width: 240px;
      text-align: center;
    }
    .ant-tabs-tab-active {
      background: var(--primary-color);
      .ant-tabs-tab-btn {
        color: #fff;
      }
    }
    .ant-tabs-ink-bar {
      visibility: hidden;
    }
    .ant-tabs-top > .ant-tabs-nav::before {
      border: none;
    }
    .ant-tabs-tab + .ant-tabs-tab {
      margin: 0;
    }
    .tab_item {
      width: 240px;
      height: 48px;
      line-height: 48px;
      text-align: center;
    }
    .tab_item_active {
      background: var(--primary-color);
      //   border-radius: 6px 0px 0px 6px;
      color: #fff;
    }
  }

  .list_container {
    width: 100%;
    // min-height: 912px;
    overflow: auto;
    height: calc(100vh - 108px - 60px);
    background: #ffffff;
    border-radius: 10px;
    margin-top: 20px;
    padding: 30px;

    .list_item {
      margin-top: 30px;
      width: 100%;
      height: 106px;
      background: #f7f9fa;
      border-radius: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;
      &:first-child {
        margin-top: 0;
      }
      .info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        .test_name {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 18px;
          color: #2a2a2a;
          max-width: 500px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .test_desc {
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #525252;
          display: flex;
          .test_count {
            margin-left: 50px;
            .test_num {
              color: var(--primary-color);
            }
          }
        }
      }
      .btns {
        display: flex;
        gap: 20px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        .btn_item {
          width: 104px;
          height: 36px;
          border-radius: 4px;
          line-height: 36px;
          text-align: center;
          cursor: pointer;
        }
        .primary {
          background: var(--primary-color);
        }
        .default {
          background: #ffffff;
          color: var(--primary-color);
          border: 1px solid var(--primary-color);
        }
      }
    }

    .pagination_container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
