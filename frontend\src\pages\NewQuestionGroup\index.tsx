import examManageApis from '@/api/exam';
import { Editor } from '@/components';
import examType from '@/types/examType';
import { getSensitiveWordPost } from '@/utils';
import { CloseOutlined, PaperClipOutlined } from '@ant-design/icons';
import { Button, message, Modal, Radio, Space, Upload } from 'antd';
import { cloneDeep } from 'lodash';
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { history, useLocation } from 'umi';
import { v4 as uuidv4 } from 'uuid';
import BasicInfo from './component/BasicInfo';
import BlankQuestion from './component/BlanksQuestion';
import JudgmentQuestion from './component/JudgmentQuestion';
import MultipleChoiceQuestion from './component/MultipleChoiceQuestion';
import PreviewGroupModal from './component/PreviewGroupModal';
import SingleChoiceQuestion from './component/SingleChoiceQuestion';
import SubjectiveQuestion from './component/SubjectiveQuestion';
import './index.less';
import { IQuestionGropItem } from './types';
import { fetchEditorContent } from './utils';

const NewQuestionGroup: FC = () => {
  const location: any = useLocation();
  const type_enum = examType.optionType_; //题目类型

  const currentOpt_type = useRef<any>(location.query.opt_type);
  const curQuestionId = useRef<any>(location.query.detail);
  // 编辑情况下，新增小题
  const addOfEdit = useRef<boolean>(false);

  // 新建的标识
  const afterCreateFlag = useRef(false);
  //
  const [btnLoading, setBtnLoading] = useState(false);
  // 第一次保存后返回的题目id
  const firstSaveId = useRef<string>('');
  // 基本信息ref
  const basicInfoRef = useRef<any>();

  // 小题列表
  const [topicList, setTopicList] = useState<IQuestionGropItem[]>([]);
  //  保持数据真实的情况，避免闭包的影响
  const questionList = useRef<IQuestionGropItem[]>([]);
  // 基础信息
  const [basicDetail, setBasicDetail] = useState<any>({});
  // 题组预览
  const [previewShow, setPreviewShow] = useState<boolean>(false);
  const saveflag = useRef<any>(undefined);
  //#region 新建题目弹窗
  const [newTopicVisible, setNewTopicVisible] = useState<boolean>(false);
  const [topicType, setTopicType] = useState<any>(0);
  const onChange = (e: any) => {
    setTopicType(e.target.value);
  };
  const createTopic = () => {
    addOfEdit.current = true;
    const newItem = {
      questions_type: topicType,
      id: uuidv4(),
      sortTimes: 0,
    };
    setTopicList([...topicList, newItem]);
    questionList.current = [...topicList, newItem];
    setNewTopicVisible(false);
  };
  //#endregion

  //#region 初始化
  const fetchDetail = async (id: number) => {
    const res = await examManageApis.fetchTopicDetail(id);
    if (res.status === 200) {
      let data = res.data;
      data.questions_level = data.questions_level || [];
      data.questions_major = data.questions_major || [];
      const initBasic = {
        // 试题难度
        questions_difficulty: data.questions_difficulty,
        // 适用层次
        questions_level: data.questions_level,
        // 适用专业
        questions_major: data.questions_major,
        // 适用课程
        questionCourseList: data.questionCourseList,
        // 分享人
        share_users: data.share_users,
        // 标签
        labels: data.labels,
        // 知识点
        knowledge_points: data.knowledge_points,
        // 认知层次
        cognitive_level: data.cognitive_level,
        // 考核方向
        assessmentCode: data.assessment?.assessmentCode,
        // 应用
        applicationClassCode: data?.applicationClasses?.map((item: any) => item?.applicationClassCode),
        applicationClasses: data?.applicationClasses,
        // 题目来源
        questionSourceCode: data.questionSourceCode,
        questionSourceName: data.questionSourceName,
      };
      setBasicDetail(initBasic);
      setTopicList(
        data?.groupQuestions?.map((item: any) => ({ ...item, sortTimes: 0 })) ||
        [],
      );
      questionList.current = data?.groupQuestions || [];
      setTopic(data?.questions_content);
      const list_ = data?.fileList?.map((item: any) => {
        return {
          name: item.attachmentName,
          status: 'done',
          size: item.attachmentSize,
          url: item.attachmentSource,
          uid: item.contentId,
        };
      });
      setFileList(list_ || []);
    }
  };
  useEffect(() => {
    const id = location.query.detail;
    if (id) {
      fetchDetail(id);
    }
  }, []);
  //#endregion

  // 小题信息修改
  const handleUpdateGroupItemInfo = useCallback(
    (type: keyof IQuestionGropItem, value: any, itemId: string) => {
      const cloneList = cloneDeep(questionList.current);
      const itemIndex = cloneList.findIndex((cell: any) => cell.id === itemId);

      cloneList[itemIndex] = {
        ...cloneList[itemIndex],
        [type]: value,
      };
      setTopicList([...cloneList]);
      questionList.current = cloneList;
    },
    [],
  );

  // 校验小题的答案内容
  const verifyAnswer = (content_list: any, type: any, index: number) => {
    // 不包含主观题
    if (type === 3) {
      return true;
    }

    const emptyFlag =
      type == 2
        ? content_list.some((item: any) => {
          if (item?.answerType === 1 || item?.answerRange === 1) {
            return !item.content;
          }
          return !item?.answerMax || !item?.answerMin;
        })
        : content_list.some((item: any) => !item.content);
    if (emptyFlag) {
      message.info(`第${index + 1}小题答案内容不能为空`);
      return false;
    }
    const errItemIndex = content_list.findIndex((item: any) => {
      if (item?.answerType === 1 || item?.answerRange === 1) {
        return false;
      }
      if (Number(item.answerMin) >= Number(item.answerMax)) {
        return true;
      }
      return false;
    });
    if (errItemIndex !== -1) {
      message.info(`第${index + 1}小题的第${errItemIndex + 1}空答案范围出错`);
      return false;
    }
    return true;
  };

  //#region
  const save = async (flag?: string) => {
    console.log('basicInfoRef', basicInfoRef.current.knowledgeValue2);

    setBtnLoading(true);
    // 基础信息
    const basicInfo = basicInfoRef.current.getFieldsValue();
    let knowledge: any = [];
    if (basicInfoRef.current.knowledgeValue.length > 0) {
      basicInfoRef.current.knowledgeValue.forEach((item: any) => {
        knowledge.push({
          entity: item.data.entity,
          entity_id: item.data.id || null,
          mapId: item.data.mapId || null,
          mapName: item.data.mapName || null,
          nodeId: item.data.nodeId || null,
          parentNode: item.data.parentNode || null,
          propertyValue: item.data.propertyValue || null,
        });
      });
    }
    let lessonledge: any = [];
    if (basicInfoRef.current.knowledgeValue2.length > 0) {
      basicInfoRef.current.knowledgeValue2.forEach((item: any) => {
        console.log('item', item);

        lessonledge.push({
          courseId: item.courseId || item.value,
          courseName: item.courseName || item.label,
          // data: item,
        });
      });
    }
    const basicParam = {
      // 试题难度
      questions_difficulty: basicInfo.questions_difficulty,
      // 适用层次
      questions_level: basicInfo.questions_level,
      // 适用专业
      questions_major: basicInfo.questions_major,
      // 适用课程
      // questions_lesson: basicInfo.questions_lesson,
      questionCourseList: lessonledge,
      // 分享人
      share_users: basicInfo.share_users,
      // 标签
      labels: basicInfoRef.current.tags,
      // 知识点
      knowledge_points: knowledge,
      // 认知层次
      cognitive_level: basicInfo.cognitive_level,
      // 考核方向
      assessment: {
        assessmentCode: basicInfo.assessmentCode,
        assessmentName: basicInfoRef.current.directionName,
      },
      // 应用
      // applicationClassCode: basicInfo.applicationClassCode,
      // applicationClassName: basicInfoRef.current.classifyName,
      applicationClasses: basicInfoRef.current.applicationSet,
      // 题目来源
      questionSourceCode: basicInfoRef.current.questionSourceCode,
      questionSourceName: basicInfo.questionSourceName,
    };
    // 小题列表格式化
    const formatGroupList = questionList.current.map((item: any) => {
      if (item?.fileList?.length > 0) {
        item.fileList = item.fileList
          .filter((item: any) => item.status == 'done')
          .map((item: any) => {
            return {
              attachmentName: item.name,
              attachmentSize: item.size,
              attachmentSource: item.url || item.response.data.httpPath,
              contentId: item.response?.data?.contentId || item.uid,
            };
          });
      }
      return { ...item, insideQuestionGroup: true, id: '' };
    });
    const params: any = {
      // 小题列表
      groupQuestions: formatGroupList,
      // 题组题目文件列表
      fileList: fileList
        .filter((item: any) => item.status == 'done')
        .map((item: any) => {
          return {
            attachmentName: item.name,
            attachmentSize: item.size,
            attachmentSource: item.url || item.response.data.httpPath,
            contentId: item.response?.data?.contentId || item.uid,
          };
        }),
      // 题组题目描述
      questions_content: topic,
      // 类型
      questions_type: 5,
      ...basicParam,
    };

    // 题组题目必填
    if (!topic) {
      message.info('题目不能为空');
      setBtnLoading(false);
      return;
    }

    const verifyFlag = questionList.current.some((item, index) => {
      if (!item?.questions_content) {
        message.info(`第${index + 1}小题题目不能为空`);
        return false;
      }

      if (item?.questions_type !== 3) {
        if (!item?.questions_answers) {
          if (item?.questions_type === 2) {
            message.info(`第${index + 1}小题答案不能为空`);
          } else {
            message.info(`第${index + 1}小题答案至少勾选一个`);
          }
          return false;
        }
      }
      if (
        item?.questions_type === 3 &&
        item?.hasAttachment.some((cell: any) => cell.name === '') &&
        item?.enableHasAttachment
      ) {
        message.warning(`第${index + 1}小题请填写要添加的附件名称！`);
        return false;
      }
      // 小题的答案内容,不包含主观题
      const answerContentFlag = verifyAnswer(
        item?.questions_options,
        item?.questions_type,
        index,
      );
      if (!answerContentFlag) {
        return false;
      }
      return true;
    });
    if (!verifyFlag && questionList.current.length > 0) {
      setBtnLoading(false);
      return;
    }

    let res: any;
    let sensitiveWord = JSON.stringify(
      Object.keys(params).map((key: any) => {
        if (key == 'knowledge_points') {
          return params[key].map((item: any) => item.entity);
        } else if (key == 'questions_options') {
          return params[key].map((item: any) => item.content);
        } else if (key == 'questions_major') {
          return '';
        } else {
          return params[key];
        }
      }),
    ).replace(RegExp('<.+?>', 'g'), '');
    if (currentOpt_type.current === 'edit' || !!firstSaveId.current) {
      res = await getSensitiveWordPost(sensitiveWord, '内容', () =>
        examManageApis.topicUpdate(
          curQuestionId.current || firstSaveId.current,
          params,
        ),
      );
    } else {
      res = await getSensitiveWordPost(sensitiveWord, '内容', () =>
        examManageApis.addTopic(params),
      );
    }
    setBtnLoading(false);
    if (!res) return; //有敏感词
    if (res.status === 200) {
      message.success('保存成功');
      if (!firstSaveId.current && !curQuestionId.current) {
        firstSaveId.current = res.data.id;
      }
      if (flag === 'back') {
        afterCreateFlag.current = true;
      }
      return 0;
    } else {
      message.error('保存失败，请重试');
      return 1;
    }
  };

  const copy = () => {
    // save().then((res: any) => {
    //   if (res === 0) {
    //     saveflag.current = undefined;
    //     currentOpt_type.current = 'copy';
    //     // clean();
    //     message.success('复制成功，当前为复制的试题，可直接编辑');
    //     history.push({
    //       pathname: `/topic/manage`,
    //       query: {
    //         opt_type: 'copy',
    //         detail: detail.id,
    //       },
    //     });
    //   } else if (res === 1) {
    //     message.error('保存失败，请重试');
    //   } else {
    //   }
    // });
  };

  const cleanEditorContent = (name: any) => {
    (window as any).tinymce.editors[name].setContent('');
  };

  //清空题目选项
  const clean = () => {
    // 清空题目
    cleanEditorContent('head_topic'); //清空题目；
    // cleanEditorContent('analysis'); //清空解析；
    setTopic('');
    // 清空文件列表
    setFileList([]);

    // 清空小题列表
    setTopicList([]);
    questionList.current = [];

    // 清空基本信息
    if (basicInfoRef.current) {
      basicInfoRef.current.resetFields();
    }

    // 其他需要重置的状态
    setPreviewShow(false);
    setNewTopicVisible(false);
  };

  const create_ = () => {
    // 先保存后在继续创建
    firstSaveId.current = '';  // 这是更新id 这里只做保存不做更新
    save().then((res: any) => {
      if (res === 0) {
        saveflag.current = undefined;
        clean();
      } else if (res === 1) {
        message.error('保存失败，请重试');
      } else {
      }
    });
  };

  const handlePathBack = () => {
    if (location.query.from === 'out') {
      addTopicBack(null, true);
    } else {
      history.goBack();
    }
  };

  // 预览题组
  const handlePreviewGroup = () => {
    if (questionList.current.length > 0) {
      setPreviewShow(true);
      return;
    }
    message.error('请先添加小题');
  };

  // 排序确认
  const handleSortEnd = (list: IQuestionGropItem[]) => {
    setTopicList([...list]);
    questionList.current = list;
    setPreviewShow(false);
  };
  //#endregion

  // 新建题目
  const handleCreateNew = () => {
    setNewTopicVisible(true);
  };

  // 删除题目
  const handleDelQuestionItem = useCallback((itemId: string) => {
    const newQuestionList = questionList.current.filter(
      (item: any) => item.id !== itemId,
    );
    setTopicList(newQuestionList);
    questionList.current = newQuestionList;
  }, []);

  //#region 题组题目
  const uploadRef = useRef<any>(null);
  const [topic, setTopic] = useState<any>(''); //题目
  const [fileList, setFileList] = useState<any>([]);

  const topicChange = (e: any, item: string) => {
    const data = e.level?.content;
    if (item === 'head_topic') {
      //通过e.target.content获取的内容可能不是想要的数据，并且直接使用getContent会使图片的src是相对路径，跨项目引用会访问不到,只能手动避开；
      const realTopic = fetchEditorContent('head_topic') ? data : '';
      if (realTopic === undefined) return; //处理插入化学公式会出现的bug;
      setTopic(realTopic);
    }
  };
  //附件添加
  const uploadProps = {
    name: 'file',
    action: '/rman/v1/upload/reference/material/import',
    headers: {
      authorization: 'authorization-text',
    },
    beforeUpload: async (file: any, list: any) => {
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('上传文件不能超过100M');
      }
      if (fileList.length + 1 > 10) {
        message.info('上传文件数量不能超过10个');
        return false;
      }
      let names = list.map((e: any) => e.name).join('');
      let res: any = await getSensitiveWordPost(
        names,
        '附件名',
        () => true,
        () => false,
      );
      if (!res) return Upload.LIST_IGNORE;
      return isLt100M || Upload.LIST_IGNORE; //隐藏不符合列表的文件
    },
    fileList,
    showUploadList: {
      removeIcon: <CloseOutlined />,
    },
    maxCount: 10,
    onChange(info: any) {
      setFileList(info.fileList);
      if (info.file.status !== 'uploading') {
      }
      if (info.file.status === 'done' && info.file.response?.success) {
        message.success(`${info.file.name} 上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败.`);
      } else if (info.file.status === 'done' && !info.file.response?.success) {
        message.error(`${info.file.name} 上传失败,不支持该格式`);
        setFileList((pre: any) => {
          return pre.map((item: any) => {
            if (item.uid === info.file.uid) {
              return {
                ...item,
                status: 'error',
              };
            }
            return item;
          });
        });
      }
    },
  };
  //#endregion

  const addTopicBack = (data?: any, flag?: boolean) => {
    const data_: any = {
      action: flag ? 'addTopicBack' : 'addTopic',
      data,
    };
    window.parent.postMessage(JSON.stringify(data_), window.location.origin);
  };

  // 渲染不同的题目
  const renderQuestion = useCallback(
    (item: IQuestionGropItem, index: number) => {
      // 0 单选题 1 多选题 2 填空题 3 主观题 4 判断题
      switch (item.questions_type) {
        case 0:
          return (
            <SingleChoiceQuestion
              detail={item}
              itemIndex={index}
              onDeleteTopic={handleDelQuestionItem}
              onChangeItem={handleUpdateGroupItemInfo}
              editType={addOfEdit.current || currentOpt_type.current}
              key={item.id}
            />
          );
        case 1:
          return (
            <MultipleChoiceQuestion
              detail={{ ...item }}
              itemIndex={index}
              onDeleteTopic={handleDelQuestionItem}
              onChangeItem={handleUpdateGroupItemInfo}
              editType={addOfEdit.current || currentOpt_type.current}
              key={item.id}
            />
          );
        case 2:
          return (
            <BlankQuestion
              detail={{ ...item }}
              itemIndex={index}
              onDeleteTopic={handleDelQuestionItem}
              onChangeItem={handleUpdateGroupItemInfo}
              editType={addOfEdit.current || currentOpt_type.current}
              key={item.id}
            />
          );
        case 3:
          return (
            <SubjectiveQuestion
              detail={{ ...item }}
              itemIndex={index}
              onDeleteTopic={handleDelQuestionItem}
              onChangeItem={handleUpdateGroupItemInfo}
              editType={addOfEdit.current || currentOpt_type.current}
              key={item.id}
            />
          );
        case 4:
          return (
            <JudgmentQuestion
              detail={{ ...item }}
              itemIndex={index}
              onDeleteTopic={handleDelQuestionItem}
              onChangeItem={handleUpdateGroupItemInfo}
              editType={addOfEdit.current || currentOpt_type.current}
              key={item.id}
            />
          );
        default:
          return null;
      }
    },
    [],
  );

  const renderQuestionList = useMemo(() => {
    return topicList.map((item, index) => {
      return (
        <div className="question_item" key={`${item.id}_${item.sortTimes}`}>
          <div className="question_content">{renderQuestion(item, index)}</div>
          <div
            className="divider"
            style={{
              visibility: topicList.length === index + 1 ? 'hidden' : 'visible',
              marginTop: 70,
            }}
          />
        </div>
      );
    });
  }, [topicList]);
  return (
    <div className="question_group_container">
      <div className="header_container">
        <div className="header">
          <div className="header_left">
            <a
              onClick={() => {
                if (location.query.from === 'out') {
                  addTopicBack(null, true);
                } else {
                  history.goBack();
                }
              }}
            >
              <span>{'<'}</span>
              <span>返回</span>
            </a>
            <span>题组</span>
          </div>
          <div className="header_right">
            {currentOpt_type.current === 'new' ? (
              <Space>
                <Button
                  type="primary"
                  // disabled={afterCreateFlag.current}
                  onClick={handleCreateNew}
                >
                  新建小题
                </Button>
                <Button
                  onClick={() => save('back')}
                  // disabled={afterCreateFlag.current}
                  loading={btnLoading}
                >
                  保存
                </Button>
                <Button onClick={create_}>继续创建</Button>
                <Button onClick={handlePathBack}>关闭</Button>
                <Button
                  onClick={handlePreviewGroup}
                  loading={btnLoading}
                // disabled={afterCreateFlag.current}
                >
                  预览题组
                </Button>

                {/* <Button
                  onClick={() => save('back')}
                  // disabled={afterCreateFlag.current}
                  loading={btnLoading}
                >
                  继续创建
                </Button> */}
              </Space>
            ) : currentOpt_type.current === 'edit' ? (
              <Space>
                {/* <Button type="primary" onClick={copy}>
                  复制
                </Button> */}
                <Button
                  type="primary"
                  // disabled={afterCreateFlag.current}
                  onClick={handleCreateNew}
                >
                  新建小题
                </Button>
                <Button
                  type="primary"
                  onClick={() => save()}
                  loading={btnLoading}
                >
                  保存
                </Button>
                <Button onClick={handlePathBack}>关闭</Button>
                <Button onClick={handlePreviewGroup} loading={btnLoading}>
                  预览题组
                </Button>
              </Space>
            ) : (
              <Space>
                <Button
                  type="primary"
                  onClick={() => save()}
                  loading={btnLoading}
                >
                  保存
                </Button>
                <Button onClick={handlePathBack}>关闭</Button>
              </Space>
            )}
          </div>
        </div>
      </div>
      <div className="group_content">
        <BasicInfo ref={basicInfoRef} detail={basicDetail} />
        <div className="divider" />
        <div className="form_item_top">
          <div className="form_item_header">
            <span className="tag"></span>
            <span>题目</span>
          </div>
          <div className="form_item_body">
            <Editor
              name="head_topic"
              value={topic}
              onChange={(e: any) => topicChange(e, 'head_topic')}
              addBlanks={undefined}
              addFile={() => {
                uploadRef.current?.click();
              }}
              // addCase={parameterConfig.target_customer === "tcm" ? () => setCaseVisible(true) : undefined}
              textSetting={{
                max: 5000,
                spaces: true,
                toast: function () {
                  message.info(`题目输入不能超过${(this as any).max}个字`);
                },
              }}
            />
          </div>
          <div className={`enclosure${fileList.length > 0 ? '' : ' hidden'}`}>
            <span className="label">附件列表:</span>
            <Upload {...uploadProps}>
              <Button ref={uploadRef} icon={<PaperClipOutlined />} type="ghost">
                添加附件
              </Button>
            </Upload>
          </div>
        </div>
        {renderQuestionList}
      </div>

      <Modal
        visible={newTopicVisible}
        className={'newtopicmodal'}
        width={535}
        onCancel={() => setNewTopicVisible(false)}
        footer={[
          <Button type="primary" onClick={createTopic}>
            确定
          </Button>,
        ]}
        title="新建题目类型"
      >
        <Radio.Group onChange={onChange} defaultValue={0}>
          {type_enum.map((item: any, index: number) => {
            if (index === 5) {
              return null;
            }
            return <Radio key={item} value={index}>{`${item}题`}</Radio>;
          })}
        </Radio.Group>
      </Modal>
      <PreviewGroupModal
        showPreview={previewShow}
        tableList={[...questionList.current]}
        content={topic}
        onClose={() => setPreviewShow(false)}
        onConfirm={handleSortEnd}
      />
    </div>
  );
};

export default NewQuestionGroup;
