.card_point_item {
  width: 100%;
  height: 175px;
  background: #fff;
  border: 1px solid #f4f4f4;
  border-radius: 5px;
  overflow: hidden;
  .cover_img {
    position: relative;
    width: 100%;
    height: 110px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #000000;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .time_box {
      position: absolute;
      bottom: 0px;
      width: 100%;
      height: 25px;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: flex-end;

      span {
        font-size: 14px;
        color: #fff;
        margin-right: 15px;
      }
    }
  }

  .point_name {
    width: 90%;
    height: 35px;
    display: flex;
    align-items: center;
    margin-left: 5%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;

    .span_name {
      // 超出一行自动省略号
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      margin-left: 10px;
      cursor: pointer;
    }
  }

  .point_link {
    width: 90%;
    height: 25px;
    display: flex;
    align-items: flex-start;
    margin-left: 5%;

    span {
      color: rgba(0, 0, 0, 0.5);
      // 超出一行自动省略号
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      cursor: pointer;
    }
  }
}

.modal-content {
  display: flex;
  .directory {
    width: 200px;
    min-width: 100px;
    border: 1px solid #f0f0f0;
    // height: 680px;
    height: 65vh;
    overflow-y: auto;

    font-size: 14px !important;

    .ant-tree .ant-tree-treenode {
      padding: 10px 0 13px 0;
    }

    .ant-tree.ant-tree-directory
      .ant-tree-treenode
      .ant-tree-node-content-wrapper {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .ant-tree-indent-unit {
      width: 12px;
    }
  }

  .content {
    flex: 1;
    margin-left: 20px;

    .search-group {
      height: 32px;
      display: flex;
      margin-right: 32px;
      .ant-input-group-compact {
        width: 285px;
        margin-right: 20px;
      }

      .ant-picker {
        width: 70%;
        margin: 0 20px;
      }

      .mode-switch-wrapper {
        display: flex;
        font-size: 16px;
        color: #919596;
        margin-left: 24px;

        .mode-item {
          line-height: 32px;
          cursor: pointer;

          &:hover,
          &.active {
            color: var(--primary-color);
          }
        }
      }
    }

    .breadcrumb {
      width: 100%;
      height: 40px;
      padding-top: 10px;
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      padding-left: 10px;
      padding-right: 30px;
      .ant-breadcrumb a {
        // color:rgba(0, 0, 0, 0.85)
        color: var(--primary-color);
      }
      // .ant-breadcrumb a:hover{
      //   color:#eb766c
      // }
      .ant-breadcrumb > span:last-child a {
        color: rgba(0, 0, 0, 0.85);
      }
    }
    .pagination {
      height: 42px;
      padding-top: 10px;
    }

    .list {
      width: 100%;
      overflow: auto;
      height: calc(65vh - 32px - 40px - 42px - 20px);
      margin-top: 20px;
      display: flex;
      justify-content: flex-start;
      // align-items: center;
      flex-wrap: wrap;

      .list-item {
        cursor: pointer;
        // width: 240px;
        max-width: 240px;
        width: 30%;

        height: 168px;
        padding: 6px;
        border: 1px solid rgba(221, 221, 221, 1);
        margin-bottom: 30px;
        margin-right: 15px;

        .img-box {
          width: 100%;
          height: 127px;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: hidden;

          > .ant-skeleton-element {
            width: 100%;
            height: 100%;
          }

          > .ant-skeleton-element {
            .ant-skeleton-image {
              width: 100%;
              height: 100%;
            }
          }

          > img {
            max-width: 100%;
          }

          .img-title {
            width: 100%;
            height: 26px;
            background: rgba(0, 0, 0, 1);
            opacity: 0.4;
            color: #fff;
            text-align: center;
            position: absolute;
            bottom: 0;
            left: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;

            > p {
              margin: 0;
            }

            > p:first-child {
              width: calc(100% - 54px);
            }

            > p:last-child {
              width: 54px;
              height: 100%;
              line-height: 26px;
              background-color: #b63524;
            }
          }
        }

        .item-title {
          display: flex;
          align-items: center;
          padding: 5px 5px 0;
          font-size: 14px;
          // 超出一行自动省略号

          .span_name {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
          }

          .ant-checkbox-wrapper,
          .ant-radio-wrapper {
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .ant-checkbox + span {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
          }
        }
      }

      // .list-item:nth-child(3n) {
      //   margin-right: 0;
      // }
    }
    .res-title {
      cursor: pointer;
    }

    .cover-img {
      max-width: 50px;
      max-height: 30px;
      margin-right: 6px;
    }

    .res-table {
      // width: 800px;
      margin-top: 24px;
      font-size: 14px;
    }

    .ant-pagination {
      text-align: center;
    }
  }
}
@media screen and (min-width: 1400px) {
  .resourceModal {
    // width: 1100px !important;
    width: 1450px !important;
  }
}
@media screen and (max-width: 1400px) {
  .resourceModal {
    // width: 800px !important;
    width: 1400px !important;
  }
}
.resourceModal {
  // max-height: 80% !important;
  // overflow-y: auto;
}
.ant-modal-wrap {
  overflow: unset;
}
.recommend-container {
  width: 25rem;
  height: 65vh;
  border-left: #e5e7eb solid 1px;
  margin-left: 5px;
  padding-left: 10px;
  // display: flex;
  // flex-direction: column;
  .course-img {
    width: 120px;
    height: 80px;
    margin-right: 5px;
    // background-color: #b63524;
  }
  .recommend-item-container {
    display: flex;
    width: 100%;
    border: 1px solid #e5e7eb;
  }
  .course-info {
    display: flex;
    flex-direction: column;
    // justify-content: space-between;
    flex: 1;
    overflow: auto;
  }
  .course-name {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    gap: 2px;
  }
  .course-key-word {
    font-size: 13px;
    margin-top: 5px;
  }
  .course-path {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 13px;
    color: rgba(0, 0, 0, 0.85);
  }
  .recommend-scroll-list {
    width: 100%;
    height: 550px;
    overflow: auto;
  }
  .recommend-course {
    font-weight: bold;
    margin: 0 2px;
  }
  .reco-keywords {
    color: var(--primary-color);
    font-size: 11px;
    background: geekblue;
    padding: 2px;
    border-radius: 4px;
  }
  .recommend-title {
    padding: 5px 0;
    font-size: 15px;
  }
}

.knowlege-container {
  display: flex;
  .knowlege-list {
    // width: -webkit-fill-available;
    width: 100%;
    // width: calc(100% - 25rem);
  }
  .recommend-point-container {
    width: 25rem;
    border-left: #e5e7eb solid 1px;
    margin-left: 5px;
    padding-left: 10px;
    .course-img {
      width: 120px;
      height: 80px;
      margin-right: 5px;
      // background-color: #b63524;
    }
    .recommend-item-container {
      display: flex;
      width: 100%;
      border: 1px solid #e5e7eb;
    }
    .course-info {
      display: flex;
      flex-direction: column;
      // justify-content: space-between;
      flex: 1;
      overflow: auto;
    }
    .course-name {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: flex;
      align-items: center;
      gap: 2px;
    }
    .course-key-word {
      font-size: 13px;
      margin-top: 5px;
    }
    .course-path {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 13px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
