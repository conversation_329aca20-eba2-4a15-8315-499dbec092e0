import examManageApis from '@/api/exam';
import { Editor, IconFont } from '@/components';
import useInitFormValue from '@/pages/NewQuestionGroup/hooks/useInitFormValue';
import examType from '@/types/examType';
import {
  CloseOutlined,
  DeleteOutlined,
  PaperClipOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import type { InputRef } from 'antd';
import {
  AutoComplete,
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  Radio,
  Row,
  Select,
  Space,
  Tag,
  Tooltip,
  TreeSelect,
  Upload,
  message,
} from 'antd';
import debounce from 'lodash/debounce';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { history, useLocation, useSelector } from 'umi';
import { v4 as uuidv4 } from 'uuid';
import { getSensitiveWordPost } from '../../utils';
import CaseModal from '../CaseModal';
import TeacherItem from '../select/teacherItem';
import './index.less';
const { Option } = Select;
const { TextArea } = Input;
interface props {
  opt_type?: any;
  type?: any; //试题类型
  topicId?: any; //试题号
  detail?: any; //试题详情
  selectKeys?: any; //初始分享人
  kecname?: any; //课程名称
}
const TopicCreation: React.FC<props> = (props) => {
  const location: any = useLocation();
  const {
    opt_type,
    type, //试题类型
    topicId, //试题号
    detail, //已分享
    selectKeys,
    kecname,
  } = props;
  const realtype = detail ? detail.questions_type : type;
  const [form] = Form.useForm();
  const [topic, setTopic] = useState<any>(''); //题目
  const [analysis, setAnalysis] = useState<any>(''); //解析
  const [options, setOptions] = useState<any[]>([]); //选项内容
  // 搜索知识点下拉框的数据
  const [knowledgeList, setKnowledgeList] = useState<any>([]);
  // 选择知识节点的值
  const [knowledgeValue, setKnowledgeValue] = useState<any>([]);
  // 知识点选择
  const [knowledgeSelect, setKnowledgeSelect] = useState<any>([]);
  // 搜索项目点下拉框的数据
  const [knowledgeList2, setKnowledgeList2] = useState<any>([]);
  // 选择项目节点的值
  const [knowledgeValue2, setKnowledgeValue2] = useState<any>([]);
  // 项目选择
  const [knowledgeSelect2, setKnowledgeSelect2] = useState<any>([]);
  // 当前选择的参数详情
  const [keyword, setKeyword] = useState<any>([]);
  const [teacherList, setTeacherList] = useState<any>([]);
  let teacherSelect = useRef([]);
  const uploadRef = useRef<any>(null);
  const [page, setPage] = useState<number>(1);
  const [totalPage, setTotalPage] = useState<number>(1);
  const [answer, setAnswer] = useState<any>(undefined);
  const saveflag = useRef<any>(undefined);
  const currentOpt_type = useRef<any>(opt_type);
  const major = useSelector<any, any>((state) => {
    return state.globalData.major;
  });
  const { parameterConfig } = useSelector<any, any>(({ permission }) => permission);
  const [tags, setTags] = useState<string[]>([]);
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [editInputIndex, setEditInputIndex] = useState(-1);
  const [editInputValue, setEditInputValue] = useState('');
  const inputRef = useRef<InputRef>(null);
  const editInputRef = useRef<InputRef>(null);
  const [fileSupport, setFileSupport] = useState<any[]>([
    { name: '', required: false, key: new Date().valueOf() },
  ]);
  const [fileVisible, setFileVisible] = useState<boolean>(true);
  const [fileList, setFileList] = useState<any>([]);

  const [caseVisible, setCaseVisible] = useState<boolean>(false);
  //#region 案例库的关联
  const caseMap = useRef<Map<string, string[]>>(new Map())

  //#region 初始化的部分
  const { initClassify, initQuestionSource } = useInitFormValue();
  //#endregion

  //#region 应用分类
  // 应用分类
  const [applicationSet, setApplicationSet] = useState<any[]>([]);
  // 选择的分类值
  const [classifyName, setClassifyName] = useState('');
  // 选择的方向值
  const [directionName, setDirectionName] = useState('');
  // 题目来源code
  const [questionSourceCode, setQuestionSourceCode] = useState('');
  // 分类列表
  const classifyOptions = useMemo(() => {
    return initClassify.map((item: any) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  }, [initClassify]);
  const [directionOptions, setDirectionOptions] = useState<any>([]);
  useEffect(() => {
    const optList: any = initClassify?.[0]?.children?.map((item: any) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    setDirectionOptions(optList);
  }, [initClassify]);
  // 应用改变
  const handleChangeApplication = (value: string, opt: any) => {
    setApplicationSet(
      opt.map((item: any) => ({
        applicationClassCode: item.value,
        applicationClassName: item.label,
      })),
    );
    const curList =
      initClassify.find((item: any) => item.id === value) ||
      initClassify?.[0] ||
      {};
    setClassifyName(curList?.name || '');
    const optList: any = curList?.children?.map((item: any) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    setDirectionOptions(optList || []);
  };
  //#endregion

  //#endregion
  useEffect(() => {
    let temp: any;
    if (detail) {
      form.setFieldsValue({
        ...detail,
        share_users: detail.share_users ? detail.share_users : [],
        assessmentCode: detail?.assessment?.assessmentCode || '',
        applicationClassCode: detail?.applicationClasses?.map((item: any) => item?.applicationClassCode)
      });
      setApplicationSet(detail?.applicationClasses)
      if (detail.questionCourseList) {
        let arr2 = detail.questionCourseList?.map(
          (item: any) => item.courseId || item.courseName,
        );
        setKnowledgeSelect2(arr2);
        let obj2 = detail.questionCourseList.map((item: any) => {
          return {
            value: item.courseId,
            label: item.courseName,
            title: item.courseName,
            data: item,
          };
        });
        setKnowledgeValue2(obj2);
        setKnowledgeList2(obj2);
      }
      // 课程 
      if (detail.knowledge_points) {
        let arr = detail.knowledge_points.map(
          (item: any) => item.entity_id || item.entity,
        );
        setKnowledgeSelect(arr);
        let obj = detail.knowledge_points.map((item: any) => {
          return {
            value: item.entity_id,
            label: item.entity,
            title: item.mapName + '/' + item.parentNode + '/' + item.entity,
            data: item,
          };
        });
        setKnowledgeValue(obj);
        setKnowledgeList(obj);
      }
      const list_ = detail.fileList?.map((item: any) => {
        return {
          name: item.attachmentName,
          status: 'done',
          size: item.attachmentSize,
          url: item.attachmentSource,
          uid: item.contentId,
        };
      });
      setFileList(list_ || []); //设置回显；
      setTopic(detail.questions_content);
      setAnalysis(detail.questions_analysis);
      setTags(detail.labels || []);
      switch (realtype) {
        case 0:
          temp = detail.questions_answers[0];
          break;
        case 1:
          temp = detail.questions_answers;
          break;
        case 2:
          temp = detail.questions_answers;
          break;
        case 3:
          break;
        case 4:
          temp = detail.questions_answers[0];
          break;
      }
      setAnswer(temp);
      if (realtype === 3) {
        setFileSupport(detail.hasAttachment ?? []);
        setFileVisible(detail.hasAttachment?.length > 0);
      }
    }
    //
    if (detail?.applicationLibMap) {
      const caseItemMap = detail.applicationLibMap
      Object.keys(caseItemMap).map(keyStr => {
        caseMap.current.set(keyStr, caseItemMap[keyStr])
      })
    }
    initOptions(realtype);
    console.log(detail, 'detail22');

  }, [detail]);

  useEffect(() => {
    if (selectKeys?.length > 0) {
      form.setFieldsValue({
        share_users: selectKeys,
      });
    }
  }, [selectKeys]);
  useEffect(() => {
    if (inputVisible) {
      inputRef.current?.focus();
    }
  }, [inputVisible]);
  useEffect(() => {
    if (kecname) {
      var arr = {
        value: kecname,
        label: kecname,
        title: kecname,
      }
      setKnowledgeSelect2(arr);
    }
  }, [kecname]);
  useEffect(() => {
    editInputRef.current?.focus();
  }, [inputValue]);
  const type_: any = examType.optionType_;
  //初始化选项
  const initOptions = (type: any) => {
    if (detail) {
      //这里处理数据是因为模板导入的选项是字幕 ABCD
      const optins = detail?.questions_options?.map(
        (item: any, index: number) => {
          return {
            seq: index + 1,
            content: item.content,
            answerType: item?.answerType || 1,
            answerRange: item?.answerRange || null,
            answerMax: item?.answerMax || null,
            answerMin: item?.answerMin || null,
            disorder: item.disorder,
            uid: uuidv4(),
          };
        },
      );
      setOptions(JSON.parse(JSON.stringify(optins)));
    } else {
      switch (type) {
        case 0:
          setOptions([
            { seq: 1, uid: uuidv4(), content: '' },
            { seq: 2, uid: uuidv4(), content: '' },
            { seq: 3, uid: uuidv4(), content: '' },
            { seq: 4, uid: uuidv4(), content: '' },
          ]);
          break;
        case 1:
          setOptions([
            { seq: 1, uid: uuidv4(), content: '' },
            { seq: 2, uid: uuidv4(), content: '' },
            { seq: 3, uid: uuidv4(), content: '' },
            { seq: 4, uid: uuidv4(), content: '' },
          ]);
          break;
        case 2:
          setOptions([]);
          break;
        case 3:
          setOptions([]);
          break;
        case 4:
          setOptions([
            { seq: 1, content: '正确' },
            { seq: 2, content: '错误' },
          ]);
          break;
        default:
          setOptions([]);
      }
    }
  };
  // 遍历目录树
  const forTree = (tree: any) => {
    return tree?.map((item: any) => {
      return {
        // key: item.categoryCode + ',' + item.categoryName,
        title: item.categoryName,
        value: item.categoryCode + ',' + item.categoryName,
        children: item.children ? forTree(item.children) : [],
      };
    });
  };
  const save = async (flag?: string) => {
    //题目和选项必填

    if (!topic) {
      message.info('题目不能为空');
      return -1;
    }
    if (realtype !== 3) {
      if (!answer) {
        if (realtype === 2) {
          message.info('答案不能为空');
        } else {
          message.info('答案至少勾选一个');
        }
        return -1;
      }

      const emptyFlag =
        realtype == 2
          ? options.some((item: any) => {
            if (item?.answerType === 1 || item?.answerRange === 1) {
              return !item.content;
            }

            return !item?.answerMax || !item?.answerMin;
          })
          : options.some((item: any) => !item.content);

      if (emptyFlag) {
        message.info('答案内容不能为空');
        return -1;
      }
      const errItemIndex = options.findIndex((item: any) => {
        if (item?.answerType === 1 || item?.answerRange === 1) {
          return false;
        }
        if (Number(item.answerMin) >= Number(item.answerMax)) {
          return true;
        }
        return false;
      });

      if (errItemIndex !== -1) {
        message.info(`第${errItemIndex + 1}空答案范围出错`);
        return -1;
      }
    }
    if (
      realtype === 3 &&
      fileSupport.some((item) => item.name === '') &&
      fileVisible
    ) {
      message.warning('请填写要添加的附件名称！');
      return;
    }
    let formdata: any = form.getFieldsValue();
    // 由于后端参数格式固定了 只能前端手动处理

    // return

    let lessonledge: any = [];
    if (knowledgeValue2.length > 0) {
      console.log(knowledgeValue2, 'knowledgeValue2');

      knowledgeValue2.forEach((item: any) => {
        lessonledge.push({
          courseId: item.data.courseId || item.data.course_id,
          courseName: item.data.entity || item.data.courseName || item.data.course_name,
        });
      });
    }
    let knowledge: any = [];

    if (knowledgeValue.length > 0) {
      knowledgeValue.forEach((item: any) => {
        knowledge.push({
          entity: item.data.entity,
          entity_id: item.data.id || null,
          mapId: item.data.mapId || null,
          mapName: item.data.mapName || null,
          nodeId: item.data.nodeId || null,
          parentNode: item.data.parentNode || null,
          propertyValue: item.data.propertyValue || null,
        });
      });
    }

    handleDealCaseId(topic)
    const libMap: any = {}
    caseMap.current.forEach((value, key) => {
      libMap[key] = value
    })
    let params: any = {
      ...formdata,
      applicationLibMap: libMap,
      knowledge_points: knowledge,
      questionCourseList: lessonledge,
      hasAttachment: realtype === 3 ? (fileVisible ? fileSupport : []) : [],
      questions_type: realtype,
      questions_analysis: analysis,
      questions_answers:
        realtype !== 2
          ? Array.isArray(answer)
            ? answer
            : [answer]
          : options.map(
            (item: any) =>
              item.content || `${item?.answerMin}~${item?.answerMax}`,
          ), //试题的正确答案
      questions_content: topic,
      questions_options: options,
      labels: tags,
      fileList: fileList
        .filter((item: any) => item.status == 'done')
        .map((item: any) => {
          return {
            attachmentName: item.name,
            attachmentSize: item.size,
            attachmentSource: item.url || item.response.data.httpPath,
            contentId: item.response?.data?.contentId || item.uid,
          };
        }),
      // 考核方向
      assessment: {
        assessmentCode: formdata.assessmentCode,
        assessmentName: directionName,
      },
      // 应用
      applicationClassCode: '',
      // applicationClassName: classifyName,
      applicationClasses: applicationSet,
      // 题目来源
      questionSourceCode: questionSourceCode,
      questionSourceName: formdata.questionSourceName,
    };

    let res: any;
    let sensitiveWord = JSON.stringify(
      Object.keys(params).map((key: any) => {
        if (key == 'knowledge_points') {
          return params[key].map((item: any) => item.entity);
        } else if (key == 'questionCourseList') {
          return params[key].map((item: any) => item.courseName);
        } else if (key == 'questions_options') {
          return params[key].map((item: any) => item.content);
        } else if (key == 'questions_major') {
          return "";
        } else {
          return params[key];
        }
      }),
    ).replace(RegExp('<.+?>', 'g'), '');
    //第一次保存后 下次不在调保存了
    if (
      saveflag.current ||
      saveflag.current === 0 ||
      (detail && currentOpt_type.current !== 'copy')
    ) {
      res = await getSensitiveWordPost(sensitiveWord, '内容', () =>
        examManageApis.topicUpdate(saveflag.current || detail.id, params),
      );
    } else {
      res = await getSensitiveWordPost(sensitiveWord, '内容', () =>
        examManageApis.addTopic(params),
      );
    }


    if (!res) return; //有敏感词
    if (res.status === 200) {
      message.success('保存成功');
      if (location.query.from === 'out') {
        addTopicBack(res.data, flag == 'back' || location.query.opt_type === "copy");
      }
      if (!!!saveflag.current) {
        saveflag.current = res.data?.id;
      }
      return 0;
    } else {
      message.error('保存失败，请重试');
      return 1;
    }
  };
  const copy = () => {
    save().then((res: any) => {
      if (res === 0) {
        saveflag.current = undefined;
        currentOpt_type.current = 'copy';
        // clean();
        message.success('复制成功，当前为复制的试题，可直接编辑');

        history.push({
          pathname: `/topic/manage`,
          query: {
            opt_type: 'copy',
            detail: detail.id,
          },
        });
      } else if (res === 1) {
        message.error('保存失败，请重试');
      } else {
      }
    });
  };
  //清空题目选项
  const clean = () => {
    cleanEditorContent('topic'); //清空题目；
    cleanEditorContent('analysis'); //清空解析；
    //清空选项
    if ([0, 1].includes(realtype)) {
      //清理单选多选
      setOptions((pre: any) => {
        pre.forEach((item: any) => {
          cleanEditorContent(`options_${item.seq}`);
        });
        const temp = pre.map((item: any) => {
          return {
            seq: item.seq,
            uid: item.uid,
            content: '',
          };
        });

        return temp;
      });
    } else {
      if (realtype === 2) {
        //清空填空选项
        setOptions([]);
      }
      if (realtype === 3) {
        //清空主观题学生上传附件
        setFileSupport([
          { name: '', required: false, key: new Date().valueOf() },
        ]);
      }
    }
    setAnswer(undefined); //置空答案 由于更新key是answer 所以只能最后更新 以便好刷新其余更新后的数据
    setFileList([]);
    setFileVisible(false);
  };
  const create_ = () => {
    //先保存后在继续创建
    save().then((res: any) => {
      if (res === 0) {
        saveflag.current = undefined;
        clean();
      } else if (res === 1) {
        message.error('保存失败，请重试');
      } else {
      }
    });
  };
  const addOptions = () => {
    const temp = JSON.parse(JSON.stringify(options));
    //由于更新延迟一步 只需要判定19即可；
    if (temp.length > 19) {
      message.info('最多可添加20个选项！');
      return;
    }
    temp.push({
      seq: temp.length + 1,
      content: '',
      uid: uuidv4(),
    });
    setOptions(temp);
  };
  const addBlanks = () => {
    setOptions((pre: any) => {
      let temp = JSON.parse(JSON.stringify(pre));
      temp.push({
        seq: temp.length + 1,
        content: '',
        answerType: 1,
        answerRange: null,
        answerMax: null,
        answerMin: null,
        disorder: false,
        uid: uuidv4(),
      });
      return temp;
    });
    (window as any).tinymce.editors['topic'].insertContent(
      `<span>_______</span>`,
    );
  };
  const handleAddCase = (id: string, name: string, caseId: string) => {
    if (caseMap.current.has(caseId)) {
      const caseItem = caseMap.current.get(caseId) || []
      caseItem.push(id)
      const newArr = [...new Set(caseItem)]
      caseMap.current.set(caseId, newArr)
    } else {
      caseMap.current.set(caseId, [id])
    }
    (window as any).tinymce.editors['topic'].insertContent(
      `<a href="/medicalcaselib/#/outPreviewDetail/${id}" target="_blank">${name}（可右键点击查看案例）</a>`,
    );
    setCaseVisible(false);
  }

  const inputChange = (
    e: any,
    itemIndex: number,
    editorName?: string,
    isBlank = false,
  ) => {
    const data = e.level?.content;
    const realData = fetchEditorContent(editorName) ? data : '';
    if (itemIndex < options.length) {
      setOptions((pre: any) => {
        let temp = JSON.parse(JSON.stringify(pre));
        temp[itemIndex].content = realData;
        if (isBlank) {
          setAnswer(temp.map((cell: any) => cell.content));
        }
        return temp;
      });
    }
  };
  //
  const blinkInputChange = (
    e: any,
    uid: string,
    editorName?: string,
    isBlank = false,
  ) => {
    const data = e.level?.content;
    const realData = fetchEditorContent(editorName) ? data : '';
    setOptions((pre: any) => {
      let temp = JSON.parse(JSON.stringify(pre));
      const itemIndex = temp.findIndex((cell: any) => cell.uid === uid);
      if (itemIndex != -1) {
        temp[itemIndex].content = realData;
      }
      if (isBlank) {
        setAnswer(temp.map((cell: any) => cell.content));
      }
      return temp;
    });
  };
  // 将字符中的id给找出来
  const handleDealCaseId = (html_: string) => {
    //正则匹配出字符串
    const regArr: string[] = (html_.match(/href="\/medicalcaselib\/#\/outPreviewDetail\/(\S*)"/g) || []).map(item => {
      const str = item.split('/outPreviewDetail/')[1]
      return str.slice(0, str.length - 1)
    })
    const newCaseArr = [...new Set(regArr)]
    const cloneMap = new Map<string, string[]>()
    //删除： 原来的map对象里的值在不在新的数组里，不在那么就是被删除的
    const reflectObj: any = {}
    caseMap.current.forEach((value, key) => {
      value.forEach(keyStr => {
        reflectObj[keyStr] = key
      })
    })
    newCaseArr.forEach(item => {
      if (cloneMap.has(reflectObj[item])) {
        const caseItemArr = cloneMap.get(reflectObj[item]) || []
        cloneMap.set(reflectObj[item], [...new Set([...caseItemArr, item])])
      } else {
        cloneMap.set(reflectObj[item], [item])
      }
    })
    caseMap.current = cloneMap
  }
  const topicChange = (e: any, item: any) => {
    const data = e.level?.content;
    if (item === 'topic') {
      //通过e.target.content获取的内容可能不是想要的数据，并且直接使用getContent会使图片的src是相对路径，跨项目引用会访问不到,只能手动避开；
      const realTopic = fetchEditorContent('topic') ? data : '';
      if (realTopic === undefined) return; //处理插入化学公式会出现的bug;
      setTopic(realTopic);
    } else {
      const realAnalysis = fetchEditorContent('analysis') ? data : '';
      setAnalysis(realAnalysis);
    }
  };
  const deleteOption = (item: any) => {
    setOptions((pre: any) => {
      const temp = JSON.parse(JSON.stringify(pre));
      temp.splice(item.seq - 1, 1);
      for (let i = item.seq - 1; i < temp.length; i++) {
        temp[i].seq--;
      }
      const last = temp[temp.length - 1];
      //如果删除的答案已经不在剩余选项里了就重置答案
      if (String.fromCharCode(64 + Number(last?.seq)) <= answer) {
        setAnswer(undefined);
      }
      return temp;
    });
  };
  //专业TreeSelect的change函数
  const onProfessionChange = (value: any, label: any, extra: any) => {
    // setMajorName(label);
  };
  // 获取富文本内容
  const fetchEditorContent = (name: any) => {
    const data = (window as any).tinymce.editors[name].getContent();
    return data;
  };
  // 清空富文本内容
  const cleanEditorContent = (name: any) => {
    (window as any).tinymce.editors[name].setContent('');
  };
  //删除富文本
  const handleDelEditor = (name: string) => {
    (window as any).tinymce.editors[name].remove();
  };
  //答案选项
  const answerChange = (e: any, item?: any) => {
    if (realtype === 0 || realtype === 4) {
      //单选题
      setAnswer(e.target.value);
    } else if (realtype === 1) {
      //多选题
      setAnswer(e.length == 0 ? undefined : e);
    } else if (realtype === 2) {
      //填空题

      const data = e.target?.value;

      if (data && item) {
        setOptions((pre: any) => {
          let temp = JSON.parse(JSON.stringify(pre));
          temp[item.seq - 1].content = data;
          setAnswer(temp.map((item: any) => item.content));
          return temp;
        });
      }
    } else {
      //主观题
    }
  };
  // 填空题的改变
  type TChangeType = 'type' | 'numberType' | 'value' | 'disorder'; // 填空类型|数字类型|值
  const handleChangeBlank = (
    e: any,
    item?: any,
    type?: TChangeType,
    key?: 'answerMax' | 'answerMin' | 'content ' | 'disorder',
  ) => {
    let data: any;
    //如果是选择数字和文本的改变
    if (type === 'type') {
      data = e;
      setOptions((pre: any) => {
        const temp = JSON.parse(JSON.stringify(pre));
        temp[item.seq - 1].answerType = data;
        if (data === 2 && !temp[item.seq - 1]?.answerRange) {
          temp[item.seq - 1].answerRange = 1;
        }
        return temp;
      });
      if (data === 2) {
        handleDelEditor(`answers_${item.uid}`);
      }
    }
    //
    if (type === 'numberType') {
      data = e;
      setOptions((pre: any) => {
        const temp = JSON.parse(JSON.stringify(pre));
        temp[item.seq - 1].answerRange = data;
        return temp;
      });
    }

    if (type === 'value' && !!key) {
      data = e.target?.value;
      setOptions((pre: any) => {
        let temp = JSON.parse(JSON.stringify(pre));
        temp[item.seq - 1][key] = data;

        setAnswer(
          temp.map((item: any) => {
            if (item?.answerType === 2 && item?.answerRange === 2) {
              return `${item?.answerMin}~${item?.answerMax}`;
            }
            return item.content;
          }),
        );
        return temp;
      });
    }

    if (type === 'disorder') {
      console.log(e, 'e', item);
      setOptions((pre: any) => {
        const temp = JSON.parse(JSON.stringify(pre));
        temp[item.seq - 1].disorder = e;
        return temp;
      });
    }


  };
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleChangeBlankbox = (value: any) => {
    console.log('111111', value);
    setOptions((pre: any) => {
      return pre.map((item: any) => ({
        ...item,
        disorder: !(item.disorder ?? false)
      }));
    });
  };

  const handleInputConfirm = () => {
    if (inputValue && tags.indexOf(inputValue) === -1) {
      setTags([...tags, inputValue]);
    }
    setInputVisible(false);
    setInputValue('');
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value);
  };

  const handleEditInputConfirm = () => {
    const newTags = [...tags];
    newTags[editInputIndex] = editInputValue;
    setTags(newTags);
    setEditInputIndex(-1);
    setInputValue('');
  };
  const handleClose = (removedTag: string) => {
    const newTags = tags.filter((tag) => tag !== removedTag);

    setTags(newTags);
  };
  const showInput = () => {
    setInputVisible(true);
  };
  const fileNameChange = (e: any, index: number, key: 'name' | 'required') => {
    const arr = [...fileSupport];
    arr[index][key] = String(e.target.value ?? e.target.checked);
    setFileSupport(arr);
  };
  //附件添加
  const uploadProps = {
    name: 'file',
    action: '/rman/v1/upload/reference/material/import',
    headers: {
      authorization: 'authorization-text',
    },
    beforeUpload: async (file: any, list: any) => {
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('上传文件不能超过100M');
      }
      if (fileList.length + 1 > 10) {
        message.info('上传文件数量不能超过10个');
        return false;
      }
      let names = list.map((e: any) => e.name).join('');
      let res: any = await getSensitiveWordPost(
        names,
        '附件名',
        () => true,
        () => false,
      );
      if (!res) return Upload.LIST_IGNORE;
      return isLt100M || Upload.LIST_IGNORE; //隐藏不符合列表的文件
    },
    fileList,
    showUploadList: {
      removeIcon: <CloseOutlined />,
    },
    maxCount: 10,
    onChange(info: any) {
      setFileList(info.fileList);
      if (info.file.status !== 'uploading') {
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败.`);
      }
    },
  };
  const addTopicBack = (data?: any, flag?: boolean) => {
    const data_: any = {
      action: flag ? 'addTopicBack' : 'addTopic',
      data,
    };
    window.parent.postMessage(JSON.stringify(data_), window.location.origin);
  };
  // 检索知识点
  const fetchKnowledgeGraph = debounce((name: any) => {
    examManageApis.fetchKnowledgeGraph(name).then((res: any) => {
      if (res.status == 200) {
        setKnowledgeList(
          res.data.map((item: any) => {
            return {
              value: item.id,
              label: item.entity,
              title: item.mapName + '/' + item.parentNode + '/' + item.entity,
              data: item,
            };
          }),
        );
      }
    });
  }, 200);
  // 添加课程搜索方法（需与后端对接）
  const fetchKnowledgeGraph2 = debounce((name: any) => {
    examManageApis.courseApiGraph(name).then((res: any) => {
      if (res.error_msg == 'Success') {
        setKnowledgeList2(
          res.extend_message.results.map((item: any) => {
            return {
              value: item.course_id,
              label: item.course_name,
              title: item.course_name,
              data: item,
            };
          }),
        );
      }
    });
  }, 200);
  const handlePathBack = () => {
    if (location.query.from === 'out') {
      addTopicBack(null, true);
    } else {
      history.goBack();
    }
  };
  // 填空题
  const selectOptions = [
    { value: 1, label: '文本' },
    { value: 2, label: '数字' },
  ];
  const selectNumberType = [
    { value: 1, label: '唯一答案' },
    { value: 2, label: '范围' },
  ];

  return (
    <div className="topic_create">
      <div className="header">
        <div className="header_left">
          <a
            onClick={() => {
              if (location.query.from === 'out') {
                addTopicBack(null, true);
              } else {
                history.goBack();
              }
            }}
          >
            <span>{'<'}</span>
            <span>返回</span>
          </a>
          <span>{(type_[realtype] || '') + '题'}</span>
        </div>
        <div className="header_right">
          {currentOpt_type.current === 'new' ? (
            <Space>
              <Button type="primary" onClick={() => save('back')}>
                保存
              </Button>
              <Button onClick={create_}>继续创建</Button>
              <Button onClick={handlePathBack}>关闭</Button>
            </Space>
          ) : currentOpt_type.current === 'edit' ? (
            <Space>
              <Button type="primary" onClick={copy}>
                复制
              </Button>
              <Button type="primary" onClick={save}>
                保存
              </Button>
              <Button onClick={handlePathBack}>关闭</Button>
            </Space>
          ) : (
            <Space>
              <Button type="primary" onClick={save}>
                保存
              </Button>
              <Button onClick={handlePathBack}>关闭</Button>
            </Space>
          )}
        </div>
      </div>
      <div className="content">
        <Form
          name="topic_form"
          form={form}
          style={{ width: '100%', height: '100%' }}
          labelAlign="left"
        // initialValues={{
        //   share_users:["cuit001"]
        // }}
        >
          <div className="form_item">
            <div className="form_item_header">
              <span className="tag"></span>
              <span>基本信息</span>
            </div>
            <div className="form_item_body">
              <Row style={{ width: '100%' }}>
                <Col span={11}>
                  <Form.Item name="questionSourceName" label="题目来源">
                    <AutoComplete
                      style={{ width: '100%' }}
                      onChange={(value: any, opt: any) => {
                        setQuestionSourceCode(opt?.labelCode || '');
                      }}
                      options={(initQuestionSource || []).map((item: any) => ({
                        value: item.questionSourceName,
                        label: item.questionSourceName,
                        labelCode: item.questionSourceCode,
                      }))}
                    />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item name="assessmentCode" label="考核方向">
                    <Select
                      style={{ width: '100%' }}
                      options={directionOptions}
                      onChange={(_, opt: any) => {
                        setDirectionName(opt?.label);
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row style={{ width: '100%' }}>
                <Col span={11}>
                  <Form.Item
                    name={'questions_difficulty'}
                    label={'难度'}
                  // rules={[
                  // ]}
                  >
                    <Select>
                      <Option value={1}>1</Option>
                      <Option value={2}>2</Option>
                      <Option value={3}>3</Option>
                      <Option value={4}>4</Option>
                      <Option value={5}>5</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item
                    name={'questions_major'}
                    label="适用院系/部门"
                  // rules={[
                  // ]}
                  >
                    <TreeSelect
                      treeData={forTree(major)}
                      maxTagCount={3}
                      // key={'categoryCode'}
                      onChange={onProfessionChange}
                      treeCheckable={true}
                      placeholder={''}
                      allowClear={true}
                      showSearch
                      treeNodeFilterProp="title"
                      getPopupContainer={(triggerNode) =>
                        triggerNode.parentNode
                      }
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row style={{ width: '100%' }}>
                <Col span={11}>
                  <Form.Item
                    name={'questions_level'}
                    label="适用层次"
                  // rules={[

                  // ]}
                  >
                    <Select mode="multiple">
                      <Option value={'0'}>本科生</Option>
                      <Option value={'1'}>研究生</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={11}>
                  {/* <Form.Item name={'questions_lesson'} label="适用课程">
                    <Input maxLength={50} showCount />
                  </Form.Item> */}
                  <Form.Item label="适用课程">
                    <Select
                      showSearch
                      mode="tags" //multiple
                      value={knowledgeSelect2}
                      placeholder="请输入适用课程"
                      filterOption={false}
                      onSearch={(e: any) => {
                        fetchKnowledgeGraph2(e);
                      }}
                      onChange={(e: any, info: any) => {
                        let arr: any = [];
                        if (info.length) {
                          info.forEach((item: any, index: number) => {
                            if (item.data) {
                              arr.push(item);
                            } else {
                              arr.push({
                                data: {
                                  entity: e[index],
                                },
                                label: e[index],
                                title: e[index],
                                value: null,
                              });
                            }
                          });
                        }
                        setKnowledgeSelect2(e);
                        setKnowledgeValue2(arr);
                      }}
                      notFoundContent={null}
                      options={knowledgeList2}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row style={{ width: '100%' }}>
                <Col span={11}>
                  <TeacherItem
                    multiple={true}
                    required={false}
                    message={''}
                    label={'分享给'}
                    name={'share_users'}
                    // edit={detail?true:false}
                    selectKeys={selectKeys}
                    key={JSON.stringify(selectKeys)}
                    onChange={(e: any) => {
                      debugger;
                    }}
                  />
                </Col>
                <Col span={11}>
                  <Form.Item
                    // name={'labels'}
                    label="标签"
                    // hidden={true}
                    className="tagItem"
                  >
                    {tags?.map((tag, index) => {
                      if (editInputIndex === index) {
                        return (
                          <Input
                            ref={editInputRef}
                            key={tag}
                            size="small"
                            className="tag-input"
                            value={editInputValue}
                            onChange={handleEditInputChange}
                            onBlur={handleEditInputConfirm}
                            onPressEnter={handleEditInputConfirm}
                          />
                        );
                      }
                      const tagElem = (
                        <Tag
                          className="edit-tag"
                          key={tag}
                          // closable={index !== 0}
                          closable={true}
                          onClose={() => handleClose(tag)}
                        >
                          <span
                            onDoubleClick={(e) => {
                              if (index !== 0) {
                                setEditInputIndex(index);
                                setEditInputValue(tag);
                                e.preventDefault();
                              }
                            }}
                          >
                            {tag.length > 20 ? `${tag.slice(0, 20)}...` : tag}
                          </span>
                        </Tag>
                      );
                      return tag.length > 20 ? (
                        <Tooltip title={tag} key={tag}>
                          {tagElem}
                        </Tooltip>
                      ) : (
                        tagElem
                      );
                    })}
                    {inputVisible && (
                      <Input
                        ref={inputRef}
                        type="text"
                        size="small"
                        className="tag-input"
                        value={inputValue}
                        onChange={handleInputChange}
                        onBlur={handleInputConfirm}
                        onPressEnter={handleInputConfirm}
                      />
                    )}
                    {!inputVisible && (
                      <Tag className="site-tag-plus" onClick={showInput}>
                        <PlusOutlined /> 新标签
                      </Tag>
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row style={{ width: '100%' }}>
                <Col span={11}>
                  <Form.Item label="知识点">
                    <Select
                      showSearch
                      mode="tags" //multiple
                      value={knowledgeSelect}
                      placeholder="请输入知识点"
                      // style={props.style}
                      // defaultActiveFirstOption={false}
                      // showArrow={false}
                      filterOption={false}
                      onSearch={(e: any) => {
                        fetchKnowledgeGraph(e);
                      }}
                      onChange={(e: any, info: any) => {
                        let arr: any = [];
                        if (info.length) {
                          info.forEach((item: any, index: number) => {
                            console.log(e, item, '1111111');
                            if (item.data) {
                              arr.push(item);
                            } else {
                              arr.push({
                                data: {
                                  entity: e[index],
                                },
                                label: e[index],
                                title: e[index],
                                value: null,
                              });
                            }
                          });
                        }
                        setKnowledgeSelect(e);
                        setKnowledgeValue(arr);
                      }}
                      notFoundContent={null}
                      options={knowledgeList}
                    />
                  </Form.Item>
                </Col>
                <Col span={11}>
                  <Form.Item label="认知层次" name="cognitive_level">
                    <Select>
                      <Option value="记忆">记忆</Option>
                      <Option value="理解">理解</Option>
                      <Option value="应用">应用</Option>
                      <Option value="分析">分析</Option>
                      <Option value="评价">评价</Option>
                      <Option value="创新">创新</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Row style={{ width: '100%' }}>
                <Col span={11} />
                <Col span={11}>
                  <Form.Item label="应用分类" name="applicationClassCode">
                    <Select
                      options={classifyOptions}
                      style={{ width: '100%' }}
                      onChange={handleChangeApplication}
                      mode='multiple'
                    />
                  </Form.Item>
                </Col>
              </Row>
            </div>
          </div>
          <div className="form_item">
            <div className="form_item_header">
              <span className="tag"></span>
              <span>题目</span>
            </div>
            <div className="form_item_body">
              <Editor
                name={'topic'}
                value={detail?.questions_content}
                onChange={(e: any) => topicChange(e, 'topic')}
                addBlanks={realtype === 2 ? addBlanks : undefined}
                addFile={() => {
                  uploadRef.current?.click();
                }}
                addCase={parameterConfig.target_customer === "tcm" ? () => setCaseVisible(true) : undefined}
                textSetting={{
                  max: 5000,
                  spaces: true,
                  toast: function () {
                    message.info(`题目输入不能超过${this.max}个字`);
                  },
                }}
              />
            </div>
            <div className={`enclosure${fileList.length > 0 ? '' : ' hidden'}`}>
              <span className="label">附件列表:</span>
              <Upload {...uploadProps}>
                <Button
                  ref={uploadRef}
                  icon={<PaperClipOutlined />}
                  type="ghost"
                >
                  添加附件
                </Button>
              </Upload>
            </div>
          </div>
          {Number(realtype) !== 3 && (
            <div className="form_item">
              <div className="form_item_header">
                <span className="tag"></span>
                <span>{realtype === 2 ? '答案' : '选项'}</span>
                {
                  Number(realtype) === 2 && (
                    <span>
                      {options.length > 0 && (
                        <span style={{ paddingLeft: '20px' }} >
                          <Checkbox
                            checked={options.every(item => item.disorder ?? false)}
                            onChange={(e) =>
                              handleChangeBlankbox(options.every(item => item.disorder ?? false))
                            }
                          >
                            答案不计顺序
                          </Checkbox>
                        </span>
                      )}
                    </span>
                  )
                }

                {[0, 1].includes(Number(realtype)) && (
                  <Button
                    icon={<PlusCircleOutlined />}
                    className="opt_box"
                    type={'primary'}
                    onClick={addOptions}
                  >
                    添加选项
                  </Button>
                )}
              </div>
              <div className="form_item_body">
                {Number(realtype) === 0 && (
                  <Radio.Group
                    className="answer_container"
                    onChange={answerChange}
                    value={answer}
                  >
                    {options.map((item: any, index: number) => {
                      return (
                        <div className="answer_list" key={item.seq}>
                          <div>
                            <Radio
                              value={String.fromCharCode(64 + Number(item.seq))}
                            >
                              {String.fromCharCode(64 + Number(item.seq))}
                            </Radio>
                          </div>
                          <Editor
                            name={`options_${index + 1}`}
                            onChange={(e: any) =>
                              inputChange(e, index, `options_${index + 1}`)
                            }
                            height={120}
                            value={item.content}
                            textSetting={{
                              max: 500,
                              spaces: true,
                              toast: function () {
                                message.info(`选项输入不能超过${this.max}个字`);
                              },
                            }}
                          />
                          <Button
                            title="删除此选项"
                            type="link"
                            icon={<IconFont type="iconweiwancheng1" />}
                            onClick={() => deleteOption(item)}
                          ></Button>
                        </div>
                      );
                    })}
                  </Radio.Group>
                )}
                {Number(realtype) === 1 && (
                  <Checkbox.Group
                    className="answer_container"
                    onChange={answerChange}
                    value={answer}
                  >
                    {options.map((item: any, index: number) => {
                      return (
                        <div className="answer_list" key={item.seq}>
                          <div>
                            <Checkbox
                              value={String.fromCharCode(64 + Number(item.seq))}
                            >
                              {String.fromCharCode(64 + Number(item.seq))}
                            </Checkbox>
                          </div>
                          <Editor
                            name={`options_${index + 1}`}
                            height={120}
                            onChange={(e: any) =>
                              inputChange(e, index, `options_${index + 1}`)
                            }
                            value={item.content}
                            textSetting={{
                              max: 500,
                              spaces: true,
                              toast: function () {
                                message.info(`选项输入不能超过${this.max}个字`);
                              },
                            }}
                          />
                          <Button
                            title="删除此选项"
                            type="link"
                            icon={<IconFont type="iconweiwancheng1" />}
                            onClick={() => deleteOption(item)}
                          ></Button>
                        </div>
                      );
                    })}
                  </Checkbox.Group>
                )}
                {Number(realtype) === 2 && (
                  <div className="answer_container">
                    {options.map((item: any, index: number) => {
                      console.log(item, 'item2222');

                      return (
                        <div style={{ position: 'relative', paddingBottom: item?.answerType === 2 ? '30px' : '0px', }} >
                          <div key={item.uid} style={{ position: 'relative' }} className="answer_list">
                            <span style={{ width: '55px' }}>{`第${item.seq}空`}</span>
                            <Select
                              defaultValue={
                                item?.answerType === 2 ? '数字' : '文本'
                              }
                              style={{ width: 120, marginRight: '10px' }}
                              onChange={(value) =>
                                handleChangeBlank(Number(value), item, 'type')
                              }
                              options={selectOptions}
                            />
                            {item?.answerType === 2 ? (
                              <Select
                                defaultValue={
                                  item?.answerRange === 2 ? '范围' : '唯一答案'
                                }
                                style={{ width: 120 }}
                                onChange={(value) =>
                                  handleChangeBlank(
                                    Number(value),
                                    item,
                                    'numberType',
                                  )
                                }
                                options={selectNumberType}
                              />
                            ) : (
                              <Editor
                                name={`answers_${item.uid}`}
                                height={120}
                                onChange={(e: any) =>
                                  blinkInputChange(
                                    e,
                                    item.uid,
                                    `answers_${item.uid}`,
                                    true,
                                  )
                                }
                                disabledImage
                                value={item.content}
                                textSetting={{
                                  max: 500,
                                  spaces: true,
                                  toast: function () {
                                    message.info(
                                      `选项输入不能超过${this.max}个字`,
                                    );
                                  },
                                }}
                              />
                            )}
                            {item?.answerType === 2 &&
                              (item?.answerRange === 2 ? (
                                <>
                                  <Input
                                    type="number"
                                    defaultValue={item?.answerMin}
                                    onChange={(e) =>
                                      handleChangeBlank(
                                        e,
                                        item,
                                        'value',
                                        'answerMin',
                                      )
                                    }
                                  />
                                  ~
                                  <Input
                                    type="number"
                                    defaultValue={item?.answerMax}
                                    onChange={(e) =>
                                      handleChangeBlank(
                                        e,
                                        item,
                                        'value',
                                        'answerMax',
                                      )
                                    }
                                  />
                                  (答案包含最小值和最大值)
                                </>
                              ) : (
                                <Input
                                  defaultValue={item.content}
                                  type="number"
                                  onChange={(e) =>
                                    handleChangeBlank(e, item, 'value', 'content')
                                  }
                                />
                              ))}
                            <Button
                              title="删除此选项"
                              type="link"
                              icon={<IconFont type="iconweiwancheng1" />}
                              onClick={() =>
                                deleteOption(item)
                              }
                              style={{ position: 'absolute', right: '-32px' }}
                            ></Button>
                          </div>
                          <div style={{
                            position: 'absolute',
                            // bottom: '20px',
                            bottom: item?.answerType === 2 ? '10px' : '20px',
                          }} >
                            <Checkbox
                              checked={item.disorder}
                              onChange={(value) =>
                                handleChangeBlank(!(item.disorder ?? false), item, 'disorder')
                              }
                            // defaultValue={item.disorder}
                            >
                              答案不计顺序
                            </Checkbox>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
                {Number(realtype) === 4 && (
                  <Radio.Group
                    className="answer_container"
                    onChange={answerChange}
                    value={answer}
                  >
                    {options.map((item: any, index: number) => {
                      return (
                        <div className="answer_list" key={index}>
                          <div>
                            <Radio
                              value={String.fromCharCode(64 + Number(item.seq))}
                            >
                              {String.fromCharCode(64 + Number(item.seq))}
                            </Radio>
                          </div>
                          <span className="judge">
                            {index === 0 ? '正确' : '错误'}
                          </span>
                        </div>
                      );
                    })}
                  </Radio.Group>
                )}
              </div>
            </div>
          )}
          {Number(realtype) === 3 && (
            <div className="form_item attachment">
              <div className="form_item_header">
                <Checkbox
                  checked={fileVisible}
                  onClick={(e: any) => setFileVisible(e.target.checked)}
                />
                <span>允许学生上传附件</span>
                <Tooltip title="添加附件则代表此道题要求学生上传附件">
                  <QuestionCircleOutlined style={{ marginLeft: '5px' }} />
                </Tooltip>
              </div>
              {fileVisible && (
                <div className="form_item_body">
                  {fileSupport.map((item: any, index) => (
                    <div className="file-support-item" key={item.key}>
                      <div className="file file-name">
                        <span>附件{index + 1}名称：</span>
                        <Input
                          value={item.name}
                          placeholder={`如：实验报告${index + 1}`}
                          onChange={(e) => fileNameChange(e, index, 'name')}
                        />
                      </div>
                      <div className="file file-required">
                        <Checkbox
                          onChange={(e) => fileNameChange(e, index, 'required')}
                          checked={item.required === 'true'}
                        ></Checkbox>
                        <span>必须上传</span>
                      </div>
                      <DeleteOutlined
                        onClick={() =>
                          setFileSupport([
                            ...fileSupport.slice(0, index),
                            ...fileSupport.slice(index + 1),
                          ])
                        }
                      />
                    </div>
                  ))}
                  <Button
                    onClick={() => {
                      if (fileSupport.length === 5) {
                        message.warning('最多只能上传5个附件！');
                        return;
                      }
                      setFileSupport([
                        ...fileSupport,
                        {
                          name: '',
                          required: false,
                          key: new Date().valueOf(),
                        },
                      ]);
                    }}
                  >
                    添加
                  </Button>
                </div>
              )}
            </div>
          )}
          <div className="form_item">
            <div className="form_item_header">
              <span className="tag"></span>
              <span>解析</span>
            </div>
            <div className="form_item_body">
              <Editor
                name={`analysis`}
                value={detail?.questions_analysis}
                height={115}
                onChange={(e: any) => topicChange(e, 'analysis')}
                textSetting={{
                  max: 5000,
                  spaces: true,
                  toast: function () {
                    message.info(`解析输入不能超过${this.max}个字`);
                  },
                }}
              />
            </div>
          </div>
        </Form>
      </div>
      <CaseModal open={caseVisible} onOk={handleAddCase} onClose={() => setCaseVisible(false)} />
    </div>
  );
};
export default TopicCreation;
