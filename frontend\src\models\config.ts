// import { Effect, ImmerReducer, Reducer, Subscription } from 'umi';

import { IReducers } from '@/types/modelsTypes';

export interface IConfig {
  showLoading: boolean;
  leftMenuExpand: boolean;
  mobileFlag: boolean;
}

export default {
  namespace: 'config',
  state: {
    showLoading: false,
    leftMenuExpand: false,
    mobileFlag:false,
  },
  reducers: {
    changeShowLoading: (
      state: IConfig,
      { payload: data }: IReducers<IConfig>,
    ) => {
      return {
        ...state,
        showLoading: data.value,
      };
    },
    updateState: (
      state: IConfig,
      { payload }: IReducers<IConfig>,
    ) => {
      return {
        ...state,
        ...payload
      };
    },
  },
};
