/**
 * 单选题
 */
import { Editor, IconFont } from '@/components';
import Retract from '@/images/icons/retract.png';
import Unfold from '@/images/icons/unfold.png';
import { getSensitiveWordPost } from '@/utils';
import {
  CloseOutlined,
  PaperClipOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import { Button, message, Radio, Upload } from 'antd';
import { FC, memo, useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { IQuestionComponentProp, IQuestionGropItem } from '../../types';
import { fetchEditorContent, formatFileList, initOptions } from '../../utils';
import './index.less';

const SingleChoiceQuestion: FC<IQuestionComponentProp> = memo(
  ({ detail, itemIndex, editType, onChangeItem, onDeleteTopic }) => {
    const [fileList, setFileList] = useState<any>([]);
    const [answer, setAnswer] = useState<any>(undefined);

    const [options, setOptions] = useState<any[]>([]); //选项内容
    const optionRef = useRef<any>([]);
    // 试题是否隐藏
    const [isShowQuestion, setIsShowQuestion] = useState(true);

    const analysisId = useRef('choice_analysis_' + detail.id);
    const topicRef = useRef('chioice_topic_' + detail.id);
    // 小题数据副本
    const initData = useRef(detail);

    // options的修改
    const updateOptions = (value: any) => {
      setOptions(value);
      optionRef.current = value;
      handleUpdate('questions_options', value);
    };

    //更新数据
    const handleUpdate = (type: keyof IQuestionGropItem, value: any) => {
      onChangeItem(type, value, initData.current.id);
    };

    //附件添加
    const uploadProps = {
      name: 'file',
      action: '/rman/v1/upload/reference/material/import',
      headers: {
        authorization: 'authorization-text',
      },
      beforeUpload: async (file: any, list: any) => {
        const isLt100M = file.size / 1024 / 1024 < 100;
        if (!isLt100M) {
          message.error('上传文件不能超过100M');
        }
        if (fileList.length + 1 > 10) {
          message.info('上传文件数量不能超过10个');
          return false;
        }
        let names = list.map((e: any) => e.name).join('');
        let res: any = await getSensitiveWordPost(
          names,
          '附件名',
          () => true,
          () => false,
        );
        if (!res) return Upload.LIST_IGNORE;
        return isLt100M || Upload.LIST_IGNORE; //隐藏不符合列表的文件
      },
      fileList,
      showUploadList: {
        removeIcon: <CloseOutlined />,
      },
      maxCount: 10,
      onChange(info: any) {
        setFileList(info.fileList);
        handleUpdate('fileList', info.fileList);
        if (info.file.status !== 'uploading') {
        }
        if (info.file.status === 'done' && info.file.response?.success) {
          message.success(`${info.file.name} 上传成功`);
        } else if (info.file.status === 'error') {
          message.error(`${info.file.name} 上传失败.`);
        } else if (
          info.file.status === 'done' &&
          !info.file.response?.success
        ) {
          message.error(`${info.file.name} 上传失败,不支持该格式`);
          const cloneList = fileList.map((item: any) => {
            if (item.uid === info.file.uid) {
              return {
                ...item,
                status: 'error',
              };
            }
            return item;
          });
          setFileList(cloneList);
          handleUpdate('fileList', cloneList);
        }
      },
    };

    const uploadRef = useRef<any>(null);
    const topicChange = (e: any, item: string) => {
      const data = e.level?.content;
      if (item === topicRef.current) {
        //通过e.target.content获取的内容可能不是想要的数据，并且直接使用getContent会使图片的src是相对路径，跨项目引用会访问不到,只能手动避开；
        const realTopic = fetchEditorContent(topicRef.current) ? data : '';
        if (realTopic === undefined) return; //处理插入化学公式会出现的bug;
        // setTopic(realTopic);
        handleUpdate('questions_content', realTopic);
      } else {
        const realAnalysis = fetchEditorContent(analysisId.current) ? data : '';
        // setAnalysis(realAnalysis);
        handleUpdate('questions_analysis', realAnalysis);
      }
    };
    // 添加选项
    const addOptions = () => {
      const temp = JSON.parse(JSON.stringify(optionRef.current));
      //由于更新延迟一步 只需要判定19即可；
      if (temp.length > 19) {
        message.info('最多可添加20个选项！');
        return;
      }
      temp.push({
        seq: temp.length + 1,
        content: '',
        uid: uuidv4(),
      });
      updateOptions(temp);
    };

    // 答案更新
    const updateAnswer = (value: any) => {
      setAnswer(value);
      handleUpdate('questions_answers', !value ? undefined : [value]);
    };
    //答案选项
    const answerChange = (e: any, item?: any) => {
      const targetValue = e.target.value;
      updateAnswer(targetValue);
    };
    //   删除选项
    const deleteOption = (item: any) => {
      const temp = JSON.parse(JSON.stringify(optionRef.current));
      temp.splice(item.seq - 1, 1);
      for (let i = item.seq - 1; i < temp.length; i++) {
        temp[i].seq--;
      }
      const last = temp[temp.length - 1];
      //如果删除的答案已经不在剩余选项里了就重置答案
      if (String.fromCharCode(64 + Number(last?.seq)) <= answer) {
        updateAnswer(undefined);
      }
      updateOptions(temp);
    };
    const inputChange = (
      e: any,
      itemIndex: number,
      editorName?: string,
      isBlank = false,
    ) => {
      const data = e.level?.content;
      const realData = fetchEditorContent(editorName) ? data : '';
      if (itemIndex < optionRef.current.length) {
        const temp = JSON.parse(JSON.stringify(optionRef.current));
        temp[itemIndex].content = realData;
        if (isBlank) {
          // setAnswer(temp.map((cell: any) => cell.content));
          updateAnswer(temp.map((cell: any) => cell.content));
        }
        updateOptions(temp);
      }
    };

    useEffect(() => {
      if (editType === 'edit') {
        setAnswer(detail?.questions_answers?.[0]);
        setOptions(detail?.questions_options || []);
        optionRef.current = detail?.questions_options || [];
        setFileList(formatFileList(detail?.fileList || []));
        return;
      }
      initOptions(0, detail, updateOptions);
    }, []);

    useEffect(() => {
      if (detail?.isSort) {
        setAnswer(detail?.questions_answers?.[0]);
        setOptions(detail?.questions_options || []);
        optionRef.current = detail?.questions_options || [];
        setFileList(formatFileList(detail?.fileList || []));
        handleUpdate('isSort', false);
      }
    }, [detail?.isSort]);

    return (
      <div className="single_container">
        <div className="form_item">
          <div className="form_item_header">
            <img
              className="unfold_icon"
              src={isShowQuestion ? Unfold : Retract}
              onClick={() => setIsShowQuestion(!isShowQuestion)}
            />
            <span className="tag"></span>
            <span>小题{itemIndex + 1}</span>
          </div>
          <div className="form_item_body">
            <Editor
              name={topicRef.current}
              value={initData.current?.questions_content}
              onChange={(e: any) => topicChange(e, topicRef.current)}
              addBlanks={undefined}
              addFile={() => {
                uploadRef.current?.click();
              }}
              // addCase={parameterConfig.target_customer === "tcm" ? () => setCaseVisible(true) : undefined}
              textSetting={{
                max: 5000,
                spaces: true,
                toast: function () {
                  message.info(`题目输入不能超过${(this as any).max}个字`);
                },
              }}
            />
            <Button
              title="删除此选项"
              type="link"
              icon={<IconFont type="iconweiwancheng1" />}
              className="del_item"
              onClick={() => onDeleteTopic(initData.current.id)}
            ></Button>
          </div>
          <div className={`enclosure${fileList.length > 0 ? '' : ' hidden'}`}>
            <span className="label">附件列表:</span>
            <Upload {...uploadProps}>
              <Button ref={uploadRef} icon={<PaperClipOutlined />} type="ghost">
                添加附件
              </Button>
            </Upload>
          </div>
        </div>
        {isShowQuestion ? (
          <>
            <div className="form_item">
              <div className="form_item_header">
                <span className="tag"></span>
                <span>选项</span>

                <Button
                  icon={<PlusCircleOutlined />}
                  className="opt_box"
                  type={'primary'}
                  onClick={addOptions}
                >
                  添加选项
                </Button>
              </div>
              <div className="form_item_body">
                <Radio.Group
                  className="answer_container"
                  onChange={answerChange}
                  value={answer}
                >
                  {options.map((item: any, index: number) => {
                    return (
                      <div className="answer_list" key={item.uid}>
                        <div>
                          <Radio
                            value={String.fromCharCode(64 + Number(item.seq))}
                          >
                            {String.fromCharCode(64 + Number(item.seq))}
                          </Radio>
                        </div>
                        <Editor
                          name={`options_${item.uid}`}
                          onChange={(e: any) =>
                            inputChange(e, index, `options_${item.uid}`)
                          }
                          height={120}
                          value={item.content}
                          textSetting={{
                            max: 500,
                            spaces: true,
                            toast: function () {
                              message.info(
                                `选项输入不能超过${(this as any).max}个字`,
                              );
                            },
                          }}
                        />
                        <Button
                          title="删除此选项"
                          type="link"
                          icon={<IconFont type="iconweiwancheng1" />}
                          onClick={() => deleteOption(item)}
                        ></Button>
                      </div>
                    );
                  })}
                </Radio.Group>
              </div>
            </div>
            <div className="form_item">
              <div className="form_item_header">
                <span className="tag"></span>
                <span>解析</span>
              </div>
              <div className="form_item_body">
                <Editor
                  name={analysisId.current}
                  value={initData.current?.questions_analysis}
                  height={115}
                  onChange={(e: any) => topicChange(e, analysisId.current)}
                  textSetting={{
                    max: 5000,
                    spaces: true,
                    toast: function () {
                      message.info(`解析输入不能超过${(this as any).max}个字`);
                    },
                  }}
                />
              </div>
            </div>
          </>
        ) : (
          <div />
        )}
      </div>
    );
  },
);

export default SingleChoiceQuestion;
