import React, { FC, useState } from 'react';
import Video from './video/video';
import Picture from './picture/picture';
import PDF from '@/components/entity/pdf';
// import HTML from '@/components/entity/html';
import EXCEL from '@/components/entity/excel';
// import Error from './error/error';
import './style.less';
// import Audio from './audio/audio';
import { DocumentEditor } from "@onlyoffice/document-editor-react";
import { getDocumentType, getFileNameByPath, getFileTypeByPath, getPathKey, jwtSign } from "./utils";
import { useSelector } from 'umi';
// import useLocale from '@/hooks/useLocale';

const isHtml = (src: string) =>
  src && (src.indexOf('.html') > -1 || src.indexOf('.htm') > -1);


const Entity: FC<Entity.IEntityProps> = ({ type, src, id, knowledge, isAutoplay, finishStatus = true, pip = true, onListener, onUpdate, cover, injectData, userInfo }) => {
  // const { userInfo } = useSelector<any, any>((state: { global: any; }) => state.global);
  const [error, setError] = useState<boolean>(false);
  // const { t } = useLocale();
  console.log(src, 'src');

  const onError = () => {
    setError(true);
  };

  if (error) {
    // return <Error />;
  }
  switch (type) {
    // case 'video':
    //   return <Video src={src} {...(injectData??{})} onError={onError} id={id} isAutoplay={isAutoplay} finishStatus={finishStatus} pip={pip} knowledge={knowledge} onListener={onListener} onUpdate={onUpdate} cover={cover} />;
    // case 'audio':
    //   return <Audio src={src} onError={onError} isAutoplay={isAutoplay} finishStatus={finishStatus} onListener={onListener} />;
    case 'picture':
      return <Picture src={src} onError={onError} onListener={onListener} />;
    case 'document':
      // 判断src是否包含http 没有的话自动加上
      if (src.indexOf('http') === -1) {
        if (process.env.NODE_ENV === 'development') {
          src = `http://**************${src}`;
        } else {
          src = `${location.origin}${src}`;
        }
      }
      console.log('src', src);
      const config: any = {
        document: {
          fileType: getFileTypeByPath(src),
          key: Date.now().toString(),
          title: decodeURIComponent(getFileNameByPath(src)),
          url: src
        },
        documentType: getDocumentType(getFileTypeByPath(src)),
        height: "100%",
        type: "desktop", //desktop mobile embedded
        width: "100%",
        lang: "zh-cn",
        editorConfig: {
          // "coEditing": {
          //     "mode": "fast",
          //     "change": true
          // },
          lang: "zh-cn",
          region: "zh-CN",
          location: "cn",
          mode: "view", //view edit
          user: {
            // "group": "Group1",
            id: userInfo?.loginName ? userInfo?.loginName : '',
            name: userInfo?.nickName ? userInfo?.nickName : ''
          },

          // "plugins": {},
          customization: {
            help: false,
            hideNotes: true,
            comments: true,
            chat: false,
            integrationMode: "embed",
            logo: {
              image: "/documentserver/web-apps/apps/common/main/resources/img/header/header-logo_s.png",
              imageDark: "/documentserver/web-apps/apps/common/main/resources/img/header/dark-logo_s.png",
              url: "http://www.sobey.com"
            },
            uiTheme: "theme-classic-light" //theme-light, theme-classic-light, theme-dark, theme-contrast-dark default-dark, default-light default:theme-classic-light
          }
        }
      };
      config.token = jwtSign(config, "tUVvbKk2mX4T40MBRrtj");

      // return <EXCEL src={src} onError={onError}></EXCEL>
      let documentServerUrl = process.env.NODE_ENV === 'development' ? 'http://**************/documentserver/' : `${location.origin}/documentserver/`;
      console.log('documentServerUrl', documentServerUrl);
      return <div style={{ width: '100%', height: '100%' }}>
        <DocumentEditor
          id="docxEditor"
          documentServerUrl={documentServerUrl}
          config={config}
          events_onAppReady={(e) => {

          }}
          events_onDocumentReady={(e) => {
            // onListener('ended')
          }} />
      </div>;
    default:
      return <div className="entity-error">{"暂不支持此格式文件的预览"}</div>;
  }

};

export default Entity;