import { IconFont } from '@/components/iconFont/iconFont';
import examType from '@/types/examType';
import {
  MenuOutlined,
  PlusCircleOutlined,
  QuestionCircleFilled,
  UndoOutlined,
} from '@ant-design/icons';
import {
  Button,
  Checkbox,
  Form,
  Input,
  message,
  Modal,
  Pagination,
  Popover,
  Select,
  Table
} from 'antd';
import { useEffect, useState } from 'react';
import { history, useSelector } from 'umi';
import './index.less';
// import { filterXSS } from 'xss';
import paperManageApis from '@/api/paper';
import PreviewPaper from '@/components/previewPaper';
import TeacherItem from '@/components/select/teacherItem';
import { IConfig } from '@/models/config';
import { noSupport } from '@/utils';
import {
  SortableHandle
} from 'react-sortable-hoc';
// const xss = require('xss');
// import { IconFont } from '../../components/index';
import dayjs from 'dayjs';
const { Option } = Select;
const DragHandle = SortableHandle(() => (
  <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />
));
function PaperManagement() {
  const [form] = Form.useForm();
  const [shareForm] = Form.useForm();
  const paperEum = examType.paperType; //题目类型
  const [selectRows, setSelectedRows] = useState<any>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [total, setTotal] = useState(0);
  const [paperType, setPaperType] = useState<any>(0);
  const [data, setData] = useState<any>([]);
  const [preObject, setPreObject] = useState<any>(undefined);
  const [editObject, setEditObject] = useState<any>(undefined);
  const [previsible, setPrevisible] = useState<boolean>(false);
  const [paperVisible, setNewPaperVisible] = useState<boolean>(false);
  const [shareModalVisible, setShareModalVisible] = useState<boolean>(false);
  const [paperPrevisible, setPaperPreviewVisible] = useState<boolean>(false);
  const configs: IConfig = useSelector<{ config: any }, IConfig>(
    ({ config }) => config,
  );
  const [checkAll, setCheckAll] = useState<boolean>(false); //全选
  const [indeterminated, setIndeterminated] = useState<boolean>(false);
  const [moreSearch, setMoreSearch] = useState<boolean>(false);
  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  const globalData = useSelector<any, any>((state) => {
    // console.log(state)
    return state.globalData.major;
  });
  const [query, setQuery] = useState<any>({
    paper_name: '', //试卷名称
    paper_type: undefined, // 0：小测验，1：考试，2：练习
    paper_difficulty: undefined,
    state: undefined, //试卷状态 0：完成，1草稿
    use_state: undefined, //0 未使用，1 使用中
    select_type: 1, //查询范围 1全部 2我创建的 3分享的
    page: 1,
    size: 10,
  });
  useEffect(() => {
    // console.log('global', globalData)
  }, []);
  useEffect(() => {
    fetchDataList();
  }, [query]);

  const columns: any = [
    {
      title: '类型',
      // width: '8%',
      dataIndex: 'paper_type',
      key: 'paper_type',
      ellipsis: true,
      render: (item: any, record: any) => <div>{examType.paperType[item]}</div>,
    },
    {
      title: '试卷名',
      // width: '8%',
      dataIndex: 'paper_name',
      key: 'paper_name',
      ellipsis: true,
    },
    {
      title: '难度',
      // width: '15%',
      dataIndex: 'paper_difficulty',
      key: 'paper_difficulty',
      ellipsis: true,
    },
    {
      title: '试题数量',
      // width: '15%',
      dataIndex: 'question_nums',
      key: 'question_nums',
      ellipsis: true,
    },
    {
      title: '分值',
      width: '5%',
      dataIndex: 'paper_marks',
      key: 'paper_marks',
      ellipsis: true,
    },
    {
      title: '创建人',
      // width: '5%',
      dataIndex: 'add_username',
      key: 'add_username',
      ellipsis: true,
    },
    {
      title: '创建时间',
      width: '15%',
      dataIndex: 'create_time',
      key: 'create_time',
      ellipsis: true,
      render: (item: any, record: any) => {
        // 格式化时间
        return dayjs(item).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: '操作',
      key: '操作',
      dataIndex: 'editable',
      // width: '8%',
      align: 'center',
      render: (disable: any, record: any) => (
        <div className="table_opt">
          <Button
            type="link"
            onClick={(e: any) => {
              e.stopPropagation();
              e.preventDefault();
              preview(record);
            }}
            title="预览"
          >
            <IconFont type="iconyulan" />
          </Button>
          {disable && (
            <>
              <Button
                type="link"
                disabled={!disable}
                // onClick={() => edit(record)}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  edit(record);
                }}
                title="编辑"
              >
                <IconFont type="iconbianji-heise" />
              </Button>
            </>
          )}
          {
            <Button
              type="link"
              title="删除"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                batchdelete(record);
              }}
            >
              <IconFont type="iconshanchu-heise-copy" />
            </Button>
          }
        </div>
      ),
    },
  ];
  const rowSelection = {
    type: 'checkbox',
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      setSelectedRowKeys(newSelectedRowKeys.filter(Boolean));
      setSelectedRows(newSelectedRows.filter(Boolean)); //得处理勾选移除后的残余空值对象
    },
    preserveSelectedRowKeys: true,
    selectedRowKeys,
  };
  const preview = async (item: any) => {
    setEditObject(item);
    const res = await paperManageApis.paperDetail(item.id);
    // console.log('fetchDetail',res)
    if (res.status === 200) {
      setPreObject(res.data);
      setPaperPreviewVisible(true);
    } else {
      message.error('预览失败');
      setPaperPreviewVisible(false);
    }
  };
  const paperClose = () => {
    setPaperPreviewVisible(false);
  };
  const edit = (item: any) => {
    if(configs.mobileFlag){
      noSupport();
      return;
    }
    history.push({
      pathname: `/paper/manage`,
      query: {
        opt_type: 'edit',
        detail: item.id,
      },
    });
  };
  const batchdelete = async (item: any) => {
    const temp = (window as any).login_useInfo?.roles?.some((item: any) =>
      ['r_second_manager', 'r_sys_manager', 'admin_S1'].includes(item.roleCode),
    );
    Modal.confirm({
      content: temp
        ? '删除后该试题不可见，使用过该试题的测验仍可查看，确认删除吗？'
        : '确定要删除吗？',
      title: '删除确认',
      icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
      onOk: async () => {
        const data = item
          ? [item.id]
          : selectRows.map((item: any) => item.id);
        const res = await paperManageApis.paperBatchDelete(data);
        if (res.status === 200) {
          message.success('删除成功');
          setSelectedRows([]);
          setSelectedRowKeys([]);
          fetchDataList();
        } else {
          message.error('删除失败');
        }
      },
    });
  };
  const fetchDataList = async () => {
    const res = await paperManageApis.fetchPaperList(query);
    console.log(res);
    if (res.status === 200) {
      setData(res.data?.data);
      setTotal(res.data?.totalCount);
    }
  };
  const newPaper_ = () => {
    if (configs.mobileFlag) {
      noSupport();
      return;
    }
    history.push({
      pathname: `/paper/manage`,
      query: {
        opt_type: 'new',
      },
    });
  };
  const onClose = () => {
    setPrevisible(false);
  };
  const batchimport = () => {
    form?.resetFields();
  };
  const share = () => {
    //试卷分享
    setShareModalVisible(true);
  };
  const handleshare = async () => {
    //试卷分享
    const res = await paperManageApis.paperBatchShare({
      ...shareForm.getFieldsValue(),
      ids: selectRows.map((item: any) => item.id),
    });
    console.log(shareForm.getFieldsValue(), res);
    if (res.status === 200) {
      message.success('分享成功');
      setShareModalVisible(false);
    } else {
      message.error('分享失败');
    }
  };
  const handlecopy = (item?: any) => {
    console.log('handlecopy', item);
    history.push({
      pathname: `/topic/manage`,
      query: {
        opt_type: 'copy',
        detail: item.id,
      },
    });
  };
  const batchhandlecopy = async () => {
    const res: any = await paperManageApis.paperBatchCopy(
      selectRows.map((item: any) => item.id),
    );
    console.log(res);
    if (res.status === 200) {
      message.success('复制成功');
      fetchDataList();
    } else {
      message.error('复制失败，请重试');
    }
  };
  const handlegroup = () => {
    form?.resetFields();
  };
  const resetForm = () => {
    form?.resetFields();
  };
  const handleFormSubmit = () => {
    let values = form.getFieldsValue();
    if (values.search) {
      //移动端
      values = {
        ...values,
        ...values.search,
      };
      delete values.search;
      setMoreSearch(false);
    }
    setQuery({
      ...query,
      ...values,
      page: 1,
    });
  };
  const btnList: any = [];
  
  btnList.push({
    title: '删除试卷',
    func: () => batchdelete(null),
    disabled: selectRows.length === 0,
    dom: <IconFont type="icondelete" />,
  });

  btnList.push({
    title: '复制',
    func: () => batchhandlecopy(),
    disabled: selectRows.length === 0,
    dom: <IconFont type="iconcopy" />,
  });

  btnList.push({
    title: '分享',
    func: () => share(),
    disabled: selectRows.length === 0,
    dom: <IconFont type="iconshare" />,
  });
  const checkAllChange = (e: any) => {
    const check = e.target.checked;
    setCheckAll(check);
    setIndeterminated(false);
    if (check) {
      setSelectedRows(data);
    } else {
      setSelectedRows([]);
    }
  };
  const checkChange = (check: any) => {
    setSelectedRows(check);
    setIndeterminated(!!check.length && check.length < data.length);
    setCheckAll(check.length === data.length);
  };
  return (
    <div
      className={`paper_manage${
        configs.mobileFlag ? ' paper_manage_mobile' : ''
      }`}
    >
      <div className="search_box">
        <Form
          name="paper_form"
          form={form}
          initialValues={{
            search: {
              paper_type: '',
            },
          }}
          onFinish={handleFormSubmit}
        >
          {configs.mobileFlag ? (
            <>
              <Input.Group compact>
                <Form.Item name={['search', 'paper_type']} noStyle>
                  <Select>
                    <Select.Option value={''}>全部</Select.Option>
                    {paperEum.map((item: any, index: number) => (
                      <Select.Option value={index} key={item + index}>
                        {item}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item name={['search', 'paper_name']} noStyle>
                  <Input
                    placeholder="搜索试卷名称"
                    allowClear
                    autoComplete={'off'}
                    addonAfter={
                      <IconFont onClick={handleFormSubmit} type="iconsousuo2" />
                    }
                  />
                </Form.Item>
              </Input.Group>
              <Button
                onClick={() => setMoreSearch(true)}
                icon={<IconFont type="iconshaixuan" />}
              >
                筛选
              </Button>
              <div className={moreSearch ? 'ant-modal-mask' : ''}></div>
              <div className={`moreSearch${moreSearch ? '' : ' none'}`}>
                <div className="head">
                  <span>更多筛选</span>
                  <IconFont
                    type="iconguanbi2"
                    onClick={() => setMoreSearch(false)}
                  />
                </div>
                <Form.Item name={'paper_difficulty'} className="form_select">
                  <Select placeholder="试卷难度">
                    <Select.Option value={1}>1</Select.Option>
                    <Select.Option value={2}>2</Select.Option>
                    <Select.Option value={3}>3</Select.Option>
                    <Select.Option value={4}>4</Select.Option>
                    <Select.Option value={5}>5</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item name={'select_type'} className="form_select">
                  <Select placeholder="创建人">
                    <Option value={1}>全部</Option>
                    <Option value={2}>我创建的</Option>
                    <Option value={3}>分享给我的</Option>
                  </Select>
                </Form.Item>
                <div className="btns">
                  <Button onClick={resetForm}>重置</Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    onClick={handleFormSubmit}
                  >
                    确认
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <>
              <Form.Item name={'paper_name'} className="form_input">
                <Input autoComplete="off" placeholder="搜索试卷名称" />
              </Form.Item>
              <Form.Item name={'paper_type'} className="form_select">
                <Select placeholder="试卷类型">
                  {paperEum.map((item: any, index: number) => (
                    <Select.Option value={index} key={item + index}>
                      {item}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item name={'paper_difficulty'} className="form_select">
                <Select placeholder="试卷难度">
                  <Select.Option value={1}>1</Select.Option>
                  <Select.Option value={2}>2</Select.Option>
                  <Select.Option value={3}>3</Select.Option>
                  <Select.Option value={4}>4</Select.Option>
                  <Select.Option value={5}>5</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item name={'select_type'} className="form_select last">
                <Select placeholder="创建人">
                  <Option value={1}>全部</Option>
                  <Option value={2}>我创建的</Option>
                  <Option value={3}>分享给我的</Option>
                </Select>
              </Form.Item>
              <Button
                onClick={() => {
                  resetForm();
                  form.submit();
                }}
                type="link"
              >
                清空
                <UndoOutlined />
              </Button>
              <Button type="primary" htmlType="submit">
                搜索
                <IconFont type="iconsousuo2" />
              </Button>
            </>
          )}
        </Form>
      </div>
      <div className="split_line"></div>
      <div className="content">
        <div className="opt_btn">
          {configs.mobileFlag && (
            <Checkbox
              checked={checkAll}
              onChange={checkAllChange}
              indeterminate={indeterminated}
            >
              全选
            </Checkbox>
          )}
          <Button type="primary" onClick={newPaper_}>
            <PlusCircleOutlined />
            新建试卷
          </Button>
          {configs.mobileFlag ? (
            <>
              {btnList.length >= 3 ? (
                <>
                  {btnList.slice(0, 1).map((item: any, index: number) => {
                    return (
                      <Button
                        key={index}
                        className={item.disabled ? 'disabled' : ''}
                        onClick={() => {
                          if (!item.disabled) {
                            setOpreatMenuVisible(false);
                            item.func();
                          }
                        }}
                      >
                        {item.dom}
                        {item.title}
                      </Button>
                    );
                  })}
                  {btnList.slice(1, btnList.length).length > 0 && (
                    <Popover
                      placement="bottomLeft"
                      className="mobile_btns_popover"
                      getPopupContainer={(e: any) => e.parentNode}
                      onOpenChange={(newOpen: boolean) =>
                        setOpreatMenuVisible(newOpen)
                      }
                      open={operatMenuVisible}
                      content={
                        <div className="mobile_btns">
                          {btnList
                            .slice(1, btnList.length)
                            .map((item: any, index: number) => {
                              return (
                                <div
                                  key={index}
                                  className={item.disabled ? 'disabled' : ''}
                                  onClick={() => {
                                    if (!item.disabled) {
                                      setOpreatMenuVisible(false);
                                      item.func();
                                    }
                                  }}
                                >
                                  {item.dom}
                                  <span>{item.title}</span>
                                </div>
                              );
                            })}
                        </div>
                      }
                    >
                      <Button
                        onClick={(e: any) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setOpreatMenuVisible(!operatMenuVisible);
                        }}
                      >
                        <IconFont type="iconziyuanku1" />管理
                      </Button>
                    </Popover>
                  )}
                </>
              ) : (
                btnList.map((item: any, index: number) => {
                  return (
                    <Button
                      key={index}
                      className={item.disabled ? 'disabled' : ''}
                      onClick={() => {
                        if (!item.disabled) {
                          setOpreatMenuVisible(false);
                          item.func();
                        }
                      }}
                    >
                      {item.dom}
                    </Button>
                  );
                })
              )}
            </>
          ) : (
            btnList.map((item: any, index: number) => {
              return (
                <div
                  key={index}
                  className={item.disabled ? 'disabled item_' : 'item_'}
                  onClick={() => {
                    if (!item.disabled) {
                      item.func();
                    }
                  }}
                >
                  {item.dom}
                  <span>{item.title}</span>
                </div>
              );
            })
          )}
        </div>
        {configs.mobileFlag ? (
          <>
            <div className="list">
              <Checkbox.Group value={selectRows} onChange={checkChange}>
                {data.map((item: any, index: number) => {
                  return (
                    <div key={item.id} className="item">
                      <div className="head">
                        <Checkbox value={item} />
                        <div className="type">
                          【{examType.paperType[Number(item.paper_type)]}】
                        </div>
                        <div className="topic">{item.paper_name}</div>
                      </div>
                      <div className="info">
                        <div>
                          <div>
                            <label>难度</label>
                            <span>{item.paper_difficulty}</span>
                          </div>
                          <div>
                            <label>试题数量</label>
                            <span>{item.question_nums}</span>
                          </div>
                        </div>
                        <div>
                          <div>
                            <label>分值</label>
                            <span>{item.paper_marks}</span>
                          </div>

                          <div>
                            <label>创建人</label>
                            <span>{item.add_username}</span>
                          </div>
                        </div>
                      </div>
                      <div className="bottom">
                        <Button
                          onClick={() => preview(item)}
                          icon={<IconFont type="iconyulan" />}
                        >
                          预览
                        </Button>
                        <Button
                          onClick={noSupport}
                          icon={<IconFont type="iconbianji-heise" />}
                        >
                          编辑
                        </Button>
                        <Button
                          onClick={() => batchdelete(item)}
                          icon={<IconFont type="iconshanchu-heise-copy" />}
                        >
                          删除
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </Checkbox.Group>
            </div>
            <div className="pagination">
              <Pagination
                showSizeChanger
                total={total}
                current={query.page}
                showQuickJumper
                onChange={(page: number, size: any) =>
                  setQuery({
                    ...query,
                    page,
                    size,
                  })
                }
                showTotal={(total) => `共 ${total} 条`}
                size="small"
              />
            </div>
          </>
        ) : (
          <Table
            onRow={(record, index) => {
              return {
                onClick: () => {
                  console.log('点击行', record, index);
                  preview(record);
                }, // 点击行
                onDoubleClick: (event) => {},
              };
            }}
            dataSource={data}
            rowKey={'id'}
            columns={columns}
            rowSelection={rowSelection as any}
            pagination={{
              position: ['bottomCenter'],
              showSizeChanger: true,
              total: total,
              showQuickJumper: true,
              onChange: (page: number, size: any) =>
                setQuery({
                  ...query,
                  page,
                  size,
                }),
              showTotal: (total) => `共 ${total} 条`,
              size: 'small',
            }}
            scroll={{ y: 'calc(100vh - 340px)' }}
          />
        )}
      </div>
      <Modal
        visible={shareModalVisible}
        className={'shareMoadl'}
        onCancel={() => setShareModalVisible(false)}
        footer={[
          <Button
            type="primary"
            onClick={() => {
              shareForm.submit();
            }}
          >
            确定
          </Button>,
        ]}
        title="分享设置"
      >
        <Form name="shareForm" form={shareForm} onFinish={handleshare}>
          <TeacherItem
            multiple={true}
            required={true}
            message={'请至少勾选一个'}
            label={'分享给'}
            name={'share_users'}
            key="teacher1"
          />
        </Form>
      </Modal>
      <PreviewPaper
        visible={paperPrevisible}
        title={preObject?.paper_name}
        detail={preObject}
        editObject={editObject}
        onClose={paperClose}
        handledit={() => edit(preObject)}
      />
    </div>
  );
}
export default PaperManagement;
