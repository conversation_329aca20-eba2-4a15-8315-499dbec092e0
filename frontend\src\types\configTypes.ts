import { IUser } from "./userTypes";
import { ILang } from "./langTypes";

interface IButtonEnableBtnAndSourceCopyAttr {
    enable: boolean
    name: string
}
interface IButtonEnableBtnAndSourceCopy {
    browse: IButtonEnableBtnAndSourceCopyAttr
    cloud: IButtonEnableBtnAndSourceCopyAttr
    share: IButtonEnableBtnAndSourceCopyAttr
    shareList: IButtonEnableBtnAndSourceCopyAttr
    workspace: IButtonEnableBtnAndSourceCopyAttr
    workteamDetail: IButtonEnableBtnAndSourceCopyAttr
}
interface IButtonEnableBtnAndSource {
    copy: IButtonEnableBtnAndSourceCopy
}
interface IButtonEnable {
    btnAndSource: IButtonEnableBtnAndSource
    copy: boolean
    download: boolean
    move: boolean
    outstore: boolean
}
interface IDeskEnable {
    unreadTip: boolean
}
interface IDohaBase {
    privateName: string
    publicName: string
}
interface IDohaSetting {
    filterEnable: boolean
    searchTypeEnable: boolean
}
interface IDohaUserSortSetting {
    isDesc: boolean
    sortField: string
}
interface IEntityDetail {
    createGif: boolean
    progressBarShowMarkPoint: boolean
}
interface IEntityHistory {
    allowImportUser: boolean
    enable: boolean
}
export interface IEntityType {
    code: string
    enableCatalogue: boolean
    enableExport: boolean
    enableSearchTab: boolean
    extensions: string[]
    isOther: boolean
    keyframe: string
    name: string
    namespace: string
    shortCode: string
    uiProcessor: string
    selected?: boolean
}
interface IEntity {
    detail: IEntityDetail
    editUrl: string
    history: IEntityHistory
    showNullEntity: boolean
    types: IEntityType[]
    viewUrl: string
    documentPostilEnable: boolean
    externalSystemID: string
    tvbSaveSectionEnable: boolean
}
interface IEntityLinkPermission {
    canDelete: boolean
    canExecute: boolean
    canRead: boolean
    canWrite: boolean
}
export interface IEntityLink {
    appPermissionEnable: boolean
    appPermission?: string
    content: string
    enable: string
    entityType: string[]
    hasChildren: boolean
    href: string
    permission: IEntityLinkPermission
    showInDetailPage: boolean
    source: any[]
    sourceEnable: boolean
    target: string
    tooltip: string
    href_?: string
    subChildren?: IEntityLink[]
    class: string
}
export interface IKeyFrameImages {
    code: string
    keyframe: string
    name: string
    type: number
    extensions?: string[]
}
export interface ITopNav {
    appPermissionEnable: boolean
    appPermission?: string
    class: string
    content: string
    enable: boolean
    href: string
    module: string
    target: string
    tooltip: string
}
export interface IUserDropDownList {
    appPermissionEnable: boolean
    content: string
    enable: boolean
    href: string
    isAdmin: boolean
    isSystemAdmin: boolean
    module: string
    target: string
    tooltip: string
}
interface IShare {
    programShareEnable: boolean
    shieldSiteS1: boolean
}
interface IToolBarsImageText {
    enable: boolean
    opLinkCode: string
}
interface IToolBars {
    imageText: IToolBarsImageText
}
interface IUploadConfigExtensionLimit {
    type: number
    extensions: string[]
}
interface IVtubeDownloadConfig {
    address: string
    port: number
    userName: string
    password: string
    path: string
    usedConfig: boolean
    importType: number
}
interface ISearchBaseSubfunctions {
    searchCataEnable: boolean
    searchTemplateEnable: boolean
    hotKeywordEnable: boolean
}
interface ISearchBase {
    defaultViewMode: number
    facetOpenType: number
    exportToExcelMaxLimit: number
    historyKeepCount: number
    subfunctions: ISearchBaseSubfunctions
}
interface IVtubeInfo {
    address: string
    port: number
    userName: string
    password: string
    path: string
    usedConfig: boolean
    importType: number
}
interface IIpsField {
    enable: boolean
    name: string
    key: string
}
export interface IIpsSendBtn {
    enable: boolean
    name: string
    key: string
    icon: string
    checked?: boolean
}
interface IIps {
    sendEnable: boolean
    apiUrl: string
    field: IIpsField[]
    smartTagEnable: boolean
    sendBtn: IIpsSendBtn[]
    jobModel: number
    audioSendEnable: boolean
    vuVisuable: boolean
}
interface ITechaudit {
    btnEnable: boolean
    flowName: string
    appPermission?: any
}
interface ITheme {
    name: string
    loginCss: string
}
interface IShareToContentLibTreeClipsource {
    description: string
    id: number
    name: string
    parentId: number
    siteName: string
    desc: string
}
interface IShareToContentLibTree {
    parentId: string
    count: number
    treeType: number
    name: string
    type: string
    contentId: string
    frameRate: number
    fileExt: string
    isFavorite: boolean
    folderPath: string
    aspect_: number
    formatFlag: number
    isMy: boolean
    dynamicResponse: any
    canRead: boolean
    canWrite: boolean
    canExecute: boolean
    canDelete: boolean
    level: number
    checked: boolean
    expand: boolean
    canCreate: boolean
    selected: boolean
    clipsource: IShareToContentLibTreeClipsource
    appPermission: string
    tool: string
    displayLangText: string
}
interface IShareToContentLib {
    enable: boolean
    text: string
    tree: IShareToContentLibTree[]
    useTimeFolder: boolean
    treeEnable: boolean
    columnEnable: boolean
    clipsourceEnable: boolean
}
interface ISearchType {
    resourceFieldName: string
    name: string
    zh: string
    displayLangText: string
    undefined: string
}
export interface ISearchSortField {
    desc: boolean
    field: string
    name: string
}
// export interface IConfig {
//     isHandleHttpPath: boolean;
//     checkDeletionEnable: boolean
//     server: string
//     searchListTemplate: string
//     searchTableTemplate: string
//     dohaSetting: IDohaSetting
//     customToolBars: any[]
//     siteName: string
//     siteHome: string
//     loginUrl: string
//     globalLogo: string
//     topNav: ITopNav[]
//     footer: string
//     dohaEnable: boolean
//     privateTreeEnable: boolean
//     publicTreeEnable: boolean
//     favoriteEnable: boolean
//     circleEnable: boolean
//     necsEnable: boolean
//     searchEnable: boolean
//     copyrightEnable: boolean
//     copyrightLimitDownloadEnable: boolean
//     copyrightLimitExportEnable: boolean
//     scoreEnable: boolean
//     recycleEnable: boolean
//     statisticEnable: boolean
//     portalEnable: boolean
//     shareEnable: boolean
//     sitesPublicShare: boolean
//     shareExportedEnable: boolean
//     contentLibShare_Enable: boolean
//     autoExportEnable: boolean
//     exportAuditEnable: boolean
//     exportIsColumnAuditEnable: boolean
//     videoClipSavaAsEnable: boolean
//     siteNoticeEnable: boolean
//     chooseBasketEnable: boolean
//     entityTagsEnable: boolean
//     bulkEditEnable: boolean
//     catalogueEnable: boolean
//     searchByImageEnable: boolean
//     searchCataEnable: boolean
//     typeTagEnable: boolean
//     extTagEnable: boolean
//     deskEnable: IDeskEnable
//     vtubeDownloadEnable: boolean
//     techAuditEnable: boolean
//     share: IShare
//     importAuditEnable: boolean
//     intelligentEnable: boolean
//     metadataFieldExtendEnable: boolean
//     samemeaningCorrectEnable: boolean
//     archiveManagerEnable: boolean
//     autoArhciveEnable: boolean
//     archiveFileEnable: boolean
//     toolBarTask: string
//     toolBarTopic: string
//     toolBars: IToolBars
//     uploadConfigExtensionLimit: IUploadConfigExtensionLimit
//     uploadGroupEnable: boolean
//     uploadPicturePackageEnable: boolean
//     userDropDownList: IUserDropDownList[]
//     vtubeUsedSameInfo: boolean
//     vtubeDownloadConfig: IVtubeDownloadConfig
//     vtubeDownloadPath: string
//     searchBase: ISearchBase
//     showRelationScriptFlag: boolean
//     favoriteInterfaceEnable: boolean
//     favoriteInterfaceAddress: string
//     favoriteSaveToYunpan: boolean;
//     entityViewMode: string
//     favoriteTagEnable: boolean
//     buttonEnable: IButtonEnable
//     showHeader: boolean
//     keyFrameImages: IKeyFrameImages[]
//     showNullEntity: boolean
//     webSocketEnable: boolean
//     webSocketPort: number
//     webSocketSslPort: number
//     vtubeInfo: IVtubeInfo
//     vtubeEnable: boolean
//     entityLinks: IEntityLink[]
//     ips: IIps
//     outstoreTitle: string
//     entityTypes: IEntityType[]
//     copyrightExpireDays: number
//     webUploadMd5Enable: boolean
//     webUploadEnable: boolean
//     webUploadThreads: number
//     techaudit: ITechaudit
//     portalScrollCount: number
//     portalScrollTime: number
//     searchResultNum: number
//     cloudSearchResultNum: number
//     workteamEnable: boolean
//     workspaceEnable: boolean
//     dohaBase: IDohaBase
//     storageType: number
//     hiveStorageType: string
//     searchType: ISearchType[]
//     theme: ITheme
//     entity: IEntity
//     shareToContentLib: IShareToContentLib
//     nebulaVip: string
//     systemCode: string
//     defaultSiteCode: string
//     dohaUserSortSetting: IDohaUserSortSetting
//     searchDefaultOrderBy?: string
//     techCensorEnable?: boolean
//     albumEntityCountLimit: number
//     sectionSearchEnable?: boolean
//     facetMultiSelectEnable?: boolean
//     downloadConfig: {
//         downloadAutoSelectOriginalEnable: boolean
//         downloadShowFileNameEnable: boolean
//         externalServerCallStrategy: number
//         externalServers: any[]
//         speedLimit: number
//         useExternalServer: boolean
//     }
//     signalrSwitch?: boolean
//     signalrServerIp?: string
//     customCss?: string
//     searchSortFields: ISearchSortField[]
//     emptyEntityConfig: IEmptyEntityConfig
//     pushPullSwitch?: boolean
//     pictureSearchEnable: boolean
//     smartCatalogueTaskTemplate?: string
//     isNewAddCatalogTask: boolean
//     defaultSearchType: string
//     advanceCheckDefalutCount?: number
// }

export interface IConfig {
    entityTypes: IEntityType[]
    loginUrl: string
    isHandleHttpPath: boolean
    server: string
    webUploadThreads: number
    webUploadMd5Enable: boolean
    ossUpCallbackUrl: string
    vtubeInfo: IVtubeInfo
    vtubeDownloadPath: string
}

export interface IEmptyEntityConfig {
    programCodeLength: number
    /** 季集数占位数 */
    episodesDigit: 0 | 10 | 100 | 1000
    seasonsDigit: 0 | 10 | 100 | 1000
    /** 使用t20特殊处理 */
    useT20: boolean
    getVideocategoryFirstCode?: boolean
}

export interface ILoginCustomImage {
    enable: boolean
    key: string
    path: string
}
export interface ILoginConfig {
    siteName: string
    siteHome: string
    loginUrl: string
    server: string
    footer: string
    custom: {
        title: string
        css: string
        overlayTheme: boolean
        customImage: ILoginCustomImage[]
    }
    theme: {
        name: string
        loginCss: string
    }
    systemCode: string
    returnRelativePathForSBar: boolean
    loadPublicFolderMaterialForSBar: boolean
    sBarExtensionSetting: string
    oaLoginEnable: boolean
    validationCodeIsEnable: boolean
    intelligentEnable: boolean
    facetMultiSelectEnable: string
}

export interface IConfigRes<TConfig> {
    config: TConfig
    currentUser: IUser
    lang: ILang
}

export interface ITableOtherOption {
    name: string
    className: string
    value: string
    valueType?: string  //'html' or 'request' or others
    style: string
}

export interface ITableOption {
    key?: string
    title: string
    dataIndex?: string
    width?: number
    value?: string
    render?: (item: any, record: any, index: number) => JSX.Element | {
        children: JSX.Element
        props: any
    } | null
    fixed?: boolean | "left" | "right" | undefined
    valueType?: "request" | "html" | undefined
}

export interface ISearchDetailOption {
    name: string
    className: string
    value: string
    valueType?: string  //'html' or 'request' or others
    style: string
}

export interface IDefaultSearchType {
    type: string
    resourceName: string
}
