import { cloneDeep } from 'lodash';

export const uniqueByKey = (arr: any[], key: string) => {
  const seen = new Set();
  //   const res: any[] = []
  //   for(const item of arr) {
  //     if(!seen.has(item[key])) {
  //       seen.add(item[key]);
  //       res.push(item);
  //     }
  //   }
  return arr?.filter((item) => {
    const k = item[key];
    return seen.has(k) ? false : seen.add(k);
  });
};

// 将题组的题进行扁平化
export const getFlagQuestion = (data: any[]) => {
  if (data?.length > 0) {
    const res: any[] = [];
    let index = 0;
    data.forEach((item: any) => {
      const examItem: any[] = [];
      item.questions.forEach((cell: any, cell_index: number) => {
        if (cell?.question?.questions_type !== 5) {
          examItem.push({
            ...cell,
            parentIndex: cell_index,
            mainId: cell.question?.id,
            itemIndex: index,
          });
          index++;
        } else {
          const itemList = cell.question?.groupQuestions?.map((cell_1: any) => {
            return {
              ...cell_1,
              // parentIndex: index,
              mainId: cell.question?.id + '_' + cell_1?.id,
              isGroup: true,
              itemIndex: index++,
            };
          });
          examItem.push({
            ...cell,
            parentIndex: cell_index,
            question: { ...cell.question, groupQuestions: itemList },
          });
        }
      });
      res.push({ ...item, questions: examItem });
    });

    return res;
  }
  return [];
};

// 格式化初始题目列表得状态
export const formatQuestionList = (data: any[]) => {
  if (!data?.length) {
    return [];
  }
  return data.map((item: any) => {
    // 非空答案
    const hasAnsweredList = item?.questions?.filter((cell: any) => {
      const preAnswer = cell?.answerQuestion?.[0]?.answer?.split(',');
      // 排除填空题的空答案
      const answerSet = [...new Set(preAnswer)];
      return answerSet?.length > 1 || answerSet?.[0] !== '';
    });
    const answeredIdList = hasAnsweredList?.map(
      (cell: any) => cell?.answerQuestion?.[0]?.partQuestionId,
    );
    // 判断是否有读题配置
    const hasReadTime = item?.readTime?.time > 0;
    // 判断本部分是否有答题倒计时设置
    const hasAnswerTime = item?.answerTime?.time >= 0;
    const questionItem = item?.questions?.map((cell: any) => {
      // 初始状态 1 未读未答 2 已答  3 已读未答
      // 如果没有配置读题时间，那么都初始化未已读未答状态
      const initStatus = hasReadTime ? 1 : 3;
      // 对题组的处理
      if (cell?.question?.questions_type === 5) {
        // 题组的答案列表 | 过滤出有答案的小题，并获取小题的id
        const groupAnswerIds =
          cell?.answerQuestion
            ?.filter(
              (cell_1: any) =>
                cell_1?.answer && cell_1?.answer?.split(',')?.length > 0,
            )
            ?.map((cell_1: any) => cell_1?.questionId) || [];
        const groupItem = cell?.question?.groupQuestions?.map((cell_1: any) => {
          const isAnswered = groupAnswerIds.includes(String(cell_1?.id));
          return {
            ...cell_1,
            status: isAnswered ? 2 : initStatus,
          };
        });
        return {
          ...cell,
          // status: isAnswered? 2 : 1,
          question: { ...cell.question, groupQuestions: groupItem },
          status: groupAnswerIds.length > 0 ? 2 : initStatus,
          hasAnswerTime,
        };
      }
      // 非题组
      const isAnswered = answeredIdList.includes(cell?.question?.id);
      return {
        ...cell,
        status: isAnswered ? 2 : initStatus,
        hasAnswerTime,
      };
    });
    return {
      ...item,
      questions: questionItem,
    };
  });
};

//   更改题目状态
export const getChangeItemStatusList = (
  list: any[],
  curInfo: any,
  answerList?: any,
) => {
  // 1、如果有答案，那么这个题标记为已做 status->2，2、如果没有答案，那么这个题标记为已读未做 status->3
  const tempList = cloneDeep(list);
  let statusNew = 2;
  // 判断是否有答案
  const hasAnswer = answerList?.filter((item: any) => !!item);
  if (!hasAnswer?.length) {
    statusNew = 3;
  }
  tempList[curInfo.partIndex].questions[curInfo.questionIndex].status =
    statusNew;
  if (curInfo?.groupItemId) {
    // 题组提交的时候可能已经做了几个小题
    // 找到已经作答的小题
    const groupItemIds = answerList?.map((cell: any) =>
      String(cell?.questionId),
    );

    // 寻找题组的小题,修改状态
    const groupItemNewArr = tempList[curInfo?.partIndex]?.questions[
      curInfo?.questionIndex
    ]?.question?.groupQuestions?.map((cell: any) => {
      if (groupItemIds?.includes(String(cell?.id))) {
        return { ...cell, status: statusNew };
      }
      return cell;
    });
    tempList[curInfo?.partIndex].questions[
      curInfo?.questionIndex
    ].question.groupQuestions = groupItemNewArr;
  }
  return tempList;
};

// 获取继续考试的题目的初始答案
export const getContinueExamInitAnswer = (questionItem: any) => {
  // 题组的答案获取
  if (questionItem?.question?.questions_type === 5) {
    return questionItem?.answerQuestion?.map((cell: any) => {
      return {
        questionId: String(cell.questionId),
        answer: cell.answer?.split(','),
      };
    });
  }
  // 普通题的答案获取
  return questionItem?.answerQuestion?.[0]?.answer?.split(',') || []
};
