import * as docx from 'docx-preview';
import { FC, useEffect, useRef } from 'react';
import styles from './index.less';

const FilePreview: FC<any> = ({ url }) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await fetch(url);
        const arrayBuffer = await res.arrayBuffer();
        const blob = new Blob([arrayBuffer]);
        const containerEle = ref.current;
        if (containerEle) {
          docx
            .renderAsync(blob, containerEle, undefined, {
              className: 'docx-wrapper-container',
            })
            .then((res) => {
              console.log(res);
            });
        }
      } catch (e) {
        console.log(e);
      }
      // docx.preview(blob, ref.current);
    };
    fetchData();
  }, [url]);

  return <div ref={ref} className={styles.wrapper} id="word-container"></div>;
};

export default FilePreview;
