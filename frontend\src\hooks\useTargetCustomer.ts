
import { useSelector } from 'umi';
export function useTargetCustomer() {
  const { parameterConfig } = useSelector<
    { global: any },
    {
      buttonPermission: string[];
      parameterConfig: any;
      permission: any;
    }
  >((state) => state.global);
  return TargetCustomer.SJ_TU;
  return parameterConfig.target_customer as TargetCustomer;
}

export enum TargetCustomer {
  NPU = 'npu',
  TCM = 'tcm',
  PP_SUC = 'ppsuc',
  YNU = 'ynu',
  SH_TECH = 'shangHaiTech',
  SJ_TU = 'sjtu',
}
