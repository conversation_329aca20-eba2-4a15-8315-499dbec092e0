/*
 * @Author: 李晋
 * @Date: 2021-09-11 12:34:15
 * @Email: <EMAIL>
 * @LastEditors: OBKoro1
 * @LastEditTime: 2021-09-11 12:40:06
 * @Description: file information
 * @Company: Sobey
 */

import http from '@/http/http';

export namespace subInfoApis {
  /**
   *
   *
   * @param {string[]} data 用户code[]
   */
  export const selectUsers = (data: string[]) =>
    http(`/rman/v1/3rd/user/info`, {
      method: 'POST',
      data: {
        ids: data,
      },
    });
}
