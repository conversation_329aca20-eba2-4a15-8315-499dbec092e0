.report_detail_page_container {
  width: 100vw;
  height: 100vh;
  background: #f7f9fa;

  .header {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    background: #fff;
    .header_name {
      flex: 1;
      height: 100%;
      text-align: center;
      line-height: 60px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 22px;
      color: #282c3c;
    }
    .collect_btn {
      width: 380px;
      height: 100%;
      display: flex;
      justify-content: end;
      align-items: center;
      //   text-align: right;
      .btn {
        padding: 7px 20px;
        background: #549cff;
        border-radius: 4px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
      }
    }
  }
  .question_content::-webkit-scrollbar,
  .question_order::-webkit-scrollbar {
    display: none;
  }
  .content_container {
    width: 100%;
    height: calc(100vh - 60px);
    padding: 20px;
    display: flex;
    gap: 20px;
    .question_detail {
      flex: 1;
      height: 100%;
      background: #fff;
      border-radius: 10px;
      display: flex;
      justify-content: center;
      padding: 20px 0;
      .question_content {
        height: 100%;
        width: 80%;
        overflow: auto;
        .auto-img {
          width: 100%;
          overflow: auto;
        }

        
        .analysis_content {
          margin-top: 20px;
          width: 100%;
          background: #f7f9fa;
          border-radius: 4px;
          padding: 10px;
          .top_answer {
            display: flex;
            gap: 40px;
            .analysis_answer {
              font-family: PingFangSC, PingFang SC;
              font-weight: 600;
              font-size: 14px;
              color: #2a2a2a;
              .answer_text {
                color: #549cff;
              }
            }
          }
          .analysis_title {
            margin-top: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 14px;
            color: #525252;
          }
        }

        // .answer_content {
        //   display: flex;
        //   flex-direction: column;
        // }
        .answer-item {
          display: flex;
          align-items: flex-start;
        }
      }
    }
    .question_order {
      width: 360px;
      height: 100%;
      background: #fff;
      border-radius: 10px;
      padding: 20px;
      overflow: auto;
      display: flex;
      flex-direction: column;
      gap: 30px;
      .part_container {
        width: 100%;
        .part_container {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #2a2a2a;
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .line {
          width: 100%;
          height: 1px;
          border: 1px dashed #c5c5c5;
          margin-top: 20px;
        }
        .order_container {
          width: 100%;
          display: flex;
          gap: 20px;
          flex-wrap: wrap;
          margin-top: 20px;
          .order_item {
            width: 36px;
            height: 36px;
            background: #ffffff;
            border-radius: 6px;
            border: 1px solid #c5c5c5;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 18px;
            color: #525252;
            cursor: pointer;
          }
          .correct_item {
            background: rgba(23, 193, 100, 0.1);
            border: 1px solid #17c164;
          }
          .wrong_item {
            background: rgba(255, 97, 97, 0.11);
            border: 1px solid #ff6161;
          }
          .checked_item {
            background: #549cff;
            border: 1px solid #549cff;
            color: #fff;
          }
        }
      }
    }
  }
}
