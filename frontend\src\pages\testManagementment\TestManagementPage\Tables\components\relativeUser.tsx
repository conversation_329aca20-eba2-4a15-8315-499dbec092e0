import train from '@/api/train'

import { TeamOutlined } from '@ant-design/icons';
import { Button, Form, Input, Table, Tag, Tree } from 'antd';
import React, {
  FC,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import './index.less';

interface ITreeItemProps {
  list: any
  code: string;
  title: string;
  key: string;
  children?: Array<ITreeItemProps>;
  icon?: React.ReactNode;
  parentName?: string;
  parentId?: number;
  parentCode?: string;
  studentCode?: any;
}

interface IRelativeUserProps {
  onOk: () => void;
  roleCode: string;
  insider: string[];
  selectinfo?: any; //默认选中的用户
  isaudit?: boolean; //是否是审核配置
  selectonchange?: Function;
  iszhuanti?: boolean; //是否是专题配置
  hideuser?: string; //隐藏的用户
  list?: any[]; // 新增：选中的数据
}

const { Search } = Input;

let RelativeUser: FC<any> = (props: IRelativeUserProps, ref) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [current, setCurrent] = useState<number>(1);
  const [keyword, setKeyword] = useState<string>('');
  const [total, setTotal] = useState<number>(0);
  const [userList, setUserList] = useState<Array<any>>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Array<any>>([]);
  const [initialRowKeys, setInitialRowKeys] = useState<Array<any>>([]);
  const [treeData, setTreeData] = useState<Array<ITreeItemProps>>([]);
  const [rootKeys, setRootKeys] = useState<Array<string>>([]);
  const [selectCode, setSelectCode] = useState<string>('');
  const [account, setAccount] = useState<string>('');
  const [accountshow, setAccountshow] = useState<String>('工号/学号/ID');
  const [loadedOrgCode, setLoadedOrgCode] = useState<string[]>([]);
  const [searchform] = Form.useForm();
  const [loaselectinfo, setselectinfo] = useState({});
  let {
    selectinfo = [],
    isaudit = false,
    selectonchange = Function,
    iszhuanti = false,
    hideuser = '',
    roleCode,
    list = []
  } = props;

  // 处理list为选中回显
  useEffect(() => {
    // 只在组件初始化时处理一次
    if (Array.isArray(list) && list.length > 0) {
      // list: [{id, phone, name, code, org: {edu}}]
      // 选中key为code
      const keys = list.map(item => item.code).filter(Boolean);
      setSelectedRowKeys(keys);
      setInitialRowKeys(keys);
    }
  }, [list]);

  useEffect(() => {
    getRoot(props.roleCode);
  }, []);
  useEffect(() => {
    if (props.studentCode && Array.isArray(props.studentCode) && props.studentCode.length > 0) {
      const codes = props.studentCode.map((item: any) => item.code).filter(Boolean);
      setSelectedRowKeys(codes);
      console.log(codes, 'codes');

    }
  }, []);

  useEffect(() => {
    const temp: any = window.localStorage.getItem('upform_platform');
    if (temp === 'ILA') {
      setAccountshow('ID');
    } else if (temp === 'Lark') {
      // setPlatformname('Lark')
    } else {
      setAccountshow('工号/学号/ID');
    }
  }, []);

  useEffect(() => {
    if (isaudit) {
      if (selectinfo.length) {
        let arr = selectinfo.map((item: any) => item.code || item);
        arr = arr.filter((item: any) => item);
        arr = Array.from(new Set(arr));
        setSelectedRowKeys(arr);
        setInitialRowKeys(arr);
      } else {
        setSelectedRowKeys([]);
        setInitialRowKeys([]);
      }
    }
  }, [selectinfo, isaudit]);

  useEffect(() => {
    if (selectCode) {
      fetchAllUser();
    }
  }, [current, keyword, selectCode, account]);

  const getRoot = async (roleCode: any) => {
    const res = await train.rootOrganization(roleCode);
    if (res && res.errorCode === 'success') {
      const rootData = res.extendMessage.map((item: any) => {
        return {
          title: item.organizationName,
          key: item.id,
          code: item.organizationCode,
        };
      });
      setTreeData(rootData);
      setSelectCode(rootData[0].code);
      if (rootData.length) {
        onLoadChild(rootData[0], true);
      }
    }
  };
  // 侧边栏
  const onLoadChild = (node: any, isRoot: boolean = false): Promise<any> => {
    const { key, children, code, title } = node;
    return new Promise(async resolve => {
      if (children) {
        resolve(null);
        return;
      }

      function updateTreeData(
        list: ITreeItemProps[],
        key: React.Key,
        children: ITreeItemProps[],
      ): ITreeItemProps[] {
        return list.map(node => {
          if (node.key === key) {
            return {
              ...node,
              children,
            };
          } else if (node.children) {
            return {
              ...node,
              children: updateTreeData(node.children, key, children),
            };
          }
          return node;
        });
      }

      const res = await train.childOrganization(code);
      if (res && res.errorCode === 'success') {
        setTreeData(origin =>
          updateTreeData(
            origin,
            key,
            res.extendMessage.map((item: any) => {
              return {
                title: item.organizationName,
                key: item.id,
                code: item.organizationCode,
                parentName: title,
                parentId: key,
                parentCode: code,
              };
            }),
          ),
        );
        if (isRoot) {
          setRootKeys([key]);
        }
        resolve(null);
      }
    });
  };

  const bindUpdate = async () => {
    props.onOk();
  };

  useImperativeHandle(
    ref,
    () => ({
      getLoaSelectInfo: () => loaselectinfo, // 获取 loaselectinfo 的方法
      bindUpdate,
    }),
    [bindUpdate],
  );

  // 获取列表
  const fetchAllUser = async () => {
    setLoading(true);
    let param: any = {
      organizationCode: selectCode,
      excludeRoles: hideuser,
      keyword,
      page: current,
      size: 10,
      roleCode: roleCode,
      // include_roles: true,
      isIncludeSubUser: true,
    };
    if (account) {
      param = {
        organizationCode: selectCode,
        excludeRoles: hideuser,
        extend: {
          account,
        },
        keyword,
        page: current,
        size: 10,
        // include_roles: true,
        IsIncludeSubUser: true,
      };
    }
    const res = await train.getAllUserByOrg(param);

    if (res && res.errorCode === 'success') {
      let data = res.extendMessage.results;
      // 不知道为啥要判断当前选择的节点  暂时删除
      // if (!loadedOrgCode.includes(selectCode)) {
      setLoadedOrgCode(origin => [...origin, selectCode]);

      data.map((item: any) => {
        item.sexshow = item.extend?.sex;
        item.accountshow = item.extend?.account;
        item.rolelist = item.roles;
        return item;
      });

      // 处理回显：如果list有，优先用list的code作为选中
      let selectedKeys = selectedRowKeys;
      if (Array.isArray(list) && list.length > 0) {
        const listCodes = list.map(item => item.code).filter(Boolean);
        // 只选当前页有的
        const userCodes = data.map(item => item.user_code);
        // 只勾选当前页中属于list的
        selectedKeys = [
          ...new Set([
            ...selectedRowKeys,
            ...listCodes.filter(code => userCodes.includes(code)),
          ]),
        ];
        setSelectedRowKeys(selectedKeys);
        setInitialRowKeys(selectedKeys);
      }

      setUserList(data);
      setTotal(res.extendMessage.recordTotal);
    }
    setLoading(false);
  };

  // 检索
  const searchRole = () => {
    if (
      keyword === searchform.getFieldsValue().keyword &&
      account === searchform.getFieldsValue().account &&
      current === 1
    ) {
      fetchAllUser();
    } else {
      setKeyword(searchform.getFieldsValue().keyword);
      setAccount(searchform.getFieldsValue().account);
      setCurrent(1);
    }
  };

  // 切换页码
  const handleTableChnage = (params: any) => {
    setCurrent(params.current);
  };

  // 目录树选择
  const onSelect = (selectedKeys: any, e: any) => {
    setSelectCode(e.node.code);
  };

  const pagination = {
    total,
    current,
    showSizeChanger: false,
  };

  const rowSelection = {
    onChange: (newSelectedRowKeys: Array<any>, seletrow: any) => {
      // console.log(newSelectedRowKeys, 'newSelectedRowKeys',);

      let validRows = seletrow.filter(Boolean); // 过滤掉 undefined

      let newSelectInfo = {
        roleCode: roleCode,
        data: validRows
      };

      // 找出 newSelectedRowKeys 中没有出现在 newSelectInfo.data 里的 user_code
      const dataUserCodes = (newSelectInfo?.data || []).map((item: any) => item?.user_code);
      const missingCodes = newSelectedRowKeys?.filter(
        (code: string) => !dataUserCodes?.includes(code)
      );

      // 从 props.studentCode 里找出 code 匹配 missingCodes 的项
      let missingItems: any[] = [];
      if (Array.isArray(props?.studentCode) && missingCodes.length > 0) {
        missingItems = props?.studentCode.filter((item: any) =>
          missingCodes.includes(item?.code)
        );
      }

      // 合并 validRows 和 missingItems，避免重复
      const mergedData = [
        ...validRows,
        ...missingItems?.filter(
          (item: any) => !validRows.some((row: any) => row?.user_code === item?.code)
        ).map((item: any) => ({
          ...item,
          user_code: item?.code, // 保证结构一致
          nick_name: item?.name
        }))
      ];
      newSelectInfo.data = mergedData;
      setselectinfo(newSelectInfo);
      // console.log('newSelectedRowKeys', roleCode, newSelectInfo);
      if (isaudit) {
        const addKey: any = newSelectedRowKeys.filter(
          (code: string) => selectedRowKeys.indexOf(code) < 0,
        );
        const deleteKey: any = selectedRowKeys.filter(
          (code: string) => newSelectedRowKeys.indexOf(code) < 0,
        );
        //选中的数据
        if (addKey.length) {
          let newaddarr = validRows.filter((item: any) => {
            if (item) {
              return addKey.includes(item.user_code);
            } else {
              return false;
            }
          });
          let addarr: any = [];

          newaddarr.forEach((item: any) => {
            addarr.push({
              nick_name: item.nick_name,
              user_code: item.user_code,
            });
          });
          selectinfo.forEach((item: any) => {
            if (item.code) {
              addarr.push({
                nick_name: item.name,
                user_code: item.code,
              });
            }
          });
          selectonchange(addKey, addarr, '1');
        } else {
          selectonchange(deleteKey, validRows, '2');
        }
      }
      setSelectedRowKeys(newSelectedRowKeys);
    },
    preserveSelectedRowKeys: true,
    selectedRowKeys,
    getCheckboxProps: (record: any) => ({
      disabled: props.insider?.includes(record.user_code),
    }),


  };
  const columns: any = [
    {
      title: '姓名',
      dataIndex: 'nick_name',
    },
    {
      title: '性别',
      dataIndex: 'sexshow',
    },
    {
      title: `${accountshow}`,
      dataIndex: 'accountshow',
    },
    {
      title: '角色',
      dataIndex: 'rolelist',
      align: 'center',
      render: (text: any, record: any) =>
        text && text.map((item: any) => <Tag>{item.name}</Tag>),
    },
    // {
    //   title: '是否禁用',
    //   key: 'disabled',
    //   dataIndex: 'disabled',
    //   render: (disabled: number) => {
    //     const color = !disabled ? 'green' : 'geekblue';
    //     return (
    //       <Tag color={color}>{!disabled ? '否' : '是'}</Tag>
    //     );
    //   },
    // }
  ];
  return (
    <div className="relative-user">
      <div className="tree">

        {!!rootKeys.length && (
          <Tree
            icon={<TeamOutlined />}
            defaultExpandedKeys={rootKeys}
            defaultSelectedKeys={[rootKeys[0]]}
            loadData={onLoadChild}
            treeData={treeData}
            onSelect={onSelect}
          />
        )}
      </div>
      <div className="content">
        <div className="search_role">
          <div className="__search_box">
            <Form
              layout={'inline'}
              // labelCol={{ span: 5 }}
              form={searchform}
            >
              {/* <Form.Item
                label="工号/学号"
                name="account"
              >
                <Input autoComplete="off" placeholder='请输入工号/学号' />
              </Form.Item> */}
              <Form.Item style={{ marginBottom: '10px' }} label={'姓名'} name="keyword">
                <Input
                  autoComplete="off"
                  placeholder={'请输入姓名'}
                  allowClear
                  onPressEnter={searchRole}
                />
              </Form.Item>
              <Form.Item>
                <Button type="primary" onClick={searchRole}>
                  {'检索'}
                </Button>
              </Form.Item>
            </Form>
            {/* <Search
              placeholder="角色名称"
              onSearch={searchRole} /> */}
          </div>
        </div>
        <Table
          loading={loading}
          dataSource={userList}
          columns={columns}
          rowKey="user_code"
          size="small"
          rowSelection={{
            ...rowSelection,
          }}
          pagination={{
            ...pagination,
          }}
          onChange={handleTableChnage}
        />
      </div>
    </div>
  );
};

export default forwardRef<any, IRelativeUserProps>(RelativeUser as any);
