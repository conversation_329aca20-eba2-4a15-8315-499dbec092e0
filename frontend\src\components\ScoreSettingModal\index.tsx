import React, { FC, useState } from 'react';
import { Table, Input, Button, Modal, message } from 'antd';
import examType from '@/types/examType';
import './index.less'
import { MinusSquareOutlined, PlusSquareOutlined } from '@ant-design/icons';
import { useEffect } from 'react';
import { createMarkup } from '@/utils';
import RenderHtml from '../renderHtml';
interface params {
    title: any,
    tempData?: any,
    visible: boolean,
    callback: (data: any) => void,
    onclose: (data: any) => void,
}
const ScoreSettingModal: React.FC<params> = (params) => {
    const { title, tempData, visible, callback, onclose } = params
    const [check, setCheck] = useState<boolean>(true);
    const [data, setData] = useState<any>([ //模拟数据结构
        {
            type: 0,
            point_set: undefined,
            expand: false,
            children: [
                {
                    questions_content: '1',
                    points: 0
                }
            ]
        },
        {
            type: 1,
            point_set: undefined,
            expand: false,
            children: [
                {
                    questions_content: '2',
                    points: 0
                }
            ]
        }
    ]);

    const removeMathML = (html: string): string => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        // 移除数学公式相关的元素
        const mathElements = doc.querySelectorAll('.math-tex, mjx-container');
        mathElements.forEach((el) => el.remove());
        return doc.body.textContent || '';  // 获取去除数学公式后的纯文本内容
    };
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            width: '20%',
            ellipsis: true,
            render: (item: any, record: any, index: number) => (
                <div>{index + 1}</div>
            )
        },
        {
            title: '题目内容',
            dataIndex: 'questions_content',
            width: '60%',
            key: 'questions_content',
            ellipsis: true,
            render: (item: any, record: any) => {
                const extractedText = removeMathML(item);
                return <RenderHtml cname="auto-img" value={extractedText} />;
            }
        },
        {
            title: '分数',
            dataIndex: 'points',
            key: 'points',
            width: '20%',
            ellipsis: true,
            render: (item: any, record: any) => {
                return (
                    <Input
                        defaultValue={record.points}
                        className='inputs'
                        onChange={(e) => singleSet(record, e)}
                        value={item}
                    />
                );
            }
            // render: (item: any, record: any) => (
            //     // <div>{examType.optionType_[Number(item)]}</div>
            //     <Input    defaultValue={record.points}  className='inputs' onChange={(e) => singleSet(record, e)} value={item} />
            // )
        },
    ];
    const handleScoreConsistency = (questionsType: number, allQuestionLists: any[]) => {
        let allScoresSame = true;
        let firstScore: null | number = null;
        allQuestionLists.forEach((item: any) => {
            if (item?.questions_type === questionsType) {
                if (firstScore === null) {
                    firstScore = item.score;
                }
                if (item.score !== firstScore) {
                    allScoresSame = false;
                }
            }
        });

        return allScoresSame ? firstScore : null;
    };

    useEffect(() => {
        let temp_: any = [
            {
                type: 0,
                point_set: undefined,
                expand: false,
                children: [
                ]
            },
            {
                type: 1,
                point_set: undefined,
                expand: false,
                children: [
                ]
            },
            {
                type: 2,
                point_set: undefined,
                expand: false,
                children: [
                ]
            },
            {
                type: 3,
                point_set: undefined,
                expand: false,
                children: [
                ]
            },
            {
                type: 4,
                point_set: undefined,
                expand: false,
                children: [
                ]
            },
            {
                type: 5,
                point_set: undefined,
                expand: false,
                children: [
                ]
            }
        ]

        if (tempData?.part?.length > 0) {

            const allQuestionLists = tempData?.part.flatMap((item: { questionList: any; }) => item.questionList);
            let allScoresSame = true;
            let firstScore: null = null;
            if (allQuestionLists?.length > 0) { //构造数据
                allQuestionLists.forEach((item: any, index: number) => {
                    if (item?.questions_type == 0) { //单选
                        temp_[0].children.push({
                            questions_content: item.questions_content,
                            id: item.id,
                            type: item.questions_type,
                            points: item.score ? item.score : 0
                        })
                        temp_[0].point_set = handleScoreConsistency(0, allQuestionLists) || '';
                    }
                    else if (item?.questions_type === 1) { //多选
                        temp_[1].children.push({
                            questions_content: item.questions_content,
                            id: item.id,
                            type: item.questions_type,
                            points: item?.score ? item?.score : 0
                        })
                        temp_[1].point_set = handleScoreConsistency(1, allQuestionLists) || '';
                    }
                    else if (item?.questions_type === 2) { //填空
                        temp_[2].children.push({
                            questions_content: item.questions_content,
                            id: item.id,
                            type: item.questions_type,
                            points: item?.score ? item?.score : 0
                        })
                        temp_[2].point_set = handleScoreConsistency(2, allQuestionLists) || '';
                    }
                    else if (item?.questions_type === 3) { //主观
                        temp_[3].children.push({
                            questions_content: item.questions_content,
                            id: item.id,
                            type: item.questions_type,
                            points: item?.score ? item?.score : 0
                        })
                        temp_[3].point_set = handleScoreConsistency(3, allQuestionLists) || '';
                    } else if (item?.questions_type === 5) { //题组
                        temp_[5].children.push({
                            questions_content: item.questions_content,
                            id: item.id,
                            type: item.questions_type,
                            points: item?.score ? item?.score : 0,
                            // children: item?.groupQuestions
                            children: item?.groupQuestions?.map((groupQuestion: any) => ({
                                ...groupQuestion,
                                points: groupQuestion?.groupQuestionScore || 0
                            })) || []
                        })
                        temp_[5].point_set = handleScoreConsistency(5, allQuestionLists) || '';
                    } else { //判断
                        temp_[4].children.push({
                            questions_content: item?.questions_content,
                            id: item?.id,
                            type: item?.questions_type,
                            points: item?.score ? item?.score : 0
                        })
                        temp_[4].point_set = handleScoreConsistency(4, allQuestionLists) || '';
                    }
                });
            }
        }
        const result = temp_.filter((item: any) => item.children.length > 0);


        setData(result)
    }, [tempData])

    const on = (item: any) => {
        const temp = JSON.parse(JSON.stringify(data));
        temp.forEach((item_: any) => {
            if (item_.type === item.type) {
                item_.expand = true;
                if (item_.type === 5) {
                    // 如果是题组类型，展开 groupQuestions
                    item_.groupQuestionsExpand = true;
                }
            }
        });
        setData(temp);
    };

    const off = (item: any) => {
        const temp = JSON.parse(JSON.stringify(data));
        temp.forEach((item_: any) => {
            if (item_.type === item.type) {
                item_.expand = false;
                if (item_.type === 5) {
                    // 如果是题组类型，收起 groupQuestions
                    item_.groupQuestionsExpand = false;
                }
            }
        });
        setData(temp);
    };
    const batchSet = (item: any, e: any) => {
        // console.log('当前设置对象3', item, e)
        const num = Number(e.target.value);
        if (num < 0) {
            message.error('分数不能为负数');
            return
        }
        if (isNaN(num)) {
            message.error('请设置有效分数');
            return
        }
        if (item.type === 5) {
            const temp = JSON.parse(JSON.stringify(data))
            for (let i = 0; i < temp.length; i++) {
                if (temp[i].type === item.type) {
                    // 处理 children
                    temp[i].children?.forEach((item_: any) => {
                        item_.points = num;
                        item_.children?.forEach((item_two: any) => {
                            item_two.points = num;
                        });
                    });
                    temp[i].point_set = num;
                    break;
                }
            }
            setData(temp);
        } else {
            const temp = JSON.parse(JSON.stringify(data));
            for (let i = 0; i < temp.length; i++) {
                if (temp[i].type === item.type) {
                    temp[i].children?.map((item_: any) => {
                        item_.points = num
                    })
                    temp[i].point_set = num
                    break;
                }
            }
            setData(temp);
        }


    }


    const singleSet = (item: any, e: any) => {   //单个设置
        const num = Number(e.target.value);
        if (num < 0) {
            message.error('分数不能为负数');
            return
        }
        if (isNaN(num)) {
            message.error('请设置有效分数');
            return
        }
        const temp = JSON.parse(JSON.stringify(data));
        let flag: boolean = true;

        if (item && item.type !== undefined && item.type !== null) {
            if (item.type === 5) {
                const foundItem = findItemByIdInChildrens(item, temp);
                foundItem.points = num;
                if (foundItem && foundItem.children) {
                    foundItem.children.forEach((child: any) => {
                        child.points = num;
                    });

                }
            } else {
                for (let i = 0; i < temp.length; i++) {
                    if (temp[i].type === item.type) {
                        temp[i].children?.map((item_: any) => {
                            if (item_.id === item.id) {
                                item_.points = num;
                            }
                            if (item_.points !== temp[i].children[0].points) {
                                flag = false;
                            }
                        });
                        if (!flag) {
                            temp[i].point_set = undefined;
                        } else {
                            temp[i].point_set = num;
                        }
                        break;
                    }
                }
            }
        } else {
            const foundItem = findItemByIdInChildren(item.id, temp);
            foundItem.points = num;
        }

        setData(temp)
    }

    const findItemByIdInChildren = (id: number, temp: any[]) => {
        const item = temp.find(i => i.type === 5);

        if (!item) {
            return null;
        }
        for (let child of item.children) {
            for (let subChild of child.children) {
                if (subChild.id === id) {
                    return subChild;
                }
            }
        }

        return null;
    };

    const findItemByIdInChildrens = (item: any, temp: any[]) => {
        if (item.type === 5) {
            for (let child of temp) {
                if (child.type === 5) {
                    for (let subChild of child.children) {
                        if (subChild.id === item.id) {
                            return subChild;
                        }
                    }
                }
            }
        }
        return null;
    };

    return (
        <Modal
            title={title}
            visible={visible}
            onCancel={onclose}
            onOk={() => callback(data)}
            width={800}
            className='point_modal'
        >
            <div className='header'>
                <span></span>
                <span>题型</span>
                <span>分数</span>
            </div>
            {
                data.map((item: any, index: number) => {
                    return (
                        <>
                            <div className='content_row' key={index}>
                                {item.expand ? (
                                    <MinusSquareOutlined onClick={() => off(item)} />
                                ) : (
                                    <PlusSquareOutlined onClick={() => on(item)} />
                                )}
                                <span>{examType.optionType_[Number(item.type)]}</span>

                                <Input
                                    onChange={(e: any) => batchSet(item, e)}
                                    value={item.point_set}
                                />

                            </div>
                            {item.expand && (
                                <Table
                                    className='Tables'
                                    columns={columns}
                                    rowKey={'id'}
                                    dataSource={item.children}
                                    pagination={false}
                                />
                            )}
                        </>
                    );
                })
            }
        </Modal >
    );

};

export default ScoreSettingModal;