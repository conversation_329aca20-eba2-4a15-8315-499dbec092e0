namespace copyandmoveTypes {
    export interface Imovedetail {
        key: string
        title: string
        id: string
    }
    export interface IcopyData {
        contentId: string,
        copyEntity: {
          tree:string[]
        }
        name?:string
    }
    export interface ImoveData {
        checkPermission: boolean
        paramType: string
        source: string[]
        target: string
        tryMerge: boolean
    }
    // export interface IdeleteResult {
    //     check_use_time: number
    //     failed_count: number
    //     failed_list: Ifailedlist[]
    //     finish_time: string
    //     id: string
    //     running_use_time: number
    //     start_time: string
    //     status: string
    //     success_count: number
    //     total_count: number
    // }
}

export default copyandmoveTypes