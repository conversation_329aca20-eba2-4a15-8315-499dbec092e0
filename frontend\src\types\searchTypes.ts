namespace searchTypes {
    export interface IBasePaging {
        pageIndex: number
        pageSize: number
        pageTotal: number
        recordTotal: number
    }

    export interface ISearchRes extends IBasePaging {
        breadcrumbs: IBreadcrumb[]
        data: ISearchData[]
        facets: any[]
    }

    export interface ISearchData {
        ascription: string
        contentId_: string
        createDate_: string
        createUser_: string
        creator: string
        importuser: string
        keyframe_: string
        lastUpdate_: {
            site: string
            system: string
            user: string
        }
        name: string
        name_: string
        operateCode_: number
        order_: number
        privilegeUserGroup_: string[]
        privilege_: string
        site_: string
        source: string
        subtype: string
        superTaskInstanceID: string
        tree_: string[]
        type: string
        type_: string
        newFolder?: boolean
        duration: number
        filesize: number
        fileext: string
        system_: string
        fatherTreeId?: string
        keyframe: string
    }

    export interface IBreadcrumb {
        folderId: string
        name: string
        path: string
    }

    export interface IFolder {
        childCount?: number;
        id:string
        // code: string
        name: string
        path: string
        showName: string
        children?: IFolder[]
        layer: number
        parentPath: string
        value: string
    }
    export interface Nfolder {
        users: object
        name: string
        parentId?: string
        parentPath?: string
        privilege: string
    }
    export interface RNfolder {
        resourceId: string
        newName: string
        oldName: string
    }
    export interface RNentity {
        resourceId: string
        newName: string
        oldName: string
    }
    export interface DLentity {
        users: object
        name: string
        parentId?: string
        privilege: string
    }
    export interface INfolder {
        contentId: string
        name: string
        parentPath: string
        path: string
        showName: string
    }
    export interface IDownLoad {
        contentId: string
        downloadAddress: Array<string>
        entityName: string
        fileGroups: Array<object>
    }
    export interface IEntityType {
        alias: string
        controlType: number
        dataType: string
        fieldName: string
        fieldPath: string
        fixItemId: string
        hiveMaxLength: number
        hiveMinLength: number
        hiveMustInput: boolean
        id: number
        isArray: boolean
        isEnable: boolean
        isMultiSelect: boolean
        isMustInput: boolean
        isReadOnly: boolean
        maxLength: number
        metadataType: string
        minLength: number
        order: number
        showName: string
        type: string
    }
}

export default searchTypes