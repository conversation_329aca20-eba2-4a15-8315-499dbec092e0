import React, { useState, useEffect } from 'react';
import './index.less';
import {
  Input,
  Select,
  message
} from 'antd';
import { IconFont, TopicCreation } from '@/components/index';
import {useLocation} from 'umi'
import paperManageApis from '@/api/paper';
import PaperCreation from '@/components/paperCreation';
// import {IconFont} from '@/components/iconFont/iconFont';
function newPaper() {
  const location:any = useLocation();
  useEffect(() => {
    // fetchSharelist();
  }, []);
  const [initusers,setInitusers]= useState<any>([]);
  const detail = location.query.detail;
  return (
    <div className="paper_manage">
      <PaperCreation
        opt_type={location.query.opt_type}
        selectKeys={initusers}
        detail={detail?detail:''}
      />
    </div>
  );
}
export default newPaper;
