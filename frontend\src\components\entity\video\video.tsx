import React, { <PERSON> } from 'react';
import XgVideo from "@/components/XgVideo";

const Video: FC<Entity.IBaseEntity> = ({ src, onError, finishStatus, id, isAutoplay = false, pip, knowledge, onListener, onUpdate,cover ,...rest}) => {
  return <XgVideo {...rest} url={src}  id={id} isAutoplay={isAutoplay} finishStatus={finishStatus} pip={pip} knowledge={knowledge} onListener={onListener} cover={cover} onUpdate={onUpdate} />;
};

export default Video;