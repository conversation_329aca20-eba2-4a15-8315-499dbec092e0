import React, { useEffect, useState } from "react";
import { Table, Input, Button, Tag, Modal } from "antd";
import './index.less';
import { LeftCircleFilled, } from '@ant-design/icons';
import { history, useLocation, useSelector } from 'umi';
import Contract from "@/api/Contract";
//
import passImage from '@/images/courseface/pass.png';
import fineImage from '@/images/courseface/fine.png';
import goodImage from '@/images/courseface/good.png';
import flunkImage from '@/images/courseface/flunk.png';

const Details = () => {
    const [searchText, setSearchText] = useState("");
    const location: any = useLocation();
    const [Id, setId] = useState();
    const [Data, setData] = useState([]);

    const [TableData, setTableData] = useState([]);
    const [pagination, setPagination] = useState({
        page: 1,  // 当前页码
        size: 10, // 每页显示的数量,
        id: Id,
    });
    const [pagtotal, setPagtotal] = useState();

    useEffect(() => {
        const id = location.query.Id;
       
        setId(id)
        // setPagination(id)
        getSubmitVoAPI(id)

    }, []);

    useEffect(() => {
        const id = location.query.Id;
        pageAPI(pagination.page, pagination.size, id, '');
    }, [pagination.page, pagination.size]);


    const getSubmitVoAPI = async (id: any) => {
        const res = await Contract.getSubmitVo(id);
        if (res.status === 200) {
            setData(res.data)
        }
    };

    const pageAPI = async (page: number, size: number, Id: any, nameCodeStr: string) => {
        const res = await Contract.page({ page, size, Id, nameCodeStr });
        if (res.status === 200) {
           
            setPagtotal(res.data.totalCount)
            setTableData(res.data)
        }
    };

    const checkReadAPI = async (Id: any, record: any) => {
        const res = await Contract.checkRead(Id);
        if (res.status === 200) {
            operatecheckReadSelct(res.data, record)
        }
    };

    // 表格列配置
    const columns = [
        {
            title: "姓名",
            dataIndex: "stuName",
            key: "stuName",
            align: 'center',
        },
        {
            title: "账号",
            dataIndex: "stuCode",
            key: "stuCode",
            align: 'center',
        },
        {
            title: "提交时间",
            dataIndex: "submitDate",
            key: "submitDate",
            align: 'center',
            render: (text: any) => formatDate(text),
        },
        {
            title: "批改状态",
            dataIndex: "isRead",
            key: "isRead",
            align: 'center',
            render: (text: any) =>
                <div style={{ color: text ? '#3DBD16' : '#FF6D38' }}>
                    {text ? '已批阅' : '未批阅'}
                </div>
        },
        {
            title: "批改人",
            dataIndex: "readUserName",
            key: "readUserName",
            align: 'center',
        },
        {
            title: "得分",
            dataIndex: "score",
            key: "score",
            align: 'center',
        },
        {
            title: '等级',
            dataIndex: 'rank',
            key: 'rank',
            align: 'center',
            render: (rank: string) => {
                // 定义图片映射关系
                const rankImageMap: { [key: string]: string } = {
                    pass: passImage,
                    fine: fineImage,
                    good: goodImage,
                    flunk: flunkImage,
                };

                // 根据 rank 获取对应的图片
                const imageSrc = rankImageMap[rank];

                // 如果图片存在，则显示图片，否则显示空内容
                return imageSrc ? (
                    <img src={imageSrc} alt={rank} style={{ width: '70px', height: '24px' }} />
                ) : (
                    ''
                );
            },
        },

        {
            title: "操作",
            key: "action",
            align: 'center',
            render: (_: any, record: any) => (
                <Button style={{
                    background: '#549CFF',
                    borderRadius: '17px',
                    border: 'none',
                    padding: '6px 23px'
                }}
                    onClick={() => operate(record)} type="primary">{record.isRead ? "查看" : "批阅"}
                </Button>
            ),
        },
    ];

    const handleTableChange = (pagination: { page: any; size: any; }) => {
        setPagination({
            page: pagination.page,
            size: pagination.size,
            id: Id,
        });
    };


    // 操作处理逻辑
    const operate = (record: any) => {
        // console.log(record, '操作', Id);
        checkReadAPI(Id, record)
    };

    const operatecheckReadSelct = (checkReadSelcts: any, record: any) => {
       
        if (checkReadSelcts) {
            if (record.isRead) {
                
                const newArray = TableData?.data.map(item => ({
                    name: item.name,
                    id: item.id,
                    totalScore: item.totalScore,
                    createUserName: item.createUserName
                }));
                const index = newArray.findIndex(item => item.id === record.id);
                if (index !== -1) {
                    const [matchedItem] = newArray.splice(index, 1);
                    newArray.unshift(matchedItem);
                }

                
                history.push({
                    pathname: `/ExamModule/ExamDetails`,
                    state: { data: newArray, id: record.id }
                });
            } else {
                const newArray = TableData?.data.map(item => ({
                    name: item.name,
                    id: item.id,
                    totalScore: item.totalScore,
                    createUserName: item.createUserName
                }));
                const index = newArray.findIndex(item => item.id === record.id);
                if (index !== -1) {
                    const [matchedItem] = newArray.splice(index, 1);
                    newArray.unshift(matchedItem);
                }

                
                history.push({
                    pathname: `/ExamModule/ExamDetails`,
                    state: { data: newArray, id: record.id }
                });
            }
        } else {
            Modal.warning({
                title: '您没有权限',
                content: `您没有权限${record.isRead ? "查看" : "批阅"}此项内容`,
                okText: '知道了',
            });
        }
    }

    // 搜索处理逻辑
    const handleSearch = (e: any) => {
        setSearchText(e.target.value);
    };

    const butnsearch = () => {
        pageAPI(pagination.page, pagination.size, Id, searchText);
    }

    // 过滤数据
    // const filteredData = data.filter(
    //     (item) =>
    //         item.name.includes(searchText) || item.account.includes(searchText)
    // );

    const getReadStateLabel = (readState: any) => {
        switch (readState) {
            case 1:
                return '未批阅';
            case 2:
                return '已批改';
            case 3:
                return '系统阅卷';
            default:
                return '';
        }
    };

    // 根据 readState 返回对应的颜色
    const getTagColor = (readState: number) => {
        if (readState === 1) {
            return '#FF6D38';
        }
        return '#3DBD16';
    };

    const formatDate = (timestamp: string | number | Date) => {
        if (!timestamp) return '';  // 如果没有时间戳，返回空字符串
        const date = new Date(timestamp);  // 将毫秒时间戳转换为 Date 对象
        return date.toLocaleDateString('zh-CN');  // 转换为中文格式的日期 (yyyy-mm-dd)
    };

    return (
        <div className="Detailss" >
            <header className='header'>
                <a className='comeback' onClick={() => window.history.back()} >
                    <LeftCircleFilled style={{
                        color: '#CBCBCB', width: '20px',
                        height: '20px', fontSize: '32px',
                        marginRight: '10px',
                    }} />
                    <div className='fs'>返回</div>
                </a>
                <div className='statisticsContainer'>

                </div>
            </header >
            <div className="box" >
                <div className="statistics">
                    <div style={{ marginBottom: "20px" }}>
                        <div>
                            <Tag color={getTagColor(Data?.readState)}>
                                {getReadStateLabel(Data?.readState)}
                            </Tag>
                            <strong className="fs" >{Data?.name}</strong>
                        </div>
                        <div className="pdtop fs fs14 color2a" >测验开放时间: {formatDate(Data?.openStartDate)} - {formatDate(Data?.openEndDate)} </div>
                        <div className="pdtop fs fs14 color2a">已提交（含补考）：{Data?.submitQuantity ? Data?.submitQuantity : 0}</div>
                        <div className="pdtop fs fs14 color2a">待批改（含补考）：{Data?.notReadQuantity ? Data?.notReadQuantity : 0}</div>
                    </div>
                </div>
                <div className="statistics" >
                    <div className="searchinput">
                        <Input
                            placeholder="请输入姓名"
                            style={{ width: 300, marginRight: 10 }}
                            value={searchText}
                            onChange={handleSearch}
                        />
                        <Button type="primary" onClick={butnsearch}  >搜索</Button>
                    </div>

                    <Table
                        columns={columns}
                        dataSource={TableData?.data || []}
                        pagination={{
                            position: ["bottomCenter"],
                            current: pagination.page,
                            pageSize: pagination.size,
                            total: pagtotal,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            onChange: (page, size) => handleTableChange({ page, size }),
                        }}
                        scroll={{ y: 'calc(100vh - 505px)' }}
                    />
                </div>
            </div>
        </div>
    );
};

export default Details;
