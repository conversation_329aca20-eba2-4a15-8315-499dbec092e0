import React, { FC, useEffect, useState } from 'react';
import moment from "moment";
import themeService from "@/api/theme";
import { Empty, message } from "antd";
import "./index.less";

const MessageItem: FC<{ data: any; onClick: () => void; }> = ({ data, onClick }) => {
  const clickHandle = () => {
    if (/Mobi|Android|iPhone/i.test(navigator.userAgent)) {
      message.info("暂不支持手机端，请前往电脑端操作");
      return;
    }
    if (!data.isRead) {
      themeService.updateMessageRead({ id: data.id }).then(res => {
        if (res.errorCode === 'success') {
          onClick();
        }
      });
    }
    window.open(data.linkUrl)
  };
  return <div className="message-item">
    <div className={`prefix ${data.isRead ? '' : 'unread'}`}></div>
    <div className="box">
      <div className="date">{moment(data.notifyTime * 1000).format("YYYY年MM月DD日 HH:mm")}</div>
      <div className="title" onClick={() => {
        clickHandle();
        window.open("/unifiedplatform/#/personal/message");
      }}>{data.title}</div>
      <div className="content">
        <div className="message-text">
          <span>{data.content}</span>
          {data.linkUrl && <a className="jump" onClick={clickHandle}>前往查看{'>>'}</a>}
        </div>
      </div>
    </div>
  </div>;
};

const MessageBox: FC<{ readChange: () => void; }> = ({ readChange }) => {
  const [messages, setMessages] = useState<any>([]);
  const [total, setTotal] = useState<number>(0);
  useEffect(() => {
    getMessages();
  }, []);
  const getMessages = () => {
    themeService.reqMessageList({ page: 1, size: 10, isRead: false }).then((res: any) => {
      if (res.errorCode === 'success') {
        setMessages(res.extendMessage.results ?? []);
        setTotal(res.extendMessage.recordTotal);
      }
    });
  };

  const HandleClick = () => {
    if (window.location.pathname.includes("/personal/message")) return;
    window.location.href = "/unifiedplatform/#/personal/message";
  };
  return <div className='message-box'>
    {messages.length > 0 ? messages.map((item: any) => <MessageItem key={item.id} data={item} onClick={() => {
      getMessages();
      readChange();
    }}/>) : <Empty description="暂无未读消息" />}
    <div className='look-more' onClick={HandleClick}>查看更多</div>
  </div>;
};

export default MessageBox;