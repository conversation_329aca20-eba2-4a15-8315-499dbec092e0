import Header from '@/components/header';
import { IconFont } from '@/components/iconFont/iconFont';
import LeftMenu from '@/components/leftMenu';
import NPUHeader from '@/components/NPUHeader';
import { IConfig } from '@/models/config';
import { ModuleCfg } from '@/permission/globalParams';
import loginApis from '@/service/loginApis';
import { Menu, Tabs } from 'antd';
import { FC, useEffect, useState } from 'react';
import { history, Link, useLocation, useSelector } from 'umi';
import './index.less';
const ExamLayout: FC = ({ children }) => {
  const location: any = useLocation();
  const [userInfo, setUserInfo] = useState<any>(null);
  const [loginName, setLoginname] = useState<string>('');
  const { parameterConfig, permissions, modules } = useSelector<
    { permission: any },
    any
  >(({ permission }) => permission);
  const configs: any = useSelector<{ config: any }, IConfig>(
    ({ config }) => config,
  );

  const active = location.pathname || '';

  // 动态高亮逻辑
  const getSelectedKey = () => {
    if (active.startsWith('/exam/testManagementment') || active.startsWith('/ExamModule/Revise') || active.startsWith('/ExamModule/Detailspage')) {
      return '/exam/testManagementment'; // 测试管理菜单项高亮
    }
    return active; // 其他路径保持原样
  };
  useEffect(() => {
    loginApis.fetchUserInfo().then((item: any) => {
      if (item && item.errorCode === 'success') {
        setLoginname(item.extendMessage.nickName);
        // console.log(item, 'item.dataitem.data');
        localStorage.setItem("userinfo", JSON.stringify(item.extendMessage));
        (window as any).login_useInfo = item.extendMessage;
        setUserInfo(item.extendMessage);
      }
    });
  }, []);
  const tabChange = (e:any)=>{
    console.log(location, 'location')
    if(e=='topic'){
      history.push(`/exam/topicManage`)
    }else{
      history.push('/exam/paperManage')
    }
    if (e === 'ExamModule') {
      history.push(`/exam/testManagementment`)
    }
    if (e === 'Revise') {
      history.push(`/exam/paperManage`)
    }
  }
  // console.log(location, 'location')
  return (
    <div className={`uf-exam-layout-wrapper ${parameterConfig.target_customer === "npu" && 'exam-npu-container'}`}>
      {parameterConfig.target_customer === "npu" ? <NPUHeader /> : <Header
        // subtitle={'试题库'}
        ifBack={
          modules.includes(ModuleCfg.jove) &&
          modules.includes(ModuleCfg.teacher)
        }
        user_Info={userInfo}
        showNav={true}
        navActive={active}
      />}
      <div className="uf-exam-layout-content">
        {parameterConfig.target_customer != null && parameterConfig.target_customer !== "npu" && parameterConfig.target_customer !== "kczx" && <LeftMenu userInfo={userInfo} />}
        {configs.mobileFlag ? (
          <div className='mobileContainer'>
            <Tabs
              activeKey={location.pathname.includes('topicManage') ? 'topic' : (location.pathname.includes('someOtherCondition') ? 'paper' : 'ExamModule' || 'contract')}
              onChange={tabChange}
            >
              <Tabs.TabPane key={'topic'} tab='题库管理'></Tabs.TabPane>
              <Tabs.TabPane key={'paper'} tab='作业组卷'></Tabs.TabPane>
              <Tabs.TabPane key={'ExamModule'} tab='测试管理'></Tabs.TabPane>
              <Tabs.TabPane key={'Revise'} tab='测验分类'></Tabs.TabPane>
            </Tabs>
            <div className='content'>{children}</div>
          </div>
        ) : (
          <>
            <div className="uf-exam-left-part">
              <Menu mode="inline" selectedKeys={[getSelectedKey()]}>
                {
                  <Menu.Item key="/exam/topicManage">
                    <Link to={`/exam/topicManage${location?.query?.micromajor ? '?micromajor=true' : ''}`}>
                      <IconFont
                        type="icontikuguanlibeifen1"
                        style={{ paddingRight: '10px', fontSize: '16px', }}
                      />
                      题库管理
                    </Link>
                  </Menu.Item>
                }
                {modules.includes('paperManagement') && (
                  <Menu.Item key="/exam/paperManage">
                    <Link to={`/exam/paperManage${location?.query?.micromajor ? '?micromajor=true' : ''}`}>
                      <IconFont
                        type="iconshijuanguanli1"
                        style={{ paddingRight: '10px', }}
                      />
                      作业组卷
                    </Link>
                  </Menu.Item>
                )}

                {modules.includes('testManagementment') && (
                  <Menu.Item key="/exam/testManagementment">
                    <Link to={`/exam/testManagementment${location?.query?.micromajor ? '?micromajor=true' : ''}`}>
                      <IconFont
                        type="iconceyanguanlibeifen1"
                        style={{ paddingRight: '10px', fontSize: '16px', }}
                      />
                      测验管理
                    </Link>
                  </Menu.Item>
                )}

                {modules.includes('ContractManagement') && (
                  <Menu.Item key="/exam/ContractManagement">
                    <Link to={`/exam/ContractManagement${location?.query?.micromajor ? '?micromajor=true' : ''}`}>
                      <IconFont
                        type="iconfenleiguanlibeifen1"
                        style={{ paddingRight: '10px', fontSize: '16px', }}
                      />
                      测验分类
                    </Link>
                  </Menu.Item>
                )}

                {/* {
                  <Menu.Item key="/exam/testManagementment">
                    <Link to={`/exam/testManagementment${location?.query?.micromajor ? '?micromajor=true' : ''}`}>
                      <IconFont
                        type="icontikuguanli"
                        style={{ paddingRight: '10px' }}
                      />
                      测验管理
                    </Link>
                  </Menu.Item>
                }
                {
                  <Menu.Item key="/exam/ContractManagement">
                    <Link to={`/exam/ContractManagement${location?.query?.micromajor ? '?micromajor=true' : ''}`}>
                      <IconFont
                        type="icontikuguanli"
                        style={{ paddingRight: '10px' }}
                      />
                      分类管理
                    </Link>
                  </Menu.Item>
                } */}
                <Menu.Item key="/exam/recycle">
                  <Link to={`/exam/recycle${location?.query?.micromajor ? '?micromajor=true' : ''}`}>
                    <IconFont
                      type="iconhuishouzhan"
                      style={{ paddingRight: '10px' }}
                    />
                    回收站
                  </Link>
                </Menu.Item>
                {modules.includes('testsystem') && (
                  <Menu.Item key="/testsystem" onClick={() => {
                    history.push('/testsystem')
                  }}>

                    <IconFont
                      type="iconhuishouzhan"
                      style={{ paddingRight: '10px' }}
                    />
                    开始测验
                  </Menu.Item>
                )}
              </Menu>
            </div>
            <div className="uf-exam-right-part">{children}</div>
          </>
        )}
      </div>
    </div>
  );
};
export default ExamLayout;
