namespace resourcesType {
    export interface Ifile {
        ascription: string
        contentId_: string
        createDate_: string
        createUser_: string
        creator: string
        importuser: string
        keyframe_: string
        lastUpdate_: {
            site: string
            system: string
            user: string
        }
        name: string
        name_: string
        operateCode_: number
        order_: number
        privilegeUserGroup_: string[]
        privilege_: string
        site_: string
        source: string
        subtype: string
        superTaskInstanceID: string
        tree_: string[]
        type: string
        type_: string
        newFolder?:boolean
        duration: number
        filesize: number
        fileext: string
        fatherTreeId?:string
    }
    // export interface Ifolder {
    //     contentId_: string
    //     createDate_: string
    //     createUser_: string
    //     keyframe: string
    //     name_: string
    //     operateCode_?: number
    //     order_?: number
    //     privilegeUserGroup_?: Array<string>
    //     privilege_: string
    //     site_: string
    //     system_?: string
    //     tree_: Array<string>
    //     type_: string
    //     updateDate_?: string
    //     updateUser_?: string
    //     newFolder?:string
    // }
}

export default resourcesType