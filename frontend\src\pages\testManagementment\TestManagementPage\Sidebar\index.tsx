// Sidebar.tsx
import React from "react";
import { Layout, Menu } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
const { Sider } = Layout;

interface SidebarProps {
    onMenuItemClick: (section: string) => void;  // 菜单项点击时的回调函数
}

const Sidebar: React.FC<SidebarProps> = ({ onMenuItemClick }) => {
    return (
        <Sider width={200} className="sider-container">
            <Menu mode="inline" defaultSelectedKeys={["1"]} className="menu-container">
                <Menu.Item key="1" onClick={() => onMenuItemClick("basicInfo")}>
                    <div style={{ display: 'flex', alignItems: 'center' }} >
                        <img
                            src={require('@/images/icons/Message.png')}
                            alt="基本信息"
                            style={{ width: "16px", marginRight: "8px" }}
                        />

                        <div>
                            基本信息
                        </div>
                    </div>
                </Menu.Item>
                <Menu.Item key="2" onClick={() => onMenuItemClick("testQuestions")}>

                    <div style={{ display: 'flex', alignItems: 'center' }} >
                        <img
                            src={require('@/images/icons/topic.png')}
                            alt="测验题目"
                            style={{ width: "16px", marginRight: "8px" }}
                        />

                        <div>
                            测验题目
                        </div>
                    </div>
                </Menu.Item>
                <Menu.Item key="3" onClick={() => onMenuItemClick("testSettings")}>
                    <div style={{ display: 'flex', alignItems: 'center' }} >
                        <img
                            src={require('@/images/icons/setup.png')}
                            alt="测验设置"
                            style={{ width: "16px", marginRight: "8px" }}
                        />

                        <div>
                            测验设置
                        </div>
                    </div>
                </Menu.Item>
                <Menu.Item key="4" onClick={() => onMenuItemClick("StudentSettings")}>
                    <div style={{ display: 'flex', alignItems: 'center' }} >
                        <img
                            src={require('@/images/icons/Studen.png')}
                            alt="学员设置"
                            style={{ width: "16px", marginRight: "8px" }}
                        />

                        <div>
                            学员设置
                        </div>
                    </div>
                </Menu.Item>
                <Menu.Item key="5" onClick={() => onMenuItemClick("testNotification")}>
                    <div style={{ display: 'flex', alignItems: 'center' }} >
                        <img
                            src={require('@/images/icons/notify.png')}
                            alt="测试通知"
                            style={{ width: "16px", marginRight: "8px" }}
                        />

                        <div>
                            测试通知
                        </div>
                    </div>
                </Menu.Item>
                <Menu.Item key="6" onClick={() => onMenuItemClick("testIntro")}>

                    <div style={{ display: 'flex', alignItems: 'center' }} >
                        <img
                            src={require('@/images/icons/introduce.png')}
                            alt="测验介绍"
                            style={{ width: "16px", marginRight: "8px" }}
                        />

                        <div>
                            测验介绍
                        </div>
                    </div>
                </Menu.Item>
                <Menu.Item key="7" onClick={() => onMenuItemClick("testInstructions")}>

                    <div style={{ display: 'flex', alignItems: 'center' }} >
                        <img
                            src={require('@/images/icons/illustrate.png')}
                            alt="测验说明"
                            style={{ width: "16px", marginRight: "8px" }}
                        />

                        <div>
                            测验说明
                        </div>
                    </div>
                </Menu.Item>
            </Menu>
        </Sider>
    );
};

export default Sidebar;
