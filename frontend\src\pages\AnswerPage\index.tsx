import examManageApis from '@/api/exam';
import { message } from 'antd';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { FC, useEffect, useMemo, useRef, useState } from 'react';
import { history, useLocation, useSelector } from 'umi';
import { useImmer } from 'use-immer';
import LeftTimeOverModal from './component/LeftTimeOverModal';
import OrderChangeModal from './component/OrderChangeModal';
import QuestionContent from './component/QuestionContent';
import TestHeader from './component/TestHeader';
import TitleNumberList from './component/TitleNumberList';
import UnfinishedModal from './component/UnfinishedModal';
import { PageInfoContext } from './context';
import useCountdown from './hook/useCountDown';
import './index.less';
import {
  formatQuestionList,
  getChangeItemStatusList,
  getContinueExamInitAnswer,
  getFlagQuestion,
  uniqueByKey,
} from './utils';

const TimeUnit: any = {
  second: 1,
  minute: 60,
};

// 校验题目相邻的参数
interface ISelectInfo {
  partIndex: number;
  index: number;
  partId: string;
  partLen: number;
}

interface ITimeSetting {
  // 设置的时间
  time: number;
  /** 是否有设置 */
  isSetting: boolean;
  // 标识id
  id: string;
}

const AnswerPage: FC = () => {
  const location: any = useLocation();

  const [paperInfo, setPaperInfo] = useImmer({
    answer: [],
    curAnsweringItem: {},
    curQuestionInfo: {
      partIndex: 0, // 第几部分
      questionIndex: 0, // 第几题
      questionId: '', // 题目id(大题)
      partQuestionId: '', // 部分id
      isFirst: true, // 是否是第一题
      isLast: false, // 是否是最后一题
      groupItemId: '', // 题组扁平化后的手动id
    },
  });
  //
  const handleChangeImmerValue = (value: any, key: string) => {
    setPaperInfo((draft: any) => {
      draft[key] = value;
    });
  };

  const userInfo: any = useSelector<{ userInfo: any }, any>(
    ({ userInfo }: any) => userInfo,
  );
  // 考试结束
  const testIsOver = useRef(false);
  // 交卷提示
  const [showSubmitTips, setShowSubmitTips] = useState(false);
  // 是否配置了最短交卷时长
  const hasMinSubmitTime = useRef(true);
  // 答题区域的ref
  const contentRef = useRef<any>(null);
  // 是否切换了部分
  const [partIsChange, setPartIsChange] = useState(false);
  // 是否有配置读题倒计时
  // const hasReadTime = useRef(true);
  // 答题时间
  const startTime = useRef(dayjs());
  // 部分的答题时间
  const partStartTime = useRef(dayjs());

  // 当前题的读题倒计时是否结束
  const isEndReadTime = useRef(false);
  //#region 倒计时变量 所有倒计时都是非必要的
  // 默认值
  const defaultTime: ITimeSetting = {
    time: 0,
    isSetting: true,
    id: '',
  };
  // 试卷总时长
  const [paperTime, setPaperTime] = useState<ITimeSetting>(defaultTime);
  // 最短交卷时长
  const [enableSubMinTime, setEnableSubMinTime] =
    useState<ITimeSetting>(defaultTime);
  // 结束时间多久前的提示
  const [tipsTime, setTipsTime] = useState<ITimeSetting>(defaultTime);
  // 每题的读题时长
  const [itemReadTime, setItemReadTime] = useState<ITimeSetting>(defaultTime);
  // 每题的作答时长
  const [itemAnswerTime, setItemAnswerTime] =
    useState<ITimeSetting>(defaultTime);
  // 该部分答题的最低总时长
  const [partMinTime, setPartMinTime] = useState<ITimeSetting>(defaultTime);
  // 该部分答题最高总时长
  const [partMaxTime, setPartMaxTime] = useState<ITimeSetting>(defaultTime);
  //#endregion
  // 是否允许跨部分答题
  const enableCrossPart = useRef(false);
  // 部分内是否允许跨部分答题
  const enableSkip = useRef(false);
  // 题目列表
  const [questionList, setQuestionList] = useState<any>([]);
  const questionListRef = useRef<any>([]);
  //   当前题目位置信息
  const [curQuestionInfo, setCurQuestionInfo] = useState<any>({
    partIndex: 0, // 第几部分
    questionIndex: 0, // 第几题
    questionId: '', // 题目id(大题)
    partQuestionId: '', // 部分id
    isFirst: true, // 是否是第一题
    isLast: false, // 是否是最后一题
    groupItemId: '', // 题组扁平化后的手动id
  });
  // 使用ref保存当前题目的信息
  const curQuestionInfoRef = useRef<any>({});
  // 当前正在作答的题目
  // const [curAnsweringItem, setCurAnsweringItem] = useState<any>({});
  const curAnsweringItemRef = useRef<any>({});
  //   试卷信息
  // const [testPaperInfo, setTestPaperInfo] = useState<any>({});
  const testPaperInfoRef = useRef<any>({});
  // 当前题的答案
  // const [answer, setAnswer] = useState<string[]>([]);
  const answerRef = useRef<any[]>([]);
  // 初始化答案
  const initAnswer = (type: number, len: number, groupLen: number) => {
    if (type === 2) {
      const tempArr = new Array(len).fill('');
      // setAnswer(tempArr);
      handleChangeImmerValue(tempArr, 'answer');
      answerRef.current = tempArr;
    } else {
      // setAnswer([]);
      handleChangeImmerValue([], 'answer');
      answerRef.current = [];
    }
  };

  //#region 自动切换下一题
  const [isNeedToNextQuestion, setIsNeedToNextQuestion] = useState(false);
  useEffect(() => {
    if (isNeedToNextQuestion) {
      setIsNeedToNextQuestion(false);
      handleToNextQuestion();
    }
  }, [isNeedToNextQuestion]);
  //#endregion

  //#region 获取试卷信息

  // 赋值考试信息
  const handleInitPaperInfo = (res: any, isContinue?: boolean) => {
    // setTestPaperInfo(res.data);
    testPaperInfoRef.current = res.data;
    // 最短交卷时长的
    if (!res.data?.minSubmitMinute) {
      hasMinSubmitTime.current = false;
    }
    //#region 处理倒计时
    // 交卷提示分钟
    setTipsTime({
      time: res.data?.submitTipMinute || 0,
      isSetting: res.data?.submitTipMinute !== 0,
      id: res.data.id,
    });
    // 最短交卷时长
    setEnableSubMinTime({
      time: res.data?.minSubmitMinute || 0,
      isSetting: res.data?.minSubmitMinute !== 0,
      id: res.data.id,
    });

    //#endregion
    const partExamList = res.data?.partExam || [];
    // 初始化题目列表得题得状态
    const formatStatus = formatQuestionList(partExamList);
    // 扁平化题组得题
    const contentList = getFlagQuestion(formatStatus);
    const curInitItem = contentList?.[0]?.questions?.[0];
    // 是不是只有一个题
    const isOnlyOne = contentList?.length === 1 && contentList?.[0]?.questions?.length === 1;
    setQuestionList(contentList);
    questionListRef.current = contentList;
    const curInfo: any = {
      partIndex: 0,
      questionIndex: 0,
      unifiedIndex: 0,
      questionId: curInitItem?.question?.id,
      partQuestionId: partExamList?.[0]?.id,
      isFirst: true,
      isLast: isOnlyOne,
    };
    if (curInitItem?.question?.questions_type === 5) {
      curInfo.groupItemId = curInitItem?.question?.groupQuestions?.[0]?.id;
    }
    setCurQuestionInfo(curInfo);
    handleChangeImmerValue(curInfo, 'curQuestionInfo');
    curQuestionInfoRef.current = curInfo;
    // setCurAnsweringItem(curInitItem);
    curAnsweringItemRef.current = curInitItem;
    handleChangeImmerValue(curInitItem, 'curAnsweringItem');

    const curPart = contentList?.[0];
    // hasReadTime.current = !!curPart?.readTime;
    if (curPart?.status === 1) {
      isEndReadTime.current = false;
    } else {
      isEndReadTime.current = true;
    }
    if (isContinue) {
      // 获取初始化的答案
      const value = getContinueExamInitAnswer(curInitItem);
      handleChangeImmerValue(value, 'answer');
      answerRef.current = value;
      const answerMillisecond = res.data?.testAnswer?.answerMillisecond || 0;
      const allTime = (res.data?.answerMinute || 0) * 60;
      // 试卷总时长
      setPaperTime({
        time: allTime - answerMillisecond / 1000,
        isSetting: res.data?.answerMinute !== 0,
        id: res.data.id,
      });
    } else {
      // 试卷总时长
      setPaperTime({
        time: (res.data?.answerMinute || 0) * 60,
        isSetting: res.data?.answerMinute !== 0,
        id: res.data.id,
      });
    }
    // 初始化每题的读题的时间
    setItemReadTime({
      time:
        (curPart?.readTime?.time || 0) *
        TimeUnit[curPart?.readTime?.unit || 'minute'],
      isSetting: !!curPart?.readTime,
      id: curInitItem?.question?.id,
    });

    // 初始化每题的答题的时间
    setItemAnswerTime({
      time:
        (curPart?.answerTime?.time || 0) *
        TimeUnit[curPart?.answerTime?.unit || 'minute'],
      isSetting: !!curPart?.answerTime,
      id: curInitItem?.question?.id,
    });
    //
    enableCrossPart.current = res.data?.isCrossPart || false;
    enableSkip.current = curInitItem?.isSkip || false;
  };
  // 初始进入考试
  const fetchTestInfo = (testId: string) => {
    examManageApis.fetchExamStartInfo({ testId }).then((res: any) => {
      if (res.status == 200) {
        handleInitPaperInfo(res);
      }
    });
  };

  // 继续考试
  const fetchTestInfoContinue = (id: string) => {
    examManageApis.fetchContinueExam({ answerId: id }).then((res: any) => {
      if (res.status == 200) {
        handleInitPaperInfo(res, true);
      }
    });
  };

  useEffect(() => {
    const testId = location.query?.testId;
    const answerId = location.query?.answerId;
    if (!!answerId) {
      fetchTestInfoContinue(answerId);
      return;
    }
    if (testId) {
      fetchTestInfo(testId);
    }
  }, [location.state?.testId, location.query?.answerId]);
  //#endregion

  //#region 倒计时
  // 部分内的倒计时
  const partCountDownTime = useMemo(() => {
    const curPart = questionList?.[curQuestionInfo.partIndex];
    const curPartTime = curPart?.maxTime;
    const answerMillisecond = curPart?.answerPart?.answerMillisecond || 0;
    if (curPartTime?.time > 0) {
      return (
        curPartTime?.time * TimeUnit[curPartTime?.unit || 'minute'] -
          answerMillisecond / 1000 || 0
      );
    }
    return 0;
  }, [questionList?.length, curQuestionInfo?.partIndex]);
  //#endregion

  //#region 答案相关
  // 查询提交过的答案
  const getAnsweredRecord = (id: string, type: number) => {
    examManageApis
      .fetchAnswerRecord({
        answerId: testPaperInfoRef.current?.testAnswer?.id,
        partQuestionId: id,
      })
      .then((res: any) => {
        if (res.status == 200) {
          // 非题组
          let value = res.data?.[0]?.answer?.split(',') || [];
          // 题组处理
          if (type === 5) {
            value = res.data?.map((item: any) => ({
              questionId: String(item.questionId),
              answer: item.answer?.split(','),
            }));
          }
          // setAnswer(value);
          handleChangeImmerValue(value, 'answer');
          answerRef.current = value;
          setItemAnswerTime({
            time: (res.data?.[0]?.answerTime || 0) / 1000,
            isSetting: true,
            id: res.data?.[0]?.partQuestionId,
          });
        }
      });
  };
  //#endregion

  //   标记当前题目为已做
  const handleChangeItemStatus = (list: any[]) => {
    // 获取改变状态后的题目列表
    const tempList = getChangeItemStatusList(
      list,
      curQuestionInfo,
      answerRef.current,
    );
    setQuestionList(tempList);
    questionListRef.current = tempList;
  };

  //#region 提示框
  //   跳题提示框
  const [showOrderModal, setShowOrderModal] = useState<boolean>(false);
  // 判断题目是不是相邻
  const verifyisAdjacent = (curInfo: ISelectInfo, iteminfo: ISelectInfo) => {
    //   同一个部分
    if (curInfo.partId === iteminfo.partId) {
      return Math.abs(curInfo.index - iteminfo.index) === 1;
    }
    // 相邻的两个部分
    if (curInfo.index === curInfo.partLen - 1) {
      return (
        iteminfo.index === 0 && iteminfo.partIndex - curInfo.partIndex === 1
      );
    }
    if (curInfo.index === 0) {
      return (
        iteminfo.index === iteminfo.partLen - 1 &&
        curInfo.partIndex - iteminfo.partIndex === 1
      );
    }
    return false;
  };
  // 选中当前未答的item
  const handleChangeToItem = (selectItem: any) => {
    const curItem = selectItem.curInfo;
    if (answerRef.current?.length > 0) {
      // 1、原来的题目的答案要提交
      handleSaveItemAnswer(true);
    }
    // 2、当前的题目要变成已做
    handleChangeItemStatus([...questionListRef.current]);
    // 3、题目要切换
    const curQueInfo = {
      partIndex: selectItem.parentIndex,
      questionIndex: selectItem.itemIndex,
      questionId: curItem.question.id,
      partQuestionId: selectItem.parentId,
      isFirst: selectItem.firstFlag,
      isLast: selectItem.lastFlag,
      groupItemId: selectItem.groupItemId,
    };
    setCurQuestionInfo(curQueInfo);
    handleChangeImmerValue(curQueInfo, 'curQuestionInfo');
    curQuestionInfoRef.current = curQueInfo;
    // setCurAnsweringItem(curItem);
    curAnsweringItemRef.current = curItem;
    handleChangeImmerValue(curItem, 'curAnsweringItem');
    // 4、答案要初始化
    initAnswer(
      curItem.question.questions_type,
      curItem.question.questions_options?.length || 0,
      curItem.question.groupQuestions?.length || 0,
    );
    const curPart = questionListRef.current?.[selectItem.parentIndex];
    // 读题倒计时重置
    if (!!curPart?.readTime && curItem?.status === 1) {
      setItemReadTime({
        time:
          (curPart?.readTime?.time || 0) *
          TimeUnit[curPart?.readTime?.unit || 'minute'],
        isSetting: !!curPart?.readTime,
        id: curItem.question.id,
      });
    }
    // 如果有读题倒计时，重置倒计时
    if (curItem?.status === 1) {
      isEndReadTime.current = false;
    }
    // hasReadTime.current = !!curPart?.readTime;
  };
  // 切换到已经做过的题目的处理
  const handleToAnsweredItem = (item: any) => {
    const curItem = item.curInfo;
    if (answerRef.current?.length > 0) {
      handleSaveItemAnswer(true);
    }
    handleChangeItemStatus(questionListRef.current);
    // setAnswer([]);
    handleChangeImmerValue([], 'answer');
    getAnsweredRecord(curItem.question.id, curItem.question.questions_type);
    if (item.isGroupItem) {
      const itemInfo = {
        ...curQuestionInfo,
        questionIndex: item.index,
        questionId: curItem.question.id,
        partQuestionId: item.parentId,
        partIndex: item.parentIndex,
        isFirst: item.firstFlag,
        isLast: item.lastFlag,
        groupItemId: item.groupItemId,
      };
      setCurQuestionInfo(itemInfo);
      handleChangeImmerValue(itemInfo, 'curQuestionInfo');
      curQuestionInfoRef.current = itemInfo;
    } else {
      const itemInfo = {
        ...curQuestionInfo,
        questionIndex: item.index,
        questionId: curItem.question.id,
        partQuestionId: item.parentId,
        partIndex: item.parentIndex,
        isFirst: item.firstFlag,
        isLast: item.lastFlag,
        groupItemId: '',
      };
      setCurQuestionInfo(itemInfo);
      handleChangeImmerValue(itemInfo, 'curQuestionInfo');
      curQuestionInfoRef.current = itemInfo;
    }

    // setCurAnsweringItem(curItem);
    curAnsweringItemRef.current = curItem;
    handleChangeImmerValue(curItem, 'curAnsweringItem');
  };
  //   选择答题卡的小题
  // 1、点击的是已做题
  const handleSelectitem = (
    item: any,
    index: number,
    parentId: string,
    parentIndex: number,
    groupItemId?: string,
  ) => {
    // 点击的是非题组的题目
    if (
      item.question.id === curQuestionInfo.questionId &&
      (!groupItemId || groupItemId === curQuestionInfo.groupItemId)
    ) {
      return;
    }
    // 有读题限制时
    if (!isEndReadTime.current) {
      message.warning('读题时间未结束，不能操作');
      return;
    }
    const isGroupItem = item.question.questions_type === 5;
    // 点击的是题组的小题
    if (item.question.id === curQuestionInfo.questionId) {
      setCurQuestionInfo({
        ...curQuestionInfo,
        groupItemId,
      });
      handleChangeImmerValue(
        {
          ...curQuestionInfoRef.current,
          groupItemId,
        },
        'curQuestionInfo',
      );
      curQuestionInfoRef.current = {
        ...curQuestionInfoRef.current,
        groupItemId,
      };
      return;
    }
    // 标识是不是切换了部分
    // if (parentId !== curQuestionInfo?.partQuestionId) {
    //   setPartIsChange(true);
    // }
    const curQuestionList = questionListRef.current;
    const partItemLen = curQuestionList[parentIndex].questions.length;
    const partLen = curQuestionList.length;
    const lastFlag = index === partItemLen - 1 && parentIndex === partLen - 1;
    const firstFlag = index === 0 && parentIndex === 0;
    // 点击已经做过的题目
    if (item.status === 2) {
      handleToAnsweredItem({
        curInfo: item,
        index,
        parentId,
        parentIndex,
        firstFlag,
        lastFlag,
        isGroupItem,
        groupItemId,
      });
      return;
    }

    const curInfo: ISelectInfo = {
      index: curQuestionInfo.questionIndex,
      partId: curQuestionInfo.partQuestionId,
      partIndex: curQuestionInfo.partIndex,
      partLen: curQuestionList[curQuestionInfo.partIndex].questions.length,
    };
    const itemInfo: ISelectInfo = {
      index: index,
      partId: parentId,
      partIndex: parentIndex,
      partLen: curQuestionList[parentIndex].questions.length,
    };
    // 相邻的上一题或者下一题
    if (verifyisAdjacent(curInfo, itemInfo)) {
      handleChangeToItem({
        curInfo: item,
        parentIndex,
        parentId,
        itemIndex: index,
        firstFlag,
        lastFlag,
        groupItemId,
      });
      return;
    }
    // 不是相邻的题目 -> 1、同一部分 2、不同部分
    // 统一部分
    if (curQuestionInfo.partIndex === parentIndex) {
      // 允许跳题
      if (enableSkip) {
        handleChangeToItem({
          curInfo: item,
          parentIndex,
          parentId,
          itemIndex: index,
          firstFlag,
          lastFlag,
          groupItemId,
        });
        return;
      }
      // 不允许跳题
      setShowOrderModal(true);
      return;
    }
    // 不同部分
    if (enableCrossPart) {
      handleChangeToItem({
        curInfo: item,
        parentIndex,
        parentId,
        itemIndex: index,
        firstFlag,
        lastFlag,
        groupItemId,
      });
      return;
    }
    // 不允许跨部分
    setShowOrderModal(true);
    return;
  };
  //#endregion

  //#region
  const getPreOrNextItem = (pre: boolean) => {
    const curQuestionList = questionListRef.current;

    const partItemLen =
      curQuestionList[curQuestionInfo.partIndex].questions.length;
    const allPartCount = curQuestionList.length;
    if (pre) {
      // 当前为第一题
      if (curQuestionInfo.questionIndex === 0) {
        // 当前为第一部分,第一题，此时没有上一题
        if (curQuestionInfo.partIndex === 0) {
          return;
        }
        const prePart = curQuestionList[curQuestionInfo.partIndex - 1];
        const prePartItemLen = prePart.questions.length;
        const curItem = prePart.questions[prePartItemLen - 1];
        return {
          item: curItem,
          index: prePartItemLen - 1,
          parentId: prePart.id,
          parentIndex: curQuestionInfo.partIndex - 1,
        };
      }
      //  普通前一题
      const preItem =
        curQuestionList[curQuestionInfo.partIndex].questions[
          curQuestionInfo.questionIndex - 1
        ];
      return {
        item: preItem,
        index: curQuestionInfo.questionIndex - 1,
        parentId: curQuestionInfo.partQuestionId,
        parentIndex: curQuestionInfo.partIndex,
      };
    }
    // 下一题
    // 当前为最后一题
    if (curQuestionInfo.questionIndex === partItemLen - 1) {
      // 当前部分为最后一部分
      if (curQuestionInfo.partIndex === allPartCount - 1) {
        return;
      }
      // 下一部分下一题
      const nextPart = curQuestionList[curQuestionInfo.partIndex + 1];
      const nextItem = nextPart.questions[0];
      return {
        item: nextItem,
        index: 0,
        parentId: nextPart.id,
        parentIndex: curQuestionInfo.partIndex + 1,
      };
    }
    // 普通下一题
    const nextItem =
      curQuestionList[curQuestionInfo.partIndex].questions[
        curQuestionInfo.questionIndex + 1
      ];
    return {
      item: nextItem,
      index: curQuestionInfo.questionIndex + 1,
      parentId: curQuestionInfo.partQuestionId,
      parentIndex: curQuestionInfo.partIndex,
    };
  };
  //   上一题
  const hanldeToPreQuesion = () => {
    // 获取上一题
    const preItem = getPreOrNextItem(true);
    // 1、上一题已做
    // 2、上一题未做

    if (preItem) {
      const groupItemId =
        preItem?.item?.question?.questions_type === 5
          ? preItem?.item?.question?.groupQuestions?.[0]?.id
          : '';
      handleSelectitem(
        preItem.item,
        preItem.index,
        preItem.parentId,
        preItem.parentIndex,
        groupItemId,
      );
    }
  };
  //#region 下一部分相关
  const [isNextToPart, setIsNextPart] = useState(false);
  // 下一部分
  const handleToNextPart = () => {
    setIsNextPart(true);
  };
  useEffect(() => {
    if (isNextToPart) {
      setIsNextPart(false);
      // 1、没有下一部分，直接交卷
      if (curQuestionInfo?.partIndex === questionListRef.current?.length - 1) {
        handleSubmitTest(0);
        return;
      }
      // 2、有下一部分，切换到下一部分；
      const curPart = questionListRef.current[curQuestionInfo.partIndex + 1];
      const curItem = curPart?.questions?.[0];
      const groupItemId =
        curItem?.question?.questions_type === 5
          ? curItem?.question?.groupQuestions?.[0]?.id
          : '';
      handleSelectitem(
        curItem,
        0,
        curPart?.id,
        curQuestionInfo.partIndex + 1,
        groupItemId,
      );
    }
  }, [isNextToPart]);
  //#endregion

  // 下一题
  const handleToNextQuestion = () => {
    // 获取下一题
    const nextItem = getPreOrNextItem(false);
    // 1、下一题已做
    // 2、下一题未做
    if (nextItem) {
      const groupItemId =
        nextItem?.item?.question?.questions_type === 5
          ? nextItem?.item?.question?.groupQuestions?.[0]?.id
          : '';

      handleSelectitem(
        nextItem.item,
        nextItem.index,
        nextItem.parentId,
        nextItem.parentIndex,
        groupItemId,
      );
    }
  };
  //#endregion

  //#region 交卷相关
  //   未做完交卷提示
  const [showUnfinishedModal, setShowUnfinishedModal] =
    useState<boolean>(false);

  //   立刻交卷
  const handleSubmitTest = (time?: number) => {
    // if (hasMinSubmitTime.current) {
    //   // 获取剩余时间
    //   message.warning('交卷时间未到');
    //   return;
    // }
    const endTime = dayjs();
    const endDiff = dayjs(endTime).diff(startTime.current, 'ms');
    const partDownTime = partTimeLeftRef.current;

    const params = {
      answerId: testPaperInfoRef.current?.testAnswer?.id,
      countdown: time ?? pageTimeLeftRef.current,
      answerMillisecond: endDiff,
      part: [
        {
          partId: curQuestionInfoRef.current.partQuestionId,
          countdown: partDownTime,
          answerMillisecond: endDiff,
        },
      ],
    };
    const subFunc = (param: any) => {
      examManageApis.postSubmitExam(param).then((res) => {
        if (res.status === 200) {
          testIsOver.current = true;
          message.success('交卷成功');
          // history.push('/examManage');
          history.replace('/stuexam');
        }
      });
    };
    handleSaveItemAnswer(true, () => subFunc(params));
  };
  // 确认交卷
  const handleConfirmSubmit = () => {
    setShowUnfinishedModal(false);
  };

  // 获取当前答题的剩余时间
  const getAnswerLeftTime = () => {
    return contentRef.current?.leftAnswerTime as number;
  };
  // 同步时间
  const handleSyncTime = (page_time?: number, part_time?: number) => {
    const pageTime = page_time ?? pageTimeLeftRef.current;
    const partTime = part_time ?? partTimeLeftRef.current;
    console.log('pageTime', pageTime, 'partTime', partTime);
    // 计算经历得时间
    const cur = dayjs();
    const diffTime = dayjs(cur).diff(startTime.current, 'ms');
    startTime.current = cur;
    examManageApis
      .syncCountdown({
        answerId: testPaperInfoRef.current?.testAnswer?.id,
        countdown: pageTime,
        answerMillisecond: diffTime,
        part: [
          {
            partId: curQuestionInfoRef.current?.partQuestionId,
            countdown: partTime,
            answerMillisecond: diffTime,
          },
        ],
      })
      .then((res: any) => {
        if (res.status === 200) {
          console.log('同步成功');
        }
      });
  };

  //#region 退出时间同步
  // 页面倒计时
  const [timesLeft, pagesTime] = useCountdown({
    leftTime: paperTime.time * 1000,
    targetTime: 9 * 60 * 1000 + 50 * 1000,
    onEnd: () => {
      console.log('倒计时结束');
      handleSubmitTest(0);
    },
    onTarget: () => {
      console.log('倒计时到目标时间');
      setShowSubmitTips(true);
    },
  });
  // 部分倒计时
  const [leftPartTimes, partTimesFormat] = useCountdown({
    leftTime: (partCountDownTime || 0) * 1000,
    onEnd: () => {
      handleToNextPart()
    },
  });

  const pageTimeLeftRef = useRef(0);
  useEffect(() => {
    pageTimeLeftRef.current = timesLeft;
  }, [timesLeft]);
  const partTimeLeftRef = useRef(0);
  useEffect(() => {
    partTimeLeftRef.current = leftPartTimes;
  }, [leftPartTimes]);

  // 离开页面时进行时间同步 获取不到倒计时
  useEffect(() => {
    return () => {
      if (!testIsOver.current) {
        // 保存答案 和 同步时间
        handleSaveItemAnswer(true)
      }
    };
  }, []);

  // 每隔5s进行时间同步,自动保存答案
  useEffect(() => {
    const timer = setInterval(() => {
      if (!testIsOver.current) {
        handleSaveItemAnswer(true)
      }
    }, 5000);
    return () => {
      clearInterval(timer);
    };
  }, []);
  //#endregion

  // 保存
  const handleSaveItemAnswer = (noTips?: boolean, callBack?: () => void) => {
    const answerTime = getAnswerLeftTime();
    let param: any = {
      questionId: curAnsweringItemRef.current?.question?.questionId,
      partQuestionId: curAnsweringItemRef.current?.question?.id,
      answerId: testPaperInfoRef.current?.testAnswer?.id,
      answerTime: answerTime,
      fileUri: [],
      answer: '',
    };
    if (curAnsweringItemRef.current?.question?.questions_type === 5) {
      const groupAnswerArr = answerRef.current?.map((item: any) => {
        return {
          questionId: String(item.questionId),
          answer: item.answer?.join(','),
          fileUri: [],
        };
      });
      param.questionGroupAnswer = uniqueByKey(groupAnswerArr, 'questionId');
    } else {
      param.answer = answerRef.current.join(',');
    }
    handleSyncTime();
    examManageApis.postQuestionItemAnswer(param).then((res) => {
      if (res.status === 200) {
        if (!noTips) {
          message.success('保存成功');
        }
        if (callBack) {
          callBack();
        }
      }
    });
  };

  // 答案改变
  const handleChangeAnswer = (value: any, quesId?: string) => {
    if (!quesId) {
      // setAnswer(value);
      handleChangeImmerValue(value, 'answer');
      answerRef.current = value;
      return;
    }

    const itemIndex = answerRef.current?.findIndex(
      (item) => item?.questionId === quesId,
    );
    let tempList = cloneDeep(answerRef.current);
    if (!tempList) return;
    if (itemIndex > -1) {
      tempList[itemIndex].answer = value;
    } else {
      const newAnswer = { questionId: quesId, answer: value };
      tempList = [...tempList, newAnswer];
    }
    // setAnswer(tempList);
    handleChangeImmerValue(tempList, 'answer');
    answerRef.current = tempList;
    // 答案更改时，如果为题组，还需要修改小题的状态
    if (curQuestionInfo?.groupItemId !== quesId) {
      setCurQuestionInfo({
        ...curQuestionInfo,
        groupItemId: quesId,
      });
      handleChangeImmerValue(
        {
          ...curQuestionInfoRef.current,
          groupItemId: quesId,
        },
        'curQuestionInfo',
      );
      curQuestionInfoRef.current = {
        ...curQuestionInfoRef.current,
        groupItemId: quesId,
      };
    }
  };
  //#endregion
  //#region 收藏
  const handleCollectQuestion = () => {
    examManageApis
      .fetchCollectQuestion({
        id: curQuestionInfo.questionId,
        classifyId: testPaperInfoRef.current?.classifyId,
      })
      .then((res) => {
        if (res.status === 200) {
          message.success('收藏成功');
          // curAnsweringItem.isCollect = curAnsweringItem.isCollect === 0? 1 : 0
        }
      });
  };
  //#endregion
  return (
    <div className="answer_page_container">
      <PageInfoContext.Provider
        value={{
          questionList: questionList,
          ...paperInfo,
        }}
      >
        <TestHeader
          onSubmit={handleSubmitTest}
          onSave={handleSaveItemAnswer}
          answerMinute={paperTime.time}
          pagesTime={pagesTime}
          userInfo={userInfo}
          testName={testPaperInfoRef.current?.name}
          onShowTips={() => setShowSubmitTips(true)}
        />
        <div className="page_content">
          <TitleNumberList
            onChangeItem={handleSelectitem}
          />
          <QuestionContent
            ref={contentRef}
            onNextQuestion={handleToNextQuestion}
            onPreQuestion={hanldeToPreQuesion}
            partTimesLeft={partTimesFormat}
            questionContent={curAnsweringItemRef.current}
            partTime={partCountDownTime}
            itemReadTime={itemReadTime}
            itemAnswerTime={itemAnswerTime}
            answer={answerRef.current}
            isEndReadTime={isEndReadTime.current}
            onAnswerChange={handleChangeAnswer}
            onCollectQuestion={handleCollectQuestion}
            onReadTimeEnd={() => {
              isEndReadTime.current = true;
            }}
            handleChangeNextStatus={() => setIsNeedToNextQuestion(true)}
            onNextPart={handleToNextPart}
            // hasReadTime={hasReadTime.current}
          />
        </div>
      </PageInfoContext.Provider>
      {/* 跳题提示框 */}
      <OrderChangeModal
        isShow={showOrderModal}
        onClose={() => setShowOrderModal(false)}
      />
      {/* 提交提示框 */}
      <UnfinishedModal
        isShow={showUnfinishedModal}
        onClose={() => setShowUnfinishedModal(false)}
        onConfirm={handleConfirmSubmit}
      />
      {/* 还剩多少分钟 */}
      <LeftTimeOverModal
        isShow={showSubmitTips}
        time={timesLeft}
        onClose={() => setShowSubmitTips(false)}
      />
    </div>
  );
};

export default AnswerPage;
