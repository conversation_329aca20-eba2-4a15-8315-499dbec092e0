import React, { FC, useState, useImperativeHandle, useEffect } from 'react';
import { Input, Button, Space, message, Radio, Checkbox } from 'antd';
import { options, radioOptions } from '@/utils'
interface PlanContentProps {
	planContentRef: any;
	list: any;
	btnLoading: boolean
	addTestQuest: (arr: any) => void
	isScrollBottom: boolean;
}
const PlanContent: FC<PlanContentProps> = ({
	planContentRef,
	list,
	btnLoading,
	addTestQuest,
	isScrollBottom
}) => {
	const [topicList, setTopicList] = useState<any>([])
	const containerRef = React.useRef(null)

	useImperativeHandle(planContentRef, () => {
		return {
			open
		}
	});

	useEffect(() => {
		setTopicList([...list])
	}, [list]);

	// 删除
	const deleteTopic = (item: any) => {
		if (btnLoading) {
			message.info('正在生成中，请稍后进行操作')
			return
		}
		const newList = topicList.filter((i: any) => i.id !== item.id)
		setTopicList(newList)
	}

	// 编辑
	const editTopic = (item: any, type: string) => {
		if (btnLoading) {
			message.info('正在生成中，请稍后进行操作')
			return
		}
		// 将数组当前项readonly设置为true
		let newList: any = []
		if (type == 'save') {
			// 添加非空校验
			if (!item.questions_content_Tmp?.trim()) {
				message.warning('题目内容不能为空');
				return;
			}

			if (!item.questions_analysis_Tmp?.trim()) {
				message.warning('解析不能为空');
				return;
			}

			if (item?.questions_type == 2) {
				for (let i = 0; i < item?.questions_answers_Tmp?.length; i++) {
					const option = item?.questions_answers_Tmp[i];
					if (!option) { // 选项格式为 "A. 内容"，所以长度要大于3
						message.warning(`第${i + 1}个空不能为空`);
						return;
					}
				}
			} else {
				if (!item?.questions_answers_Tmp ||
					(Array.isArray(item?.questions_answers_Tmp) && item?.questions_answers_Tmp?.length === 0)) {
					message.warning('答案不能为空');
					return;
				}
			}

			// 检查每个选项内容是否为空
			for (let i = 0; i < item?.questions_options_Tmp?.length; i++) {
				const option = item?.questions_options_Tmp[i];
				if (!option || option.trim().length <= 3) { // 选项格式为 "A. 内容"，所以长度要大于3
					message.warning(`第${i + 1}个选项内容不能为空`);
					return;
				}
			}

			newList = topicList.map((i: any) => {
				if (i.id === item.id) {
					let text = item.questions_content_Tmp + '\n' + '\n'
					item?.questions_options_Tmp?.forEach((ele: string) => {
						text += ele + '\n'
					})
					switch (item.questions_type) {
						case 0: // 单选
							text += '\n' + '正确答案：' + item.questions_answers_Tmp
							break;
						case 1: // 多选
							text += '\n' + '正确答案：' + item.questions_answers_Tmp?.join('')
							break;
						case 2: // 填空
							text += '正确答案：' + item.questions_answers_Tmp?.join(', ') || item.questions_answers_Tmp
							break;
						case 3: // 主观
							text += '正确答案：' + item.questions_answers_Tmp
							break;
						case 4: // 判断
							text += '正确答案：' + ([false, 'false'].includes(item.questions_answers_Tmp) ? '错误' : '正确')
							break;
					}
					text += '\n' + '\n' + '答案解析：' + item.questions_analysis_Tmp
					return {
						...i,
						readOnly: true,
						questions_content: i.questions_content_Tmp,
						questions_analysis: i.questions_analysis_Tmp,
						questions_answers: i.questions_answers_Tmp,
						questions_options: i.questions_options_Tmp,
						content: text
					}
				}
				return i
			})
		} else {
			newList = topicList.map((i: any) => {
				if (i.id === item.id) {
					return {
						...i,
						readOnly: type == 'edit' ? false : true,
						questions_content_Tmp: i.questions_content,
						questions_analysis_Tmp: i.questions_analysis,
						questions_answers_Tmp: i.questions_answers,
						questions_options_Tmp: i.questions_options
					}
				}
				return i
			})
		}
		setTopicList(newList)
	}

	// 滚动到底部
	useEffect(() => {
		if (isScrollBottom) {
			const container: any = containerRef.current;
			if (!container) return;

			const observer = new MutationObserver(() => {
				container.scrollTop = container.scrollHeight;
			});

			observer.observe(container, { childList: true, subtree: true });
			return () => observer.disconnect();
		}
	}, [isScrollBottom]);

	const joinQuestions = (list: any[]) => {
		if (btnLoading) {
			message.info('正在生成中，请稍后加入题库')
			return
		}
		addTestQuest(list)
	}

	const renderType = (type: number) => {
		let name = ''
		for (let i = 0; i < options.length; i++) {
			if (options[i].value === type) {
				name = options[i].questions_type
				break
			}
		}
		return name
	}

	const handleInput = (e: any, item: any, type: string, index?: any, str?: string) => {
		const value = e.target.value
		console.log(e, item, type, index, str)
		const newList = topicList.map((element: any) => {
			if (element.id === item.id) {
				switch (type) {
					case 'title': // 题目
						element.questions_content_Tmp = value
						break;
					case 'analysis': // 解析
						element.questions_analysis_Tmp = value
						break;
					case 'subject': // 主观
						element.questions_answers_Tmp = value
						break;
					case 'fillin': // 填空
						element.questions_answers_Tmp = element.questions_answers_Tmp.map((ele: any, i: number) => {
							if (i === index) {
								return value
							}
							return ele
						})
						break;
					case 'judge': // 判断
						element.questions_answers_Tmp = value
						break;
					case 'selectAnswer': // 选择答案
						element.questions_answers_Tmp = value
						break;
					case 'selectOption': // 选项
						element.questions_options_Tmp = element.questions_options_Tmp.map((ele: any, i: number) => {
							if (i === index) {
								return str + value
							}
							return ele
						})
						break;
				}
				return { ...element }
			}
			return element
		})
		setTopicList(newList)
	}

	const renderOptions = (item: any) => {
		switch (item?.questions_type) {
			case 2: // 填空
				return item?.questions_answers_Tmp?.map((ele: any, index: number) => {
					return <div key={index} className="option">
						<div className="option-left">第{index + 1}空</div>
						<div className="option-right"><Input.TextArea placeholder='请输入答案' onChange={(e) => handleInput(e, item, 'fillin', index)} value={ele} autoSize={{ minRows: 1 }} /></div>
					</div>
				})
			case 3: // 主观
				return <div className="option-right">
					<Input.TextArea placeholder='请输入答案' onChange={(e) => handleInput(e, item, 'subject')} autoSize={{ minRows: 3 }} value={item.questions_answers_Tmp} />
				</div>
			case 4: // 判断
				return <div className="option-radioGroup option-right">
					<Radio.Group options={radioOptions} onChange={(e) => handleInput(e, item, 'judge')} value={item.questions_answers_Tmp} />
				</div>
			case 0: // 单选
				return <Radio.Group onChange={(e) => handleInput(e, item, 'selectAnswer')} value={item.questions_answers_Tmp}>
					<Space direction="vertical">
						{
							item?.questions_options_Tmp?.map((ele: any, index: number) => {
								return <Radio key={index} value={ele.slice(0, 1)}>
									<div className="option-right">
										<div>{ele.slice(0, 1)}</div>
										<Input.TextArea placeholder='请输入选项' onChange={(e) => handleInput(e, item, 'selectOption', index, ele.slice(0, 3))} autoSize={{ minRows: 1 }} value={ele.slice(3)} />
									</div>
								</Radio>
							})
						}
					</Space>
				</Radio.Group>
			case 1: // 多选
				return <Checkbox.Group value={item.questions_answers_Tmp} onChange={(checkedValues: any) => {
					if (checkedValues.length === 0) {
						message.warning('请至少选择一个选项');
						return;
					}
					handleInput({ target: { value: checkedValues } }, item, 'selectAnswer')
				}} >
					<Space direction="vertical">
						{
							item?.questions_options_Tmp?.map((ele: any, index: number) => {
								return <Checkbox key={index} value={ele.slice(0, 1)}>
									<div className="option-right">
										<div>{ele.slice(0, 1)}</div>
										<Input.TextArea placeholder='请输入选项' onChange={(e) => handleInput(e, item, 'selectOption', index, ele.slice(0, 3))} autoSize={{ minRows: 1 }} value={ele.slice(3)} />
									</div>
								</Checkbox>
							})
						}
					</Space>
				</Checkbox.Group>
		}
	}

	const renderLeft = (item: any) => {
		switch (item.questions_type) {
			case 2:
			case 4:
				return '答案：'
			case 3:
				return '解析：'
			default:
				return '选项：'
		}
	}

	console.log(topicList, 'topicList')
	return (
		<div className='plan-content'>
			<div className='right-header'>
				<img src={require('@/assets/img/teachingPlan/ok.png')} />
				{btnLoading ? '题目生成中...' : '题目已生成'}
				{/* <Button onClick={() => joinQuestions(topicList)}>加入题库</Button> */}
			</div>
			<div className='topic-list' ref={containerRef}>
				{topicList.map((item: any) => {
					return <div key={item.id} className='topic-item'>
						<div className='topic-header'>
							<div className="topic-type">{renderType(item.questions_type).slice(0, 1)}</div>
							<div className='topic-title'>{renderType(item.questions_type) + '题'}</div>
							<Space>
								{/* <Button className='btn-primary' onClick={() => joinQuestions([item])}>加入题库</Button> */}
								{
									item.readOnly ? <Button onClick={() => editTopic(item, 'edit')}>编辑</Button> :
										<Button onClick={() => editTopic(item, 'save')}>保存</Button>
								}
								{
									item.readOnly ? <Button onClick={() => deleteTopic(item)}>删除</Button> :
										<Button onClick={() => editTopic(item, 'cancel')}>取消</Button>
								}

							</Space>
						</div>
						<div className="topic-content">
							{
								item.readOnly ? <Input.TextArea
									className={`textarea notShowborder`}
									value={item.content}
									readOnly={item.readOnly}
									placeholder="暂无内容"
									autoSize={{ minRows: 5 }}
								/> : <div className="editContent">
									<div className="title item">
										<div className="theleft">题目：</div>
										<div className="theright"><Input.TextArea placeholder='请输入题目' onChange={(e) => handleInput(e, item, 'title')} value={item.questions_content_Tmp} autoSize={{ minRows: 2 }} /></div>
									</div>
									<div className="options item">
										<div className="theleft">{renderLeft(item)}</div>
										<div className="theright">
											{
												renderOptions(item)
											}
										</div>
									</div>
									<div className="anaylsis item">
										<div className="theleft">{item.questions_type == 3 ? '' : '解析：'}</div>
										<div className="theright"><Input.TextArea placeholder='请输入解析' onChange={(e) => handleInput(e, item, 'analysis')} value={item.questions_analysis_Tmp} autoSize={{ minRows: 3 }} /></div>
									</div>
								</div>
							}
						</div>
					</div>
				})}
			</div>
		</div>
	);
};

export default PlanContent;