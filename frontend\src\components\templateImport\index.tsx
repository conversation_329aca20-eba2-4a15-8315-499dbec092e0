import React, { useEffect, useRef, useState } from 'react'
import { Modal, Button, Upload, message, Progress } from 'antd'
// import reqwest from 'reqwest';
import api from '@/api';
import './index.less'
import examManageApis from '@/api/exam';
import { useSelector } from 'umi';
interface CreateModalProps {
  modalVisible: boolean
  modalClose: () => void
  refresh?: () => void // 刷新
  type?: string
}
const TemplateImport: React.FC<CreateModalProps> = (props) => {
  const { modalClose, modalVisible, type, refresh } = props
  const { parameterConfig, permissions, modules } = useSelector<
  { permission: any },
  any
>(({ permission }) => permission);
  const [fileList, setFileList] = useState<any>([])
  const [uploading, setUploading] = useState(false)
  const [importLock, setImportLock] = useState(false)
  const [resultFlag, setResultFlag] = useState(false)
  const [onfinish, setOnfinish] = useState(false)
  const [importDataPercent, setImportDataPercent] = useState(0)
  const [totalData, setTotalData] = useState(0)
  const [successData, setSuccessData] = useState(0)
  const [failData, setFailData] = useState(0)
  const [filePath, setFailfilePath] = useState('')
  const [modalKey, setModalKey] = useState(0);  // 强制重新渲染
  let timer = useRef()
  useEffect(() => {
    //todo
    if (modalVisible) {
      // checkLock()
    }
  }, [modalVisible])

  const propData = {
    onRemove: (file: any) => {
      const index = fileList.indexOf(file)
      const newFileList = fileList.slice()
      newFileList.splice(index, 1)
      setFileList(newFileList)
    },
    beforeUpload: (file: any) => {
      // setFileList([...fileList, file])
      setFileList([file])
      return false
    },
    fileList,
  }
  const uploadFile = async () => {
    const formData = new FormData()
    fileList.forEach((file: any) => {
      formData.append('file', file)
    })
    setUploading(true)
    setImportDataPercent(0)
    examManageApis.batchImport(formData)
      .then((res: any) => {
        if (res.status === 200) {
          setImportDataPercent(100)
          message.success('上传成功')
          refresh && refresh()
          setOnfinish(true)
          setResultFlag(true)
          setTotalData(res.data.count)
          setSuccessData(res.data.success)
          setFailData(res.data.fail)
          setFailfilePath(res.data.filePath)
          // checkLock()
        } else {
          message.success('上传失败，格式不正确')
          setOnfinish(false)
        }
        setUploading(false)
        // closeModal()
      })
  }
  const closeModal = () => {
    modalClose()
    refresh && refresh()
    setFileList([])
    setResultFlag(false)
    setFailData(0); // 清空失败数据
    setFailfilePath('')
  }
  const checkLock = async () => {
    // console.log('diyici')
    let param = ''
    // const res = await user.checkImport(param);
    // if (res && res.errorCode === 'success') {
    //   setImportLock(res.extendMessage);
    //   setResultFlag(res.extendMessage);
    //   // setImportLock(true);
    //   if(res.extendMessage){
    //     timerInterval()
    //   }
    // }
  }
  //定时器
  // const timerInterval = () =>{
  //   let interval:any = setInterval(()=>checkPercent(),1000)
  //   timer.current= interval;
  // }
  // const checkPercent = async ()=>{
  //   let param = ''
  //   // const ress = await user.checkImportProgess(param);
  //   const ress = await user.checkImportProgess(param);
  //   if (ress && ress.errorCode === 'success') {
  //     console.log('定时器111111')
  //     if(ress.extendMessage.status === 'doing'){
  //       setImportDataPercent(ress.extendMessage.percentage)
  //       setOnfinish(false)
  //     }else{
  //       setImportDataPercent(ress.extendMessage.percentage)
  //       setImportLock(false);
  //       clearInterval(timer.current)
  //       setOnfinish(true)
  //       setTotalData(ress.extendMessage.total)
  //       setSuccessData(ress.extendMessage.success)
  //       setFailData(ress.extendMessage.fail)
  //     }
  //   }
  // }
  const resetImportStatus = () => {
    setFileList([]); // 清空文件列表
    setUploading(false); // 重置上传状态
    setImportDataPercent(0); // 重置进度
    setTotalData(0); // 清空总数据
    setSuccessData(0); // 清空成功数据
    setFailData(0); // 清空失败数据
    setFailfilePath('')
    setResultFlag(false); // 重置结果标志
    setOnfinish(false); // 重置完成标志
  };

  const handleStepBack = () => {
    resetImportStatus(); // 重置所有状态
    setModalKey(modalKey + 1); // 强制重新渲染 modal
  };
  return (
    <Modal
      key={modalKey} // 通过 key 强制更新
      destroyOnClose
      title={'试题导入'}
      visible={modalVisible}
      closable={false}
      className="add-custom-plan"
      footer={[
        // <Button
        //   key="submit"
        //   type="primary"
        //   onClick={uploadFile}
        //   disabled={fileList.length === 0}
        //   loading={uploading}
        // >
        //   确定
        // </Button>,
        failData != 0 && (
          <Button
            key="submit"
            type="primary"
            onClick={handleStepBack}
          >
            上一步
          </Button>
        ),
        <Button
          key="submit"
          type="primary"
          onClick={resultFlag ? closeModal : uploadFile}
          disabled={fileList.length === 0 && !resultFlag}
          loading={uploading}
        >
          {!resultFlag ? '确定' : '完成'}
        </Button>,
        <Button key="back" onClick={closeModal}>
          取消
        </Button>,
      ]}
    >

      <div className="template-import">
        {
          failData == 0 && <Upload {...propData} className="upload">
            <Button disabled={importLock}>选择文件</Button>
          </Upload>
        }
        {
          resultFlag &&
          <div className="importProgess">
            <span> {failData > 0 ? '下载导入失败信息' : '当前正在导入数据，请稍后...'} </span>
            {/* <Progress percent={importDataPercent}/> */}
            <Progress
              percent={importDataPercent}
              status={failData > 0 ? 'exception' : 'normal'}  // 如果 failData > 0，设置为红色
            />
            {
              onfinish && (
                <div className='finishResult'>
                  <span>执行行数：{totalData}</span>
                  <span>成功行数：{successData}</span>
                  <span>失败行数：{failData}</span>
                  <a style={{ color: 'red' }} href={filePath} download='下载导入失败信息'>下载导入失败信息</a>
                </div>
              )
            }
          </div>
        }
        <div>
          注：若没有模版，请先
          {/* {type ==='/basic/user/student'? */}
          {/* <a href='/exam/static/excel/topicTemplate.xlsx' download='试题导入模板'>下载模板</a> */}
          <a
            href={parameterConfig.target_customer !== 'ppsuc'
              ? '/exam/static/excel/topicTemplate.xlsx'
              : '/exam/static/excel/topicTemplategongan.xlsx'}
            download='试题导入模板'
          >
            下载模板
          </a>
        </div>
        
      </div>
    </Modal>
  )
}
export default TemplateImport
