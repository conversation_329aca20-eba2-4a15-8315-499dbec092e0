import examManageApis from '@/api/exam';
import TeacherItem from '@/components/select/teacherItem';
import { PlusOutlined } from '@ant-design/icons';
import {
  AutoComplete,
  Col,
  Input,
  InputRef,
  Row,
  Select,
  Tag,
  Tooltip,
  TreeSelect,
} from 'antd';
import Form from 'antd/lib/form';
import debounce from 'lodash/debounce';
import {
  FC,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useSelector } from 'umi';
import useInitFormValue from '../../hooks/useInitFormValue';
import './index.less';

const BasicInfo: FC<any> = forwardRef(({ detail }, ref) => {
  const major = useSelector<any, any>((state: any) => {
    return state.globalData.major;
  });
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => {
    return {
      ...form,
      knowledgeValue,
      knowledgeValue2,
      directionName,
      classifyName,
      tags,
      questionSourceCode,
      applicationSet,
    };
  });

  const [inputVisible, setInputVisible] = useState(false);

  //#region 初始化的部分
  const { initusers, initClassify, initQuestionSource } = useInitFormValue();

  useEffect(() => {
    if (initusers?.length > 0) {
      form.setFieldsValue({
        share_users: initusers,
      });
    }
  }, [initusers]);
  //#endregion

  //#region 应用分类
  // 选择的分类值
  const [classifyName, setClassifyName] = useState('');
  // 选择的方向值
  const [directionName, setDirectionName] = useState('');
  // 题目来源code
  const [questionSourceCode, setQuestionSourceCode] = useState('');
  // 应用分类集合
  const [applicationSet, setApplicationSet] = useState<any>([]);
  // 分类列表
  const classifyOptions = useMemo(() => {
    return initClassify.map((item: any) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  }, [initClassify]);
  const [directionOptions, setDirectionOptions] = useState<any>([]);
  useEffect(() => {
    const optList: any = initClassify?.[0]?.children?.map((item: any) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    setDirectionOptions(optList);
  }, [initClassify]);
  // 应用改变
  const handleChangeApplication = (value: string, opt: any) => {
    setApplicationSet(
      opt.map((item: any) => ({
        applicationClassCode: item.value,
        applicationClassName: item.label,
      })),
    );
    const curList =
      initClassify.find((item: any) => item.id === value) ||
      initClassify?.[0] ||
      {};
    setClassifyName(curList?.name || '');
    const optList: any = curList?.children?.map((item: any) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    setDirectionOptions(optList || []);
  };
  //#endregion

  //#region 标签
  const inputRef = useRef<InputRef>(null);
  const [inputValue, setInputValue] = useState('');
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };
  const handleInputConfirm = () => {
    if (inputValue && tags.indexOf(inputValue) === -1) {
      setTags([...tags, inputValue]);
    }
    setInputVisible(false);
    setInputValue('');
  };
  const showInput = () => {
    setInputVisible(true);
  };
  //#endregion

  //#region
  const [editInputIndex, setEditInputIndex] = useState(-1);
  const editInputRef = useRef<InputRef>(null);
  const [editInputValue, setEditInputValue] = useState('');

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value);
  };
  const handleEditInputConfirm = () => {
    const newTags = [...tags];
    newTags[editInputIndex] = editInputValue;
    setTags(newTags);
    setEditInputIndex(-1);
    setInputValue('');
  };
  //#endregion

  const handleClose = (removedTag: string) => {
    const newTags = tags.filter((tag) => tag !== removedTag);

    setTags(newTags);
  };

  //#region
  const [tags, setTags] = useState<string[]>([]);
  useEffect(() => {
    console.log(detail, 'detail');

    let temp: any;
    if (detail) {
      if (detail?.knowledge_points || detail?.questionCourseList) {
        let arr2 = detail.questionCourseList?.map(
          (item: any) => item.courseId || item.courseId,
        );
        setKnowledgeSelect2(arr2);
        let obj2 = detail?.questionCourseList?.map((item: any) => {
          return {
            value: item.courseId,
            label: item.courseName,
            data: item,
          };
        });
        setKnowledgeValue2(obj2);
        setKnowledgeList2(obj2);
        let arr = detail?.knowledge_points?.map(
          (item: any) => item.entity_id || item.entity,
        );
       
        setKnowledgeSelect(arr);
        let obj = detail?.knowledge_points?.map((item: any) => {
          return {
            value: item.entity_id,
            label: item.entity,
            title: item.mapName + '/' + item.parentNode + '/' + item.entity,
            data: item,
          };
        });
        setKnowledgeValue(obj);
        setKnowledgeList(obj);
        setQuestionSourceCode(detail.questionSourceCode);
        setApplicationSet(detail?.applicationClasses || []);
        console.log(obj2, 'arr');
        console.log(obj, 'arr');
        form.setFieldsValue({
          ...detail,
          share_users: detail.share_users ? detail.share_users : [],
          knowledge: arr || [],
          knowledgeyis: arr2 || [],
        });
      }
      setTags(detail.labels || []);
    }
    //
    // if (detail?.applicationLibMap) {
    //   const caseItemMap = detail.applicationLibMap
    //   Object.keys(caseItemMap).map(keyStr => {
    //     caseMap.current.set(keyStr, caseItemMap[keyStr])
    //   })
    // }
  }, [detail]);
  //#endregion

  //#region 知识点选择
  // 知识点选择
  const [knowledgeSelect, setKnowledgeSelect] = useState<any>([]);
  // 搜索知识点下拉框的数据
  const [knowledgeList, setKnowledgeList] = useState<any>([]);
  // 选择知识节点的值
  const [knowledgeValue, setKnowledgeValue] = useState<any>([]);
  // 检索知识点
  const fetchKnowledgeGraph = debounce((name: any) => {
    examManageApis.fetchKnowledgeGraph(name).then((res: any) => {
      if (res.status == 200) {
        setKnowledgeList(
          res.data.map((item: any) => {
            return {
              value: item.id,
              label: item.entity,
              title: item.mapName + '/' + item.parentNode + '/' + item.entity,
              data: item,
            };
          }),
        );
      }
    });
  }, 200);
  //#endregion

  // 在知识点相关状态后添加课程相关状态
  const [knowledgeSelect2, setKnowledgeSelect2] = useState<any>([]);
  // 搜索知识点下拉框的数据
  const [knowledgeList2, setKnowledgeList2] = useState<any>([]);
  // 选择知识节点的值
  const [knowledgeValue2, setKnowledgeValue2] = useState<any>([]);
  // 添加课程搜索方法（需与后端对接）
  const fetchKnowledgeGraph2 = debounce((name: any) => {
    examManageApis.courseApiGraph(name).then((res: any) => {
      if (res.error_msg == 'Success') {
        setKnowledgeList2(
          res.extend_message.results.map((item: any) => {
            return {
              value: item.course_id,
              label: item.course_name,
              title: item.course_name,
              data: item,
            };
          }),
        );
      }
    });
  }, 200);

  // 遍历目录树
  const forTree = (tree: any) => {
    return tree?.map((item: any) => {
      return {
        // key: item.categoryCode + ',' + item.categoryName,
        title: item.categoryName,
        value: item.categoryCode + ',' + item.categoryName,
        children: item.children ? forTree(item.children) : [],
      };
    });
  };
  return (
    <div className="content_container">
      <Form
        name="topic_form"
        form={form}
        style={{ width: '100%', height: '100%' }}
        labelAlign="left"
      >
        <div className="form_item">
          <div className="form_item_header">
            <span className="tag"></span>
            <span>基本信息</span>
          </div>
          <div className="form_item_body">
            <Row style={{ width: '100%' }}>
              <Col span={11}>
                <Form.Item name="questionSourceName" label="题目来源">
                  <AutoComplete
                    style={{ width: '100%' }}
                    onChange={(value: any, opt: any) => {
                      setQuestionSourceCode(opt?.labelCode || '');
                    }}
                    options={(initQuestionSource || []).map((item: any) => ({
                      value: item.questionSourceName,
                      label: item.questionSourceName,
                      labelCode: item.questionSourceCode,
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col span={11}>
                <Form.Item name="assessmentCode" label="考核方向">
                  <Select
                    style={{ width: '100%' }}
                    options={directionOptions}
                    onChange={(_, opt: any) => {
                      setDirectionName(opt?.label);
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row style={{ width: '100%' }}>
              <Col span={11}>
                <Form.Item name={'questions_difficulty'} label={'难度'}>
                  <Select
                    style={{ width: '100%' }}
                    options={[
                      { value: 1, label: 1 },
                      { value: 2, label: 2 },
                      { value: 3, label: 3 },
                      { value: 4, label: 4 },
                      { value: 5, label: 5 },
                    ]}
                  ></Select>
                </Form.Item>
              </Col>
              <Col span={11}>
                <Form.Item name={'questions_major'} label="适用院系/部门">
                  <TreeSelect
                    style={{ width: '100%' }}
                    treeData={forTree(major)}
                    maxTagCount={3}
                    // key={'categoryCode'}
                    // onChange={onProfessionChange}
                    treeCheckable={true}
                    placeholder={''}
                    allowClear={true}
                    showSearch
                    treeNodeFilterProp="title"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row style={{ width: '100%' }}>
              <Col span={11}>
                <Form.Item name={'questions_level'} label="适用层次">
                  <Select
                    style={{ width: '100%' }}
                    mode="multiple"
                    options={[
                      { value: '0', label: '本科生' },
                      { value: '1', label: '研究生' },
                    ]}
                  ></Select>
                </Form.Item>
              </Col>
              <Col span={11}>
                {/* <Form.Item name={'questions_lesson'} label="适用课程">
                  <Input maxLength={50} showCount />
                </Form.Item> */}
                <Form.Item label="适用课程" name="knowledgeyis">
                  <Select
                    showSearch
                    style={{ width: '100%' }}
                    mode="tags" //multiple
                    value={knowledgeSelect2}
                    placeholder="请输入适用课程"
                    filterOption={false}
                    onSearch={(e: any) => {
                      fetchKnowledgeGraph2(e);
                    }}
                    onChange={(e: any, info: any) => {
                      let arr: any = [];
                      if (info.length) {
                        info.forEach((item: any, index: number) => {
                          if (item.data) {
                            arr.push(item);
                          } else {
                            arr.push({
                              data: {
                                entity: e[index],
                              },
                              label: e[index],
                              title: e[index],
                              value: null,
                            });
                          }
                        });
                      }
                      setKnowledgeSelect2(e);
                      setKnowledgeValue2(arr);
                    }}
                    notFoundContent={null}
                    options={knowledgeList2}
                  />
                </Form.Item>
                
              </Col>
            </Row>
            <Row style={{ width: '100%' }}>
              <Col span={11}>
                <TeacherItem
                  multiple={true}
                  required={false}
                  message={''}
                  label="分享给"
                  name={'share_users'}
                  // edit={detail?true:false}
                  selectKeys={initusers}
                  key={JSON.stringify(initusers)}
                />
              </Col>
              <Col span={11}>
                <Form.Item
                  name="labels"
                  label="标签"
                  // hidden={true}
                  className="tagItem"
                >
                  {tags?.map((tag, index) => {
                    if (editInputIndex === index) {
                      return (
                        <Input
                          ref={editInputRef}
                          key={tag}
                          size="small"
                          className="tag-input"
                          value={editInputValue}
                          onChange={handleEditInputChange}
                          onBlur={handleEditInputConfirm}
                          onPressEnter={handleEditInputConfirm}
                        />
                      );
                    }
                    const tagElem = (
                      <Tag
                        className="edit-tag"
                        key={tag}
                        // closable={index !== 0}
                        closable={true}
                        onClose={() => handleClose(tag)}
                      >
                        <span
                          onDoubleClick={(e) => {
                            if (index !== 0) {
                              setEditInputIndex(index);
                              setEditInputValue(tag);
                              e.preventDefault();
                            }
                          }}
                        >
                          {tag.length > 20 ? `${tag.slice(0, 20)}...` : tag}
                        </span>
                      </Tag>
                    );
                    return tag.length > 20 ? (
                      <Tooltip title={tag} key={tag}>
                        {tagElem}
                      </Tooltip>
                    ) : (
                      tagElem
                    );
                  })}
                  {inputVisible && (
                    <Input
                      ref={inputRef}
                      type="text"
                      size="small"
                      className="tag-input"
                      value={inputValue}
                      onChange={handleInputChange}
                      onBlur={handleInputConfirm}
                      onPressEnter={handleInputConfirm}
                    />
                  )}
                  {!inputVisible && (
                    <Tag className="site-tag-plus" onClick={showInput}>
                      <PlusOutlined /> 新标签
                    </Tag>
                  )}
                </Form.Item>
              </Col>
            </Row>
            <Row style={{ width: '100%' }}>
              <Col span={11}>
                <Form.Item label="知识点" name="knowledge">
                  <Select
                    showSearch
                    style={{ width: '100%' }}
                    mode="tags" //multiple
                    value={knowledgeSelect}
                    placeholder="请输入知识点"
                    filterOption={false}
                    onSearch={(e: any) => {
                      fetchKnowledgeGraph(e);
                    }}
                    onChange={(e: any, info: any) => {
                      let arr: any = [];
                      if (info.length) {
                        info.forEach((item: any, index: number) => {
                          if (item.data) {
                            arr.push(item);
                          } else {
                            arr.push({
                              data: {
                                entity: e[index],
                              },
                              label: e[index],
                              title: e[index],
                              value: null,
                            });
                          }
                        });
                      }
                      setKnowledgeSelect(e);
                      setKnowledgeValue(arr);
                    }}
                    notFoundContent={null}
                    options={knowledgeList}
                  />
                </Form.Item>
              </Col>
              <Col span={11}>
                <Form.Item label="认知层次" name="cognitive_level">
                  <Select
                    options={[
                      { value: '记忆', label: '记忆' },
                      { value: '理解', label: '理解' },
                      { value: '应用', label: '应用' },
                      { value: '分析', label: '分析' },
                      { value: '评价', label: '评价' },
                      { value: '创新', label: '创新' },
                    ]}
                    style={{ width: '100%' }}
                  ></Select>
                </Form.Item>
              </Col>
            </Row>
            <Row style={{ width: '100%' }}>
              <Col span={11} />
              <Col span={11}>
                <Form.Item label="应用分类" name="applicationClassCode">
                  <Select
                    options={classifyOptions}
                    style={{ width: '100%' }}
                    onChange={handleChangeApplication}
                    mode="multiple"
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
        </div>
      </Form>
    </div>
  );
});

export default BasicInfo;
