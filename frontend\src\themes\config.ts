interface IConfig {
  [propsName: string]: {
    favicon: string;
    variables: {
      [propsName: string]: string;
    };
  };
}

const themeCfg: IConfig = {
  default: {
    favicon: '/rman/static/favicon.ico',
    variables: {
      '@primary-color': '#4C9BEC',
      '@second-color': '#285C97',
    },
  },
  scu: {
    favicon: '/rman/static/favicon_scu.ico',
    variables: {
      '@primary-color': '#F29402',
      '@second-color': '#BA2A17',
    },
  },
};
export default themeCfg;
