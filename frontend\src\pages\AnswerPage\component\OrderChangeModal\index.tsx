import { Button, Modal } from 'antd';
import { FC, useEffect, useRef, useState } from 'react';
import './index.less';

export interface IOrderModalProps {
  isShow: boolean;
  onClose: () => void;
}

import { ExclamationCircleFilled } from '@ant-design/icons';

const OrderChangeModal: FC<IOrderModalProps> = ({ isShow, onClose }) => {
  const [modalCloseTime, setModalCloseTime] = useState<number>(3);
  const timer = useRef<any>(null);

  useEffect(() => {
    if (!isShow) {
      setModalCloseTime(3);
      if (timer.current) {
        clearInterval(timer.current);
      }
      return;
    }
    if (timer.current) {
      clearInterval(timer.current);
    }
    timer.current = setInterval(() => {
      setModalCloseTime((prev) => prev - 1);
    }, 1000);
    return () => {
      clearInterval(timer.current);
    };
  }, [isShow]);
  useEffect(() => {
    if (modalCloseTime === 0) {
      onClose();
    }
  }, [modalCloseTime]);

  return (
    <Modal
      title=""
      open={isShow}
      onOk={onClose}
      onCancel={onClose}
      footer={
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Button type="primary" onClick={onClose}>确定({modalCloseTime})</Button>
        </div>
      }
    >
      <div className="order_modal_container">
        <ExclamationCircleFilled
          style={{
            color: 'red',
            width: '36px',
            height: '36px',
            fontSize: '36px',
          }}
        />
        <div style={{ marginTop: 20 }}>请按题号顺序答题！</div>
        <div>不允许自行选择板块进</div>
      </div>
    </Modal>
  );
};

export default OrderChangeModal;
