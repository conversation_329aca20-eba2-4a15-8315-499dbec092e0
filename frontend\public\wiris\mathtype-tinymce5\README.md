# MathType for TinyMCE V5

Type and handwrite mathematical notation with MathType.

Easily include quality math equations in your documents and digital content.

## Table of Contents

- [Install instructions](#install-instructions)
- [Known issues](#known-issues)
- [Services](#services)
- [Documentation](#documentation)
- [Displaying on Target Page](#displaying-on-target-page)
- [Privacy policy](#privacy-policy)

## Install instructions

1. Install the npm module:

   ```bash
   npm install @wiris/mathtype-tinymce5
   ```

2. Add the plugin as an external plugin:

   ```js
   tinymce.init({
     external_plugins: {
      'tiny_mce_wiris': `node_modules/@wiris/mathtype-tinymce5/plugin.min.js`,
     }
   });
   ```


3. Add MathType buttons to the TinyMCE5 toolbar and the recommended settings:

   ```js
   tinymce.init({
    toolbar: 'tiny_mce_wiris_formulaEditor tiny_mce_wiris_formulaEditorChemistry',

    // We recommend to set 'draggable_modal' to true to avoid overlapping issues
    // with the different UI modal dialog windows implementations between core and third-party plugins on TinyMCE.
    // @see: https://github.com/wiris/html-integrations/issues/134#issuecomment-905448642
    draggable_modal: true,
 
    // You could set a different language for MathType editor:
    // language: 'fr_FR',
    // mathTypeParameters: {
    //   editorParameters: { language: 'fr' },
    // },

   });
   ```

   Notice the example assumes this directory structure:

   ```
    └───index.html
    └───node_modules
        └───@wiris/mathtype-tinymce5
   ```

## Known issues

* The editor's caret is lost when inserting a new formula on Safari with ChemType [#486](https://github.com/wiris/html-integrations/issues/486) 

## Services

This npm module uses remotely hosted services to render MathML data. However, we recommend you install these services on your backend. This will allow you, among other things, to configure the service and to locally store the images generated by MathType.

The services are available for Java, PHP, .NET and Ruby on Rails. If you use any of these technologies, please download the plugin for your backend technology from [here](https://store.wiris.com/en/products/downloads/mathtype/integrations).

In order to install the plugin along with the correspondent services, please follow the [TinyMCE5 install instructions](https://github.com/wiris/mathtype-integration-js-dev/blob/master/doc/src/services_tinymce.md).

## Displaying on Target Page

In order to display mathematical formulas on the target page, i.e. the page where content produced by the HTML editor will be visible, the target page needs to include the [MathType script](https://docs.wiris.com/en/mathtype/mathtype_web/integrations/mathml-mode#add_a_script_to_head). For example for the default setting this would be:
```html
<script src="https://www.wiris.net/demo/plugins/app/WIRISplugins.js?viewer=image"></script>
```

## Documentation

To find out more information about MathType, please go to the following documentation:

* [Install instructions](http://docs.wiris.com/en/mathtype/mathtype_web/integrations/html/tinymce)
* [MathType documentation](http://docs.wiris.com/en/mathtype/mathtype_web/start)
* [Introductory tutorials](http://docs.wiris.com/en/mathtype/mathtype_web/intro_tutorials)
* [Service customization](http://docs.wiris.com/en/mathtype/mathtype_web/integrations/config-table)
* [Testing](http://docs.wiris.com/en/mathtype/mathtype_web/integrations/html/plugins-test)

## Privacy policy

The [MathType Privacy Policy](http://www.wiris.com/mathtype/privacy-policy) covers the data processing operations for the MathType users. It is an addendum of the company’s general Privacy Policy and the [general Privacy Policy](https://wiris.com/en/privacy-policy) still applies to MathType users.
