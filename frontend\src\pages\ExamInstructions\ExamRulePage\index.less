.rule_container {
  width: 100vw;
  height: 100vh;
  //   background-color: #f7f9fa;
  .docx-view {
    width: 100vw;
    height: calc(100vh - 60px);
    padding: 20px;
    background-color: #f7f9fa;
    .content {
      width: 100%;
      height: 100%;
      border-radius: 10px;
      background-color: #fff;
      overflow: auto;
      //   padding-top: 26px;
    }
  }
  :global(#word-container) {
    width: 100%;
    height: 100%;
    overflow: auto;
    padding-top: 26px !important;
    .docx-wrapper {
      section.docx {
        width: 100% !important;
      }
    }

    [class^='docx-wrapper'] {
      background-color: transparent !important;
      padding: 0 !important;
      box-shadow: none !important;
    }
  }
}
