const routes = [
  {
    path: '/',
    component: '@/layout/guideLayout/index',
    routes: [{
      path: '/',
      component: '@/pages/index',
      title: 'home.title',
      redirect: '/exam',
    },
    {
      path: '/exam',
      component: '@/pages/index',
      title: 'home.title',
      routes: [
        {
          exact: true,
          path: '/exam/topicManage',
          component: '@/pages/topicManagement',
          title: 'home.title'
        },
        {
          exact: true,
          path: '/exam/paperManage',
          component: '@/pages/paperManagement',
          title: 'home.title'
        },
        {
          exact: true,
          path: '/exam/testManagementment',
          component: '@/pages/testManagementment',
          title: 'home.title',
        },
        {
          exact: true,
          path: '/exam/ContractManagement',
          component: '@/pages/ContractManagement',
          title: 'home.title'
        },
        {
          exact: true,
          path: '/exam/recycle',
          component: '@/pages/ExamRecycle',
          title: 'home.title'
        }
      ]
    },
    {
      exact: true,
      path: '/FavoritesPage',
      component: '@/pages/FavoritesPage',
      title: 'home.title',
    },
    {
      path: '/ExamModule/Details',
      component: '@/pages/ExamModule/Details',
      title: 'home.title',
    },
    {
      path: '/questionSetting',
      component: '@/pages/QuestionSetting',
      title: 'home.title',
    },
    {
      path: '/ExamModule/Revise',
      component: '@/pages/ExamModule/Revise',
      title: 'home.title',
    },
    {
      path: '/ExamModule/Detailspage',
      component: '@/pages/ExamModule/Revise/compoents/Detailspage',
      title: 'home.title',
    },

    {
      path: '/testManagementment/Test',
      component: '@/pages/testManagementment/TestManagementPage',
      title: 'home.title',
    },
    {
      exact: true,
      path: '/entitys',
      component: '@/pages/testManagementment/TestManagementPage/Entitys',
      title: 'home.title'
    },
    {
      path: '/ExamModule/Detailspage',
      component: '@/pages/ExamModule/Revise/compoents/Detailspage',
      title: 'home.title',
    },
    {
      path: '/ExamModule/Answer',
      component: '@/pages/ExamModule/Revise/compoents/Answer',
      title: 'home.title',
    },
    {
      path: '/ExamModule/ExamDetails',
      component: '@/pages/ExamModule/Details/compoents/ExamDetails',
      title: 'home.title',
    },
    {
      path: '/topic/manage',
      component: '@/pages/newTopic',
      title: 'home.title',
    },
    {
      path: '/paper/manage',
      component: '@/pages/newPaper',
      title: 'home.title',
    },
    {
      path: '/group/manage',
      component: '@/pages/NewQuestionGroup',
      title: '题组'
    },
    {
      path: '/stuexam',
      component: '@/pages/StartExam',
      title: '开始测验',
    },
    {
      path: '/answerPage',
      component: '@/pages/AnswerPage',
      title: '答题'
    },
    {
      path: '/testsystem',
      component: '@/pages/TestSystem',
      title: '测验系统'
    },
    {
      path: '/testreport',
      component: '@/pages/TestReport',
      title: '报告'
    },
    {
      path: '/reportdetail',
      component: '@/pages/TestReport/TestReportDetail',
      title: '报告详情'
    },
    {
      path: '/examinstructions',
      component: '@/pages/ExamInstructions',
      title: '考试说明'
    },
    {
      path: '/examrules',
      component: '@/pages/ExamInstructions/ExamRulePage',
      title: '考试规则'
    },
    {
      path: '/reportdetailpage',
      component: '@/pages/TestReport/TestReportDetailPage',
      title: '报告详情页'
    },
    { component: '@/pages/404', title: '404' }]
  }

];

export default routes;
