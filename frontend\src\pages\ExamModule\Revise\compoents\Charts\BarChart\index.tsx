import React, { useEffect, useRef } from "react";
import * as echarts from "echarts";

const BarChart = ({ data }) => {
  const chartRef = useRef(null);

  useEffect(() => {
    // 初始化图表
    const chart = echarts.init(chartRef.current);

    // 设置图表配置
    const option = {
      tooltip: {
        trigger: "item",
        formatter: function (params: { name: any; value: any; }) {
          return `${params.name}: ${params.value}%`;
        },
        textStyle: {
          fontSize: 12, // 调整字体大小
          lineHeight: 20, // 调整行高
        },
        confine: true, // 限制 tooltip 在图表容器内
      },

      xAxis: {
        type: "category",
        data: data.map((item, index) => index + 1),  // 使用 data 中的 name 字段
      },
      yAxis: {
        type: "value",
      },
      lineStyle: {
        type: 'dashed', // 'solid' 表示实线，'dashed' 表示虚线，'dotted' 表示点线
        width: 2, // 线条宽度
      },
      grid: {
        left: '2%', // 调整左侧留白宽度
        right: '0%', // 调整右侧留白宽度
        bottom: '3%', // 可以同时调整底部留白（可选）
        containLabel: true, // 确保标签不会被裁剪
      },
      series: [
        {
          data: data.map((item: { value: any; name: any; }) => ({
            value: item.value,
            name: item.name,  // 保证每个数据项都包含 name 和 value
          })),
          type: "bar",
          itemStyle: {
            color: '#1890FF',
            borderRadius: [10, 10, 0, 0],
          },
          barWidth: '14px',
        },
      ],
    };

    // 渲染图表
    chart.setOption(option);

    // 监听窗口大小变化，调整图表大小
    window.addEventListener("resize", () => {
      chart.resize();
    });

    // 清除事件监听器
    return () => {
      window.removeEventListener("resize", () => {
        chart.resize();
      });
    };
  }, [data]); // 每次 data 变化时重新渲染

  return <div ref={chartRef} style={{ width: "100%", height: "300px" }}></div>;
};

export default BarChart;
