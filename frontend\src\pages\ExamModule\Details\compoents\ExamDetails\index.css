.exampage {
  background: #F7F9FA;
  min-height: 100vh;
  height: 100%;
  /* index.less */
  /* 重置焦点样式 */
  /* 自定义滚动条 */
  /* 滚动条滑块 */
}
.exampage .wave-line {
  text-decoration: underline wavy red;
}
.exampage .auto-img {
  overflow: hidden;
}
.exampage .auto-img img {
  max-width: 900px;
  height: auto;
  vertical-align: bottom;
}
.exampage .ant-card {
  border-radius: 10px;
}
.exampage .scflow {
  overflow-y: scroll;
  height: 86vh;
}
.exampage .yul_scflow {
  overflow-y: auto;
  position: relative;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
}
.exampage .highlight {
  text-decoration: underline wavy red;
  /* 波浪线 */
  text-decoration-thickness: 2px;
  /* 线的厚度 */
  color: inherit;
  /* 保持文字颜色一致 */
}
.exampage .card {
  background: #FFFFFF;
  border-radius: 10px;
  padding: 20px 10px;
  position: relative;
  max-height: calc(90vh - 20px);
  min-height: calc(90vh - 20px);
  overflow-y: auto;
  cursor: pointer;
}
.exampage .card .card_name {
  display: flex;
  justify-content: space-between;
  padding-bottom: 5px;
  border-bottom: 1px dashed #C5C5C5;
  margin-bottom: 20px;
  padding-bottom: 10px;
}
.exampage .card .card_name .timu_name {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #2A2A2A;
}
.exampage .card .card_name .timu_name .name_padding {
  padding-right: 8px;
}
.exampage .card .card_name .timu_name .blues {
  color: #549CFF;
}
.exampage .card .card_name .name_cr {
  display: flex;
}
.exampage .card .card_name .name_cr .cr {
  display: flex;
  align-items: center;
  padding: 4px;
}
.exampage .card .card_name .name_cr .name_bor {
  width: 16px;
  height: 16px;
  margin-left: 4px;
  border-radius: 4px;
  border: 1px solid #549CFF;
}
.exampage .card .card_name .name_cr .reds {
  border: 1px solid #DD0F0F;
}
.exampage .card .bt_card .select_title {
  border-bottom: 1px dashed #C5C5C5;
  padding-bottom: 10px;
  margin-top: 20px;
}
.exampage .card .bt_card .select_title .title_sz {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #2A2A2A;
}
.exampage .card .bt_card .bt_flex {
  display: flex;
  flex-wrap: wrap;
  max-width: 100%;
  margin: 19px auto;
}
.exampage .card .bt_card .bt_flex .border-blue {
  color: #549CFF;
  border: 1px solid #549CFF;
  /* 蓝色边框 */
}
.exampage .card .bt_card .bt_flex .border-red {
  color: #DD0F0F;
  border: 1px solid #DD0F0F;
  /* 红色边框 */
}
.exampage .card .bt_card .bt_flex .bts {
  width: 36px;
  height: 36px;
  line-height: 40px;
  margin: 7px;
  border-radius: 6px;
  text-align: center;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.exampage .card .flexs {
  position: sticky;
  top: -20px;
  z-index: 999;
  background-color: #fff;
  padding: 10px 20px 21px 20px;
}
.exampage .card .middle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px dashed #C5C5C5;
  padding-bottom: 10px;
}
.exampage .card .middle .middle_name .fw {
  font-weight: 500;
  font-size: 16px;
  color: #2A2A2A;
}
.exampage .card .middle .middle_name .fw400 {
  font-weight: 400;
  font-size: 14px;
}
.exampage .card .middle .middle_name .dlename {
  font-family: PingFangSC, PingFang SC;
}
.exampage .card .middle .middle_name .mato {
  margin: 10px 0;
}
.exampage .card .middle .name_point {
  font-family: PingFangSC, PingFang SC;
  font-size: 14px;
  color: #549CFF;
}
.exampage .card .middle .fs {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 20px;
  color: #549CFF;
  line-height: 26px;
  text-align: left;
}
.exampage .card .question {
  margin-top: 20px;
}
.exampage .card .questions_list .answers {
  margin-top: 22px;
}
.exampage .card .questions_list .answers .type_other {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #549CFF;
  margin-top: 20px;
}
.exampage .card .questions_list .answers .type {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #919191;
  margin-top: 20px;
}
.exampage .card .questions_list .answers .type .fs {
  font-size: 13px;
  padding-left: 10px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #303030;
}
.exampage .card .questions_list .answers .type .color30 {
  color: #303030;
}
.exampage .card .questions_list .answers .type .color52 {
  color: #525252;
}
.exampage .card .questions_list .answers .type_xz {
  margin: 20px 15px;
}
.exampage .card .questions_list .answers .type_xz .answer_item {
  display: flex;
  align-items: flex-start;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 20px 0;
}
.exampage .card .questions_list .answers .type_xz .answer_item .radio_content {
  font-size: 14px;
}
.exampage .card .questions_list .answers .type_xz .answer_item .spcialDom {
  flex: 1 1;
}
.exampage .card .questions_list .answers .type_xz .answer_item .spcialDom img {
  max-width: 50px;
  height: auto;
  vertical-align: bottom;
}
.exampage .card .questions_list .answers .type_xz .blanks > span {
  margin-right: 10px;
}
.exampage .card .questions_list .form_item_header {
  position: relative;
  display: flex;
  align-items: stretch;
  margin-top: 20px;
}
.exampage .card .questions_list .form_item_header .tag {
  margin-right: 10px;
  display: inline-block;
  width: 4px;
  height: 23px;
  background: var(--primary-color);
}
.exampage .card .questions_list .form_item_header .span_left {
  display: flex;
  align-items: center;
}
.exampage .card .Record {
  margin: 10px 0;
  background: #F7F9FA;
  border-radius: 6px;
  padding: 10px;
}
.exampage .card .Record .scyangs {
  display: flex;
  justify-content: flex-end;
}
.exampage .card .Record .Record_box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.exampage .card .Record .Record_box .box_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.exampage .card .Record .Record_box .box_title .ant-image .ant-image-img {
  width: 38px !important;
  height: 38px !important;
  border-radius: 50%;
}
.exampage .card .Record .Record_box .box_title .title {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
}
.exampage .card .Record .Record_box .box_icon {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.exampage .card .box_nr {
  margin-top: 6px;
}
.exampage .card .Answer .Answer_item {
  margin: 20px 0;
}
.exampage .enclosure {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}
.exampage .enclosure.hidden {
  height: 0;
  overflow: hidden;
}
.exampage .enclosure .ant-upload {
  width: 0;
  overflow: hidden;
}
.exampage .enclosure > .label {
  white-space: nowrap;
}
.exampage .enclosure > span {
  display: flex;
}
.exampage .enclosure > span .ant-upload-list {
  margin-left: 10px;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
}
.exampage .enclosure > span .ant-upload-list .ant-upload-list-item {
  margin: 0 !important;
}
.exampage .ant-image-preview-img {
  z-index: 99;
  width: 200px !important;
  height: 200px !important;
}
.exampage .rich {
  position: relative;
  top: 50%;
  width: 90%;
}
.exampage .review-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid #549CFF;
  border-radius: 17px;
  cursor: pointer;
  z-index: 1;
  padding: 6px 50px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #549CFF;
}
.exampage .add_button {
  border-radius: 17px;
  padding: 0 26px;
  font-family: PingFangSC, PingFang SC;
  font-size: 14px;
  color: #FFFFFF;
}
.exampage .rich-text-editor {
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  cursor: text;
  height: 155px;
  padding: 10px;
  overflow-y: scroll;
}
.exampage .rich-text-editor::-webkit-scrollbar {
  width: 4px;
  /* 设置滚动条宽度 */
}
.exampage .rich-text-editor::-webkit-scrollbar-thumb {
  background: #D8D8D8;
  border-radius: 6px;
}
