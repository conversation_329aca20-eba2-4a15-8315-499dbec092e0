import React, { FC, useEffect, useRef, useState } from 'react';
import { Slider } from 'antd';
import { IGlobal } from '@/models/global';
import { PauseOutlined, CaretRightOutlined } from '@ant-design/icons';
import { useSelector, history } from 'umi';

const Audio: FC<Entity.IBaseEntity> = ({ src, onError, finishStatus, isAutoplay = false, onListener }) => {

  const { location } = history;
  const audioRef = useRef<any>(null);
  const { courseDetail } = useSelector<Models.Store, any>(
    (state) => state.moocCourse)
  const { userInfo } = useSelector<any, IGlobal>((state) => state.global);
  const [playStatus, setPlayStatus] = useState(false);
  const [durationAudio, setDurationAudio] = useState(0);
  const [sliderValue, setSliderValue] = useState(0);
  const [sliderText, setSliderText] = useState<string>('');

  const userOnlyStudent = (userInfo.roles.length === 1 && userInfo.roles[0]?.roleCode === 'r_student') || location?.query?.isJoin === 'true';
  const entityData = courseDetail?.entityData ?? { ratio: 0 };
  let listenerState = false; // 确保onListener方法只调用一次

  // 数字转为时间格式
  const changeTimeByNum = (sec: number): string => {
    let hour: any = Math.floor(sec / 3600);
    let minute: any = Math.floor((sec - hour * 3600) / 60);
    let second: any = sec - hour * 3600 - minute * 60;
    // if (hour < 10) {
    hour = parseInt(hour, 10);
    // }
    // if (minute < 10) {
    minute = parseInt(minute, 10);
    // }
    if (second < 10) {
      second = "0" + parseInt(second, 10);
    } else {
      second = parseInt(second, 10);
    }
    return hour > 0 ? hour + ":" + minute + ":" + second : minute + ":" + second;
  }

  const percentComplete = (listenerState: boolean): boolean => {
    let status = listenerState;
    // 音频
    if (audioRef) {
      // 音频总时长
      const videoDuration = audioRef.current.duration || 0;
      // 音频播放时长
      const videoCurrentTime = audioRef.current.currentTime || 0;

      setSliderValue((videoCurrentTime / videoDuration) * 100);
      setSliderText(`${changeTimeByNum(videoCurrentTime)}/${changeTimeByNum(videoDuration)}`);

      if (!entityData?.ratio) {
        videoCurrentTime === videoDuration && onListener && onListener('ended');
        status = true;
      }

      if (entityData?.ratio && videoCurrentTime / videoDuration >= entityData?.ratio / 100 && !status) {
        onListener && onListener('ended');
        status = true;
      }
    }

    return status;
  }

  const timeUpdate = (e: any) => {
    const status = percentComplete(listenerState);
    listenerState = status;
  }

  const changePlayStatus = () => {
    const data = !playStatus;
    setPlayStatus(data);
  }

  // 暂停
  const pauseAudio = () => {
    audioRef.current.pause();
  }

  // 播放
  const playAudio = () => {
    audioRef.current.play();
  }

  useEffect(() => {
    setPlayStatus(false);
  }, [src])

  return (
    <div>
      <audio src={src} onError={onError} ref={audioRef} preload="preload" onPause={changePlayStatus} onPlay={changePlayStatus} autoPlay={isAutoplay} controls={!userOnlyStudent || finishStatus || !entityData.allowed_drag} onTimeUpdate={timeUpdate} />
      <div style={{ display: !userOnlyStudent || finishStatus || !entityData.allowed_drag ? 'none' : 'flex', alignItems: 'center' }}>
        {playStatus ? <PauseOutlined onClick={pauseAudio} /> : <CaretRightOutlined onClick={playAudio} />}
        <Slider value={sliderValue} disabled style={{ width: 200, margin: '0 10px' }} />
        <span>{sliderText}</span>
      </div>
    </div>
  );
}
export default Audio;