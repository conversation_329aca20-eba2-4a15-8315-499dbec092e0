.content_view {
  position: relative;
  width: 100%;
  height: 100%;

  .help_btn {
    position: fixed;
    right: 0;
    bottom: 40px;
    width: 70px;
    height: 65px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 2px rgb(129 129 129 / 6%);
    border-radius: 1px;
    border: 1px solid #f1f1f1;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: #868686;
    z-index: 9999;
    cursor: pointer;

    .help_icon {
      background-color: var(--primary-color);
      width: 25px;
      height: 18px;
      line-height: 18px;
      color: #fff;
      border-radius: 4px;
      position: relative;
      margin-bottom: 8px;
      font-weight: bold;

      &::before {
        content: '';
        position: absolute;
        bottom: -6px;
        border-top: 6px solid transparent;
        border-left: 6px solid var(--primary-color);
        border-bottom: 6px solid transparent;
        left: 0;
      }
    }
  }

  .helpperview {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 9999;
    background-color: #fff;

    .content_perview {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.45);

      .imgdom {
        height: 100%;
        width: 100%;
      }

      .options_view {
        position: absolute;
        width: 20%;
        left: 40%;
        height: 50px;
        bottom: 30px;

        display: flex;
        align-items: center;
        justify-content: space-evenly;
      }
    }
  }
}

.helpDrawer_view {
  .desc_view {
    width: 100%;
    margin-bottom: 30px;

    .desc_span {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #525252;
      line-height: 22px;
      margin-bottom: 30px;
    }
  }

  .zhiyin {
    width: 100%;
    background-color: #f5f5f5;
    margin-bottom: 10px;
    border-radius: 10px;

    .item_view {
      width: 95%;
      margin-left: 5%;
      display: flex;
      align-items: center;

      img {
        width: 20px;
        height: 20px;
      }

      span {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #549cff;
        line-height: 35px;
        margin-left: 5px;
        cursor: pointer;
      }
    }
  }

  .content_box{
      position: relative;
      height:90%;
      width: 100%;
      padding-right: 10px;
      overflow-y: auto;
      // 滚动条样式优化
      &::-webkit-scrollbar {
          width: 7px;
          height: 7px;
      }
      &::-webkit-scrollbar-thumb {
          border-radius: 10px;
          background-color: rgba(0, 0, 0, 0.2);
      }
      &::-webkit-scrollbar-track {
          border-radius: 10px;
          background-color: rgba(0, 0, 0, 0.1);
      }
  }

  .mulu {
    width: 100%;
    background-color: #f5f5f5;
    margin-bottom: 10px;
    padding-bottom: 10px;
    padding-top: 10px;
    border-radius: 10px;

    .mulu_name_span {
      margin-left: 10px;
      line-height: 35px;
    }

    .mulu_item_view {
      width: 95%;
      margin-left: 5%;

      img {
        width: 20px;
        height: 20px;
      }

      span {
        width: 100%;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #549cff;
        line-height: 25px;
        margin-left: 5px;
        cursor: pointer;

        &:hover {
          // 下划线
          text-decoration: underline;
        }
      }
    }
  }

  .noshow{
    position: absolute;
    bottom: 20px;
    width: 85%;
    text-align: center;
  }
}
@media screen and (max-width: 768px) {
  #root {
    .content_view {
      height: 100%;
      .help_btn {
        display: none;
      }
      .uf-exam-layout-wrapper {
        height: 100%;
        overflow: hidden;
        .uf-exam-layout-content {
          height: calc(100% - 52px);
          .mobileContainer {
            width: 100%;
            background-color: #ffffff;
            .ant-btn {
              padding: 3px 10px;
            }
            .ant-tabs {
              padding: 0 20px;
              .ant-tabs-nav {
                margin: 0 0 10px 0;
              }
            }
            .content {
              height: calc(100% - 56px);
              .topic_manage,.paper_manage {
                .search_box {
                  margin: 0 10px !important;
                  #topic_form,#paper_form {
                    .ant-input-group {
                      display: flex;
                      margin-right: 3%;
                      .ant-input-group-wrapper {
                        border-radius: 0 !important;
                        .ant-input-affix-wrapper {
                          border-radius: 0 !important;
                        }
                        .ant-input-group-addon {
                          width: unset;
                          display: flex;
                          align-items: center;
                        }
                      }
                    }
                  }
                }
                .split_line {
                  margin: 10px;
                  width: auto !important;
                }
                .content {
                  margin: 0 !important;
                  .opt_btn {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 0 10px;
                    .ant-checkbox-wrapper {
                      .ant-checkbox + span {
                        padding: 0 0 0 5px;
                        white-space: nowrap;
                      }
                    }
                    >.ant-btn{
                      padding: 4px 10px;
                    }
                    .mobile_btns {
                      > div {
                        .anticon {
                          margin-right: 8px;
                        }
                        &:not(:last-child){
                            margin-bottom: 10px;
                        }
                      }
                    }
                  }
                  .ant-checkbox-group{
                    padding: 4px 10px;
                    .item{
                      padding: 10px;
                      .topic{
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        p{
                          display: inline;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
