:global .topic_manage .content {
  background-color: red !important;
}
:global .topic_manage .content .ant-table-wrapper .ant-spin-container {
  background-color: red !important;
}
:global .ant-table-expanded-row-fixed {
  position: relative !important;
}
.group_expand {
  background-color: red !important;
}
.group_expand .ant-table-expanded-row-fixed {
  position: relative !important;
}
.topic_manage {
  height: calc(100% - 40px);
}
.topic_manage .search_box {
  margin: 20px 20px 0px 20px;
}
.topic_manage .search_box #topic_form {
  display: flex;
}
.topic_manage .search_box #topic_form .form_select {
  flex: 0 0 10%;
}
.topic_manage .search_box #topic_form .form_select_major {
  flex: 1;
}
.topic_manage .search_box #topic_form .form_input {
  flex: 0 0 14%;
}
.topic_manage .search_box #topic_form > .ant-form-item {
  margin-right: 30px;
  border-radius: 4px;
}
.topic_manage .search_box #topic_form > .ant-form-item.last {
  margin-right: 0;
}
.topic_manage .search_box #topic_form .ant-btn {
  height: 32px;
  margin-right: 20px;
  border-radius: 4px;
  margin: 0;
}
.topic_manage .search_box #topic_form .moreSearch {
  position: fixed;
  top: 0;
  left: 0;
  transition: top 0.5s;
  background: white;
  z-index: 1000;
  width: 100%;
  padding: 5px 5%;
}
.topic_manage .search_box #topic_form .moreSearch.none {
  top: -100%;
}
.topic_manage .search_box #topic_form .moreSearch .head {
  margin: 20px 0;
  text-align: center;
}
.topic_manage .search_box #topic_form .moreSearch .head > span:first-child {
  font-size: 18px;
}
.topic_manage .search_box #topic_form .moreSearch .head .anticon {
  position: absolute;
  right: 7%;
  line-height: 30px;
}
.topic_manage .search_box #topic_form .moreSearch .public_group {
  margin-top: 20px;
}
.topic_manage .search_box #topic_form .moreSearch .public_group > .item {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 20px;
}
.topic_manage .search_box #topic_form .moreSearch .public_group > .item > div {
  width: 100%;
}
.topic_manage .search_box #topic_form .moreSearch .public_group .ant-picker-time-panel {
  display: none;
}
.topic_manage .search_box #topic_form .moreSearch .btns {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  flex-wrap: nowrap;
  margin-bottom: 10px;
}
.topic_manage .split_line {
  width: 100%;
  height: 1px;
  border-bottom: 2px #F7F9FA solid;
}
.topic_manage .content {
  height: calc(100% - 120px);
  margin: 0px 30px;
}
.topic_manage .content .opt_btn {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
}
.topic_manage .content .opt_btn button.ant-btn.ant-btn-text:not(:last-child) {
  margin: 0;
}
.topic_manage .content .opt_btn .disabled {
  color: rgba(0, 0, 0, 0.25);
}
.topic_manage .content .opt_btn .item_ {
  padding: 4px 15px;
  position: relative;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.topic_manage .content .opt_btn .item_:hover {
  color: var(--primary-color);
}
.topic_manage .content .opt_btn .item_:hover .ant-btn {
  color: var(--primary-color);
}
.topic_manage .content .opt_btn .item_.disabled {
  cursor: no-drop;
  color: rgba(0, 0, 0, 0.25) !important;
}
.topic_manage .content .opt_btn .item_.disabled .ant-btn {
  color: rgba(0, 0, 0, 0.25) !important;
}
.topic_manage .content .opt_btn .item_ > span:last-child {
  margin-left: 8px;
}
.topic_manage .content .opt_btn .item_:not(:last-child)::after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 16px;
  background: #d8d8d8;
  right: 0;
  top: 8px;
  position: absolute;
}
.topic_manage .content .opt_btn .ant-btn {
  margin-right: 14px;
  display: flex;
  justify-items: center;
  align-items: center;
  margin: 0;
}
.topic_manage .content .opt_btn .ant-btn span.anticon {
  font-size: 16px;
}
.topic_manage .content .opt_btn .ant-btn.ant-btn-primary {
  border-radius: 16px;
}
.topic_manage .content .opt_btn .ant-btn-link:not(:last-child)::after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 16px;
  background: #d8d8d8;
  right: 0;
  top: 8px;
  position: absolute;
}
.topic_manage .content .ant-table-wrapper .table_opt {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
}
.topic_manage .content .ant-table-wrapper .table_opt .ant-btn {
  padding: 0;
}
.topic_manage .content .ant-table-wrapper .ant-table-body {
  overflow-x: hidden;
}
.topic_manage .content .ant-table-wrapper .ant-table-body .ant-table-row .ant-table-cell p {
  margin-bottom: 0;
}
.topic_manage .content .ant-table-wrapper .ant-table-body .ant-table-row .ant-table-cell .spcialDom {
  max-height: 100px;
  overflow: hidden;
}
.topic_manage .content .ant-table-wrapper .ant-table-body .ant-table-row .ant-table-cell .spcialDom img {
  max-width: 50px;
  height: auto;
  vertical-align: bottom;
}
.topic_manage .content .ant-table-wrapper .ant-table-body .ant-table-row .ant-table-cell .auto-img {
  max-height: 100px;
  overflow: hidden;
}
.topic_manage .content .ant-table-wrapper .ant-table-body .ant-table-row .ant-table-cell .auto-img img {
  height: auto;
  vertical-align: bottom;
}
.topic_manage_mobile {
  height: calc(100% - 30px) !important;
}
.topic_manage_mobile .content {
  height: 100%;
}
.topic_manage_mobile .content .opt_btn {
  margin-bottom: 10px !important;
}
.topic_manage_mobile .content .list {
  height: calc(100% - 37px);
}
.topic_manage_mobile .content .list .ant-checkbox-group {
  height: 100%;
  width: 100%;
  overflow-y: auto;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item {
  margin-bottom: 10px;
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.07);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid #eee;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .head {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  white-space: nowrap;
  color: #272727;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 15px;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .head .type {
  font-weight: bold;
  margin-left: -4px;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .head .topic {
  max-height: 44px;
  width: calc(100% - 72px);
  overflow: hidden;
  text-overflow: ellipsis;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .head .topic > p {
  margin-bottom: 0;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .answer,
.topic_manage_mobile .content .list .ant-checkbox-group .item .analysis {
  font-weight: bold;
  font-size: 14px;
  line-height: 16px;
  padding: 0 0 8px 15px;
  color: #525252;
  max-height: 44px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  white-space: nowrap;
  overflow: hidden;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .answer > div > p,
.topic_manage_mobile .content .list .ant-checkbox-group .item .analysis > div > p {
  margin-bottom: 0;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .analysis {
  border-bottom: 1px solid #f0f0f0;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .info > div {
  display: flex;
  align-items: center;
  margin-top: 8px;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .info > div > div {
  width: 50%;
  display: flex;
  align-items: center;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .info > div > div > label {
  margin-right: 5px;
  color: #9d9d9d;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .info > div > div > label::after {
  content: ':';
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .info > div > div > span {
  width: calc(100% - 65px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .bottom {
  margin: 15px 0 0;
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  align-items: center;
}
.topic_manage_mobile .content .list .ant-checkbox-group .item .bottom .ant-btn {
  border-color: var(--primary-color);
  color: var(--primary-color);
}
.topic_manage_mobile .content .pagination {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: center;
}
