.answer {
  background: #F7F9FA;
}
.answer .auto-img {
  overflow: hidden;
}
.answer .auto-img img {
  max-width: 100%;
  height: auto;
  vertical-align: bottom;
  transform: scale(1);
  transform-origin: center;
}
.answer .header {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #E3E3E3;
  justify-content: space-between;
  align-items: center;
}
.answer .header .comeback {
  padding-left: 34px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.answer .header .fs {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #4A4F64;
  line-height: 22px;
  text-align: left;
  font-style: normal;
}
.answer .header .statisticsContainer {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 20px;
  color: #282C3C;
  line-height: 30px;
  margin-right: 100px;
}
.answer .scflow {
  overflow-y: scroll;
  height: calc(100vh - 100px);
}
.answer .new_scflow {
  overflow-y: auto;
  /* 仅允许子容器滚动 */
  position: relative;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
}
.answer .new_scflow::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Edge */
}
.answer .yul_scflow {
  overflow-y: auto;
  position: relative;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
}
.answer .new_scflow::-webkit-scrollbar {
  display: none;
}
.answer .other-class {
  display: flex;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #549CFF;
}
.answer .Content .collect {
  padding: 20px 174px;
  height: calc(100vh - 100px);
  overflow: hidden;
  /* 父容器禁止滚动 */
  position: relative;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
}
.answer .Content .Preview {
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 100px;
  padding-right: 100px;
}
.answer .Content .topic {
  background: #FFFFFF;
  border-radius: 10px;
}
.answer .Content .topic .contents {
  display: flex;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-bottom: 20px;
}
.answer .Content .topic .topic_title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #2A2A2A;
  line-height: 20px;
}
.answer .Content .topic .tiao {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #2A2A2A;
  line-height: 24px;
  margin: 20px 0;
}
.answer .Content .topic .topic_Content .type {
  margin: 20px 0;
}
.answer .Content .topic .topic_Content .types {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #2A2A2A;
  line-height: 20px;
  margin: 20px 0;
}
.answer .Content .topic .topic_Content .answers {
  margin: 15px 0;
  margin-left: 10px;
  display: inline-block;
}
.answer .Content .topic .topic_Content .answers .ant-radio-group {
  display: flex;
  gap: 40px;
}
.answer .Content .topic .topic_Content .answers .ant-checkbox-group {
  display: flex;
  gap: 40px;
}
.answer .Content .topic .topic_Content .answers .answer_item {
  display: flex;
  align-items: flex-start;
  flex-direction: row;
  margin-bottom: 10px;
}
.answer .Content .topic .topic_Content .answers .answer_item .radio_content {
  font-size: 14px;
}
.answer .Content .topic .topic_Content .answers .answer_item .spcialDom {
  flex: 1 1;
}
.answer .Content .topic .topic_Content .answers .answer_item .spcialDom img {
  max-width: 50px;
  height: auto;
  vertical-align: bottom;
}
.answer .Content .topic .topic_Content .answers .blanks > span {
  margin-right: 10px;
}
.answer .Content .topic .topic_Content .footer {
  width: 100%;
  background: #F7F9FA;
  border-radius: 4px;
  padding: 10px;
}
.answer .Content .topic .topic_Content .footer .aws {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #2A2A2A;
  line-height: 20px;
}
.answer .Content .topic .topic_Content .footer .blus {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 14px;
  color: #549CFF;
}
.answer .Content .topic .topic_Content .footer .analysis {
  margin-top: 20px;
  color: #525252;
  font-size: 14px;
  font-family: PingFangSC, PingFang SC;
}
.answer .Content .card::-webkit-scrollbar {
  display: none;
}
.answer .Content .card {
  background: #FFFFFF;
  border-radius: 10px;
  padding: 20px;
  position: relative;
  width: 100%;
  overflow: auto;
}
.answer .Content .card .bt_card .select_title {
  border-bottom: 1px dashed #C5C5C5;
  padding-bottom: 10px;
}
.answer .Content .card .bt_card .select_title .title_sz {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #2A2A2A;
}
.answer .Content .card .bt_card .bt_flex {
  display: flex;
  flex-wrap: wrap;
  max-width: 100%;
  margin: 19px auto;
}
.answer .Content .card .bt_card .bt_flex .blues {
  color: #549CFF;
  font-size: 17px;
}
.answer .Content .card .bt_card .bt_flex .border-blue {
  color: #549CFF;
  border: 1px solid #549CFF;
  /* 蓝色边框 */
}
.answer .Content .card .bt_card .bt_flex .border-red {
  color: #DD0F0F;
  border: 1px solid #DD0F0F;
  /* 红色边框 */
}
.answer .Content .card .bt_card .bt_flex .bts {
  width: 36px;
  height: 36px;
  line-height: 40px;
  margin: 7px;
  border-radius: 6px;
  text-align: center;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
