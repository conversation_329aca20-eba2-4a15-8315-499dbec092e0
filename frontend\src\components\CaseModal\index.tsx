import { Button, Modal } from "antd";
import { FC, useEffect, useRef, useState } from "react";
import "./index.less";

interface IProps {
  open: boolean;
  onOk: (id: string, name: string, caseId: string) => void;
  onClose: () => void;
}

const CaseModal: FC<IProps> = ({ open, onOk, onClose }) => {
  // const location: any = useLocation();

  const childRef = useRef<any>();

  const [loading, setLoading] = useState<boolean>(true);

  const receiveMessage = (e: any) => {
    try {
      if (e.origin === window.location.origin) {
        if (typeof e.data === 'string') {
          const { action, id, name, caseId } = JSON.parse(e.data);
          if (action === "addCaseForId") {
            onOk(id, name, caseId);
          } else if (action === "addFail") {
            setLoading(false);
          }
        }
      }
    } catch (e) {
      setLoading(false);
    }

  };
  useEffect(() => {
    window.addEventListener("message", receiveMessage, false);
    return () => {
      window.removeEventListener("message", receiveMessage, false);
    };
  }, []);
  const hashChange = (e: any) => {
    if (e.target.location.hash.includes("selectCase")) {
      setLoading(false);
    } else {
      setLoading(true);
    }
  };
  useEffect(() => {
    if (open) {
      setTimeout(() => {
        childRef.current?.contentWindow?.addEventListener("hashchange", hashChange);
      }, 1000);
    }
    return () => {
      childRef.current?.contentWindow?.removeEventListener("hashchange", hashChange);
    };
  }, [open]);
  const handleOk = () => {
    try {
      setLoading(true);
      childRef.current?.contentWindow.postMessage(JSON.stringify({ action: 'addCase' }),
        window.location.origin);
    } catch (e) {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return <Modal
    destroyOnClose
    // confirmLoading={loading}
    title="案例库"
    open={open}
    width={1240}
    wrapClassName="case-modal"
    // onOk={handleOk}
    onCancel={handleCancel}
    footer={
      <>
        <Button onClick={handleCancel}>取消</Button>
        <Button disabled={loading} type="primary" onClick={handleOk}>确定</Button>
      </>
    }
  >
    <iframe ref={childRef} className="case-iframe" src="/medicalcaselib/#/caseList"></iframe>
  </Modal>;
};

export default CaseModal;