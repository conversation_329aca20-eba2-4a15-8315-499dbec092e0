/**
 * 获取一个递归函数,处理流式数据
 * @param reader  流数据
 * @param stream  是否是流式数据
 */
export const getWrite = (reader: any, stream: boolean, callback: (content: any, config: any, finish: boolean, newChatInfo?: any) => void) => {
    let tempResult = '';
    let responseText = '';
    let configTools: any = null;
    let firstChatInfo: any = null;
    /**
     *
     * @param done  是否结束
     * @param value 值
     */
    const write_stream = ({ done, value }: { done: boolean; value: any }) => {
        try {
            if (done) {
                return
            }
            const decoder = new TextDecoder('utf-8')
            let str = decoder.decode(value, { stream: true })
            // let str2 = str.replace(/retry:\d+/g, '').trim();
            // 这里解释一下 start 因为数据流返回流并不是按照后端chunk返回 我们希望得到的chunk是data:{xxx}\n\n 但是它获取到的可能是 data:{ -> xxx}\n\n 总而言之就是 fetch不能保证每个chunk都说以data:开始 \n\n结束
            tempResult += str
            // 填空
            // const split = [
            //     "data:{\"answer\":[\"理解\",\"应用\"],\"analysis\":\"这道题目考察的是认知层次理论中的理解和应用阶段。理解阶段是指个体对信息的组织和解释过程，是在感知和记忆之后的信息处理阶段。应用阶段则是指个体能够在新的情境中使用已学习的知识解决问题或完成任务的能力。\",\"questions_type\":2,\"content\":\"在学习新知识的过程中，我们首先通过感官接收信息，然后经过大脑的处理进入______阶段，在此阶段信息被整合并与已有知识相连，最后才能在新的情境中______这些知识。\"}\nretry:1000\n\n"
            // ]
            // 判断
            // const split = [
            //     "data:{\"answer\":false,\"analysis\":\"线粒体和叶绿体中的DNA属于细胞质遗传物质，其分配方式为随机分配而非纺锤体介导的均等分配。这些细胞器通过母系遗传或随机分裂的方式传递给子细胞，与核DNA的精确分配机制不同。\",\"questions_type\":4,\"content\":\"细胞分裂过程中，线粒体和叶绿体中的DNA也会通过纺锤体牵引实现均等分配。\"}\nretry:1000\n\n"
            // ]
            // 主管题
            // const split = [
            //     "data:{\"answer\":\"有丝分裂过程中，遗传物质经过精确复制后平均分配到两个子细胞中，保证了亲子代细胞遗传信息的稳定性，维持了生物个体发育过程中细胞数量的增长和功能的延续。而减数分裂通过同源染色体的联会、交叉互换以及两次连续的分裂，使配子中的染色体数目减半，增加了遗传物质的重组机会，为后代带来遗传多样性，是生物有性生殖的基础。\",\"analysis\":\"有丝分裂的特点是染色体复制一次，细胞分裂一次，遗传物质均等分配，确保体细胞遗传稳定性；减数分裂染色体复制一次但细胞分裂两次，染色体数目减半，通过同源染色体重组产生遗传变异。这种差异使得有丝分裂支持个体生长和组织修复，而减数分裂为物种进化提供遗传基础。\",\"questions_type\":3,\"content\":\"请阐述细胞有丝分裂与减数分裂在遗传物质分配上的主要区别，并说明这种差异对生物个体发育和遗传多样性的意义。\"}\nretry:1000\n\n"
            // ]
            // 多选
            // const split = [
            //     "data:{\"answer\":[\"B\",\"D\"],\"options\":[\"A. 将代码写得尽可能复杂以减少代码量\",\"B. 建立清晰的代码结构和层次\",\"C. 频繁修改软件架构以适应微小需求变化\",\"D. 编写详细的文档说明软件功能和实现逻辑\"],\"analysis\":\"建立清晰的代码结构和层次可以让代码更易于理解和修改；编写详细的文档能为后续维护人员提供重要参考。将代码写得复杂反而会增加理解和维护的难度；频繁修改软件架构容易导致混乱和不稳定，不利于维护。\",\"questions_type\":1,\"content\":\"以下哪些做法有助于提高软件的可维护性？\"}\nretry:1000\n\n"
            // ]
            // 单选
            // const split = [
            //     "data:{\"answer\":\"B\",\"options\":[\"A. 队列\",\"B. 栈\",\"C. 链表\",\"D. 树\"],\"analysis\":\"栈是一种遵循后进先出（LIFO）原则的数据结构，这意味着最后添加的元素将首先被移除。这与队列（FIFO）、链表和树的结构特性不同，因此栈是最适合实现LIFO操作的数据结构。\",\"questions_type\":0,\"content\":\"在计算机科学中，以下哪种数据结构最适合用于实现后进先出（LIFO）的操作？\"}\nretry:1000\n\n"
            // ]
            const split = tempResult.match(/data:.*}\nretry:1000\n\n/g)
            console.log(split, 'splitsplitsplitsplit')
            if (split) {
                str = split.join('')
                tempResult = tempResult.replace(str, '')
            } else {
                return reader.read().then(write_stream)
            }
            // 这里解释一下 end
            if (str && str.startsWith('data:')) {
                if (split) {
                    for (const index in split) {
                        let chunk1 = split[index].replace(/retry:\d+/g, '').trim();
                        let chunk = JSON?.parse(chunk1.replace('data:', ''));
                        if (chunk?.choices) { // 为了适配upfrom大模型测试对话接口的结构数据
                            chunk = {
                                content: chunk?.choices?.[0]?.delta?.content,
                                event: chunk?.choices?.[0]?.finish_reason === 'stop' ? 'finish' : 'add',
                                role: chunk?.choices?.[0]?.delta?.role,
                                record_id: chunk?.id,
                                upform: true,
                            }
                        }
                        const content = chunk || '';

                        // console.info('chunk', 'chunk', {chunk})
                        if (chunk && (chunk?.role === 'assistant' || !chunk?.role)) {
                            responseText += content;
                        }

                        configTools = chunk;

                        if (chunk && chunk?.role === 'tools' && chunk.content?.content?.type === "chat_info") {
                            firstChatInfo = chunk.content.content.chat_info || {};
                        }
                        callback(content, configTools, false, firstChatInfo);
                        if (chunk.event === 'finish') {
                            // 流处理成功 返回成功回调
                            callback(responseText || '', configTools, true, firstChatInfo);
                            return Promise.resolve()
                        }
                    }
                }
            }
        } catch (e) {
            return Promise.reject(e)
        }
        return reader.read().then(write_stream)
    }
    /**
     * 处理 json 响应
     * @param param0
     */
    const write_json = ({ done, value }: { done: boolean; value: any }) => {
        if (done) {
            const result_block = JSON.parse(tempResult)
            if (result_block.code === 500) {
                return Promise.reject(result_block.message)
            } else {
                if (result_block.content) {
                    // console.info('content', 'content', result_block.content)
                }
            }
            return
        }
        if (value) {
            const decoder = new TextDecoder('utf-8')
            tempResult += decoder.decode(value)
        }
        return reader.read().then(write_json)
    }
    return stream ? write_stream : write_json
}