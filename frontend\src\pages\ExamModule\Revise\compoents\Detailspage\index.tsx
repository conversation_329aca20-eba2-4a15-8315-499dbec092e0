import React, { useEffect, useState } from "react";
import { Breadcrumb, Card, Col, Row, Table, Image } from "antd";
import { ArrowLeftOutlined, ClockCircleOutlined, LeftCircleFilled, TrophyOutlined, UserOutlined } from "@ant-design/icons";
import './index.less';
import ExamLayout from '@/pages'; // 路径根据你的项目结构调整
import { useLocation, history } from "umi";
import Contract from "@/api/Contract";
import { reduce } from "lodash";
import examType from '@/types/examType';
import RenderHtml from "@/components/renderHtml";






const Detailspage = () => {

    const location = useLocation();
    const [recordData, setRecordData] = useState({});
    const [titleName, settitleName] = useState();
    const [TableData, setTableData] = useState([]);
    const [pagination, setPagination] = useState({
        page: 1,  // 当前页码
        size: 10, // 每页显示的数量
    });
    const [pagtotal, setPagtotal] = useState();

    const removeMathML = (html: string): string => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        // 移除数学公式相关的元素
        const mathElements = doc.querySelectorAll('.math-tex, mjx-container');
        mathElements.forEach((el) => el.remove());
        return doc.body.textContent || '';  // 获取去除数学公式后的纯文本内容
    };

    const columns = [
        {
            title: "组成部分",
            dataIndex: "part",
            key: "part",
            align: 'center',
            width: '10%',
            render: (item: any) => (
                <div>
                    {item.name}
                </div>
            ),
        },
        {
            title: "序号",
            dataIndex: "index",
            key: "index",
            align: 'center',
            width: '6%',
            render: (t4ext: any, record: any, index: any) => {
                return index + 1; // 显示从 1 开始的序号
            }
        },
        {
            title: "题目类型",
            dataIndex: "questions_type",
            key: "questions_type",
            align: 'center',
            width: '12%',
            render: (item: any, record: any) => (
                <div>{examType.optionType_[Number(item)]}</div>
            )
        },
        {
            title: "题目内容",
            dataIndex: "questions_content",
            key: "questions_content",
            align: 'center',
            render: (value: any) => {
                const extractedText = removeMathML(value);
                return <RenderHtml cname="auto-img" value={extractedText || '\u200B'} />;
            }
            // render: (value: any) => (
            //     <RenderHtml cname="spcialDom" value={value}></RenderHtml>
            // ),
        },
        {
            title: "答案",
            dataIndex: "questions_answers",
            key: "questions_answers",
            align: 'center',
            width: '10%',
            render: (value: any) =>
                value?.map((item: any, index: number) => (
                    <RenderHtml key={index} cname="auto-img" value={item}></RenderHtml> // Use `index` as the key
                )),
        },
        {
            title: "得分",
            dataIndex: "questions_type",
            key: "questions_type",
            align: "center",
            width: "10%",
            render: (value: any, record: any) => {
                const { questions_type, answerQuestion = [] } = record;
                if (questions_type === 5) {
                    const totalScore = answerQuestion?.reduce((sum: number, item: any) => {
                        return sum + (item?.score ?? 0);
                    }, 0);
                    return totalScore?.toFixed(2) || 0;
                }

                // 如果不是类型 5，显示第一个 answerQuestion 的分数
                return record.answerQuestion?.[0]?.score?.toFixed(2) ?? 0;
            },
        },
        {
            title: "操作",
            dataIndex: "action",
            key: "action",
            align: 'center',
            width: '20%',
            render: (text: string, record: any, index: any) => (
                <a onClick={() => Questionnaire(record, index)}>查看答案详情</a>
            ),
        },
    ];
    // 扩展表格
    const expandedRowRender = (record: any) => {
        const { groupQuestions = [], answerQuestion = [] } = record;
        const expandList = groupQuestions.map((group: any, index: any) => ({
            ...group,
            answerScore: (answerQuestion || [])[index]?.score ?? 0,
        }));

        const columns: any = [
            {
                title: '组成部分',
                width: '10%',
                dataIndex: 'name',
                key: 'name',
                ellipsis: true,
                align: 'center',
                render: (item: any) => (
                    <div>{examType.optionType_[Number(item)]}</div>
                ),
            },
            {
                title: "序号",
                dataIndex: "partIndex",
                key: "partIndex",
                width: '6%',
                align: "center",
            },
            {
                title: '题目类型',
                width: '12%',
                dataIndex: 'questions_type',
                key: 'questions_type',
                align: "center",
                render: (item: any) => (
                    <div style={{ textAlign: 'center' }} >{examType.optionType_[Number(item)]}</div>
                ),
            },
            {
                title: '题目内容',
                width: '30%',
                dataIndex: 'questions_content',
                key: 'questions_content',
                align: "center",
                render: (value: any) => (
                    <RenderHtml cname="textAlign" value={value}></RenderHtml>
                ),
            },
            {
                title: '答案',
                width: '10%',
                dataIndex: 'questions_answers',
                key: 'questions_answers',
                align: "center",
                render: (value: any) =>
                    value?.map((item: any) => (
                        <RenderHtml cname="textAlign" value={item}></RenderHtml>
                    )),
            },
            {
                title: "得分",
                dataIndex: "answerScore", // 使用预处理后的分数
                key: "answerScore",
                width: '10%',
                align: 'center',
                render: (value: any) => value ?? 0, // 如果分数不存在，默认显示 0
            },
            {
                title: "操作",
                key: "action",
                width: '20%',
                align: 'center',
            },
        ];

        return <Table sticky={false} columns={columns} showHeader={false} dataSource={expandList} pagination={false} />;
    };


    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const recordStr = params.get('record');
        const name = params.get('name');
        if (recordStr) {
            settitleName(name)
            setRecordData(JSON.parse(decodeURIComponent(recordStr)));

            const recordDatas = JSON.parse(decodeURIComponent(recordStr))
            questionPageAPI(pagination.page, pagination.size, recordDatas.id);
        }

    }, [location, pagination.page, pagination.size]);


    const questionPageAPI = async (page: number, size: number, testId: any) => {
        const res = await Contract.questionPage({ page, size, testId });
        if (res.status === 200) {
            setPagtotal(res.data.totalCount)
            setTableData(res.data)
        }
    };

    const handleTableChange = (pagination: { page: any; size: any; }) => {
        setPagination({
            page: pagination.page,
            size: pagination.size,
        });
    };

    const Questionnaire = (record: any, index: any) => {
        var data = {
            listVoByAnswerId: recordData.id,
            titleName: titleName,
            SelectId: record.id,
            detailIndex: index + 1,
            property: '收藏',
            classifyId: recordData.classifyId,
        }
        history.push({
            pathname: `/ExamModule/Answer`,
            state: data
        });
    }

    return (
        <ExamLayout>
            <div className="Detailspage">
                {/* 顶部导航 */}
                <header className='header'>
                    <a className='comeback' onClick={() => window.history.back()} >
                        <LeftCircleFilled style={{
                            color: '#CBCBCB', width: '20px',
                            height: '20px', fontSize: '32px',
                            marginRight: '10px',
                        }} />
                        <div className='fs'> 返回上一级</div>
                    </a>
                    <div className='statisticsContainer'>
                    </div>
                </header >
                <div style={{ margin: '20px' }} >
                    {/* 卡片区域 */}
                    <div className="car" >
                        <div className="carName" >
                            <UserOutlined style={{ fontSize: '19px' }} />
                            <div className="Names" >{recordData?.stuName}</div>
                            <div className="xhao" > {recordData?.stuCode ? `(${recordData.stuCode})` : ''} </div>
                        </div>
                        <div className="timu" >
                            <div className="mus" >
                                <img
                                    width={19}
                                    src={require('@/images/icons/time.png')}
                                    alt="时间图标"
                                />
                                <div className="title" >答题时长</div>
                            </div>
                            <div>
                                <text className="etx" >{(recordData?.answerMillisecond / 60000).toFixed(2)}</text> 分钟
                            </div>
                        </div>
                        <div className="timu" >
                            <div className="mus">
                                <img
                                    width={19}
                                    src={require('@/images/icons/time.png')}
                                    alt="时间图标"
                                />
                                <div className="title" >全站平均答题时长</div>
                            </div>
                            <div>
                                <text className="etx" >{(recordData?.avgAnswerDuration / 60000).toFixed(2)}</text>分钟
                            </div>
                        </div>
                        <div className="timu" >
                            <div className="mus" >
                                <img
                                    width={19}
                                    src={require('@/images/icons/def.png')}
                                    alt="时间图标"
                                />
                                <div className="title" >得分/总分</div>
                            </div>
                            <div>
                                <text className="etx" >
                                    {recordData?.score ? recordData?.score?.toFixed(2) : 0} /
                                    {recordData?.totalScore ? recordData?.totalScore : 0}
                                </text>
                            </div>
                        </div>

                        <div className="timu" >
                            <div className="mus" >
                                <img
                                    width={19}
                                    src={require('@/images/icons/kj.png')}
                                    alt="时间图标"
                                />
                                <div className="title" >得分排名</div>
                            </div>
                            <div>
                                <text className="etx" >{recordData?.ranking ? recordData?.ranking : 0}</text>
                            </div>
                        </div>

                        <div className="timu" >
                            <div className="mus">
                                <img
                                    width={19}
                                    src={require('@/images/icons/jsb.png')}
                                    alt="全站平均得分"
                                />
                                <div className="title" >全站平均得分</div>
                            </div>
                            <div>
                                <text className="etx" >{recordData?.avgScore ? recordData?.avgScore : 0}</text>
                            </div>
                        </div>

                        <div className="timu" >
                            <div className="mus">
                                <img
                                    width={19}
                                    src={require('@/images/icons/zq.png')}
                                    alt="平均正确率"
                                />
                                <div className="title" >平均正确率</div>
                            </div>
                            <div>
                                <text className="etx" >{recordData?.scoringRate ? recordData?.scoringRate : 0}% </text>
                            </div>
                        </div>


                        <div className="timu" >
                            <div className="mus">
                                <img
                                    width={19}
                                    src={require('@/images/icons/zq.png')}
                                    alt="全站正确率"
                                />
                                <div className="title" >全站正确率</div>
                            </div>
                            <div>
                                <text className="etx" >{recordData?.allScoringRate ? recordData?.allScoringRate : 0}% </text>
                            </div>
                        </div>
                    </div>

                    {/* 表格区域 */}
                    {/* <div className="tables" >
                        <Table
                            style={{ padding: '20px' }}
                            columns={columns}
                            dataSource={data}
                            pagination={false}
                        />
                    </div> */}

                    <Table
                        dataSource={TableData?.data || []}
                        columns={columns}
                        rowKey="id"
                        pagination={{
                            position: ["bottomCenter"],
                            current: pagination.page,
                            pageSize: pagination.size,
                            total: pagtotal,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            onChange: (page, size) => handleTableChange({ page, size }),
                        }}
                        expandable={{
                            // expandedRowRender: record => expandedRowRender(record?.groupQuestions || []),
                            expandedRowRender: record => expandedRowRender(record),
                            rowExpandable: record => record.questions_type == 5,
                            expandedRowClassName: () => 'group_expand'
                        }}
                        scroll={{ x: 1300 }}
                    />

                </div>
            </div>
        </ExamLayout>
    );
};

export default Detailspage;
