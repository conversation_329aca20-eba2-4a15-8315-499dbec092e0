import React, { useEffect, useState } from "react";
import { Card, Table, Input, Button, Row, Col, Empty } from "antd";
import Bar<PERSON><PERSON> from "./compoents/Charts/BarChart";
import PieChart from "./compoents/Charts/PieChart";
const { Search } = Input;
import { LeftCircleFilled, } from '@ant-design/icons';
import ExamLayout from '@/pages'; // 路径根据你的项目结构调整
import './index.less';
import { history, useLocation, useSelector } from 'umi';
import Contract from '@/api/Contract';

const Revise = () => {
    const location: any = useLocation();

    const [Id, setId] = useState();
    const [titleName, settitleName] = useState();
    const [Data, setData] = useState([]);
    const [chartData, setChartData] = useState([]);

    const [TableData, setTableData] = useState([]);
    const [pagination, setPagination] = useState({
        page: 1,  // 当前页码
        size: 10, // 每页显示的数量
    });
    const [pagtotal, setPagtotal] = useState();

    useEffect(() => {
        const Id = location.query.Id;
        settitleName(location.query.name)
        setId(Id)
        getCountVoAPI(Id)
        getAnswerCountVoAPI(pagination.page, pagination.size, Id, '');
    }, [pagination.page, pagination.size]);

    const getAnswerCountVoAPI = async (page: number, size: number, testId: any, nameCode: string) => {
        const res = await Contract.getAnswerCountVo({ page, size, testId, nameCode });
        if (res.status === 200) {
            setPagtotal(res.data.totalCount)
            setTableData(res.data)
        }
    };

    const getCountVoAPI = async (id: any) => {
        const res = await Contract.getCountVo(id);
        if (res.status === 200) {
            setData(res.data)

            const chartMap = res.data.chartMap;
            if (!chartMap) return;
            const transformedData = Object.keys(chartMap).map((key) => ({
                name: key,
                value: chartMap[key],
            }));

            setChartData(transformedData);
            // if (res.data.chartMap === null) {
            // } else {
            // const chartMap = res.data.chartMap;
            // const transformedData = Object.keys(chartMap).map((key) => ({
            //     name: key,
            //     value: chartMap[key],
            // }));
            //     setChartData(transformedData);
            // }


        }
    };
    // 表格数据

    const tableColumns = [
        {
            title: "姓名",
            dataIndex: "stuName",
            key: "stuName",
            align: 'center',
        },
        {
            title: "作答时长",
            dataIndex: "answerMillisecond",
            key: "answerMillisecond",
            align: 'center',
            // 分钟
            render: (text: number | null) => {
                if (text == null) {
                    return '';
                }
                const minutes = (text / 60000).toFixed(2);
                return `${minutes} 分钟`;
            },
        },
        {
            title: "得分全站排名",
            dataIndex: "ranking",
            key: "ranking",
            align: 'center',
        },
        {
            title: "得分/总分",
            dataIndex: "score",
            key: "score",
            align: 'center',
            render: (text: any, record: any) => {
                const { score, totalScore } = record;
                return `${score} / ${totalScore}`;
            },
        },
        {
            title: "提交时间",
            dataIndex: "submitDate",
            key: "submitDate",
            align: 'center',
            render: (text: string | number | Date) => {
                const date = new Date(text);
                return date.toLocaleDateString();
            },
        },
        {
            title: "批改时间",
            dataIndex: "lastReadDate",
            key: "lastReadDate",
            align: 'center',
            render: (text: string | number | Date) => {
                if (!text) {
                    return '';
                }
                const date = new Date(text);
                if (isNaN(date.getTime())) {
                    return '';
                }
                return date.toLocaleDateString();
            },
        },

        {
            title: "平均得分率",
            dataIndex: "scoringRate",
            key: "scoringRate",
            align: 'center',
        },
        {
            title: "操作",
            dataIndex: "action",
            key: "action",
            align: 'center',
            render: (isRead: Boolean, record: any) =>

                <Button style={{
                    background: '#549CFF',
                    borderRadius: '17px',
                    border: 'none',
                    padding: '6px 23px'
                }}
                    onClick={() => Questionnaire(record)} type="primary">查看
                </Button>
        },
    ];

    const handleTableChange = (pagination: { page: any; size: any; }) => {
        setPagination({
            page: pagination.page,
            size: pagination.size,
        });
    };

    const Questionnaire = (record: any) => {
        const recordStr = encodeURIComponent(JSON.stringify(record));
        history.push(`/ExamModule/Detailspage?record=${recordStr}&name=${titleName}`);
    }


    const onSearchBtton = (nameCode: string) => {
        getAnswerCountVoAPI(pagination.page, pagination.size, Id, nameCode);
    }

    return (
        <ExamLayout>
            <div className="Revise" >
                <header className='header'>
                    <a className='comeback' onClick={() => window.history.back()} >
                        <LeftCircleFilled style={{
                            color: '#CBCBCB', width: '20px',
                            height: '20px', fontSize: '32px',
                            marginRight: '10px',
                        }} />
                        <div className='fs'>{titleName}</div>
                    </a>
                    <div className='statisticsContainer'>
                    </div>
                </header >
                <div className="pad" >
                    {/* 顶部统计部分 */}
                    <div className="boxs">
                        <div className="boxLeft">
                            <div className="left_pd" >
                                <div className="Cards" >
                                    <div className="line" />
                                    <h3>批改数/总提交数</h3>
                                    <div style={{ fontSize: "20px", width: '100%', fontWeight: "bold", color: "#1890ff", textAlign: 'center' }}>
                                        {Data?.readQuantity ? Data?.readQuantity : 0} / {Data?.submitQuantity ? Data?.submitQuantity : 0}
                                    </div>
                                </div>
                                <div style={{ height: '200px' }} >
                                    <PieChart
                                        colors={['#C1EBEE', '#53C9D2']}
                                        title={` ${Data?.avgScore ? `${Data.avgScore}` : '无数据'} \n  平均分数`}
                                        data={Data?.avgScore ? [100 - Data.avgScore, Data.avgScore] : [100, 0]} // 默认值为[100, 0]，表示没有数据
                                    />
                                </div>
                                <div style={{ height: '200px' }} >
                                    <PieChart
                                        colors={['#FDF2D3', '#FEC636']}
                                        title={` ${Data?.scoringRate ? `${Data.scoringRate}%` : '无数据'} \n 得分率`}
                                        data={Data?.scoringRate ? [100 - Data.scoringRate, Data.scoringRate] : [100, 0]} // 默认值为[100, 0]，表示没有数据
                                    />
                                </div>
                            </div>

                        </div>
                        <div className="BarChart">
                            <div className="Cards">
                                <div className="Cards_zql fs">各题目平均正确率</div>
                                {
                                    chartData && chartData.length > 0 ? (
                                        <BarChart data={chartData} />
                                    ) : (
                                        <Empty style={{ margin: '20%' }} description="暂无数据" />
                                    )
                                }
                            </div>
                        </div>
                    </div>

                    {/* 表格 */}

                    <Card style={{ marginTop: "20px" }} title="学员测验情况">
                        <Search
                            placeholder="请输入学员姓名"
                            onSearch={(value) => onSearchBtton(value)}
                            enterButton
                            style={{ marginBottom: "20px", width: "300px" }}
                        />
                        <Table
                            dataSource={TableData?.data || []}
                            columns={tableColumns}
                            rowKey="id"
                            pagination={{
                                position: ["bottomCenter"],
                                current: pagination.page,
                                pageSize: pagination.size,
                                total: pagtotal,
                                showSizeChanger: true,
                                showQuickJumper: true,
                                onChange: (page, size) => handleTableChange({ page, size }),
                            }}
                        />
                    </Card>
                </div>
            </div>
        </ExamLayout>
    );
};

export default Revise;
