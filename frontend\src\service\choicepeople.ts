import http from '../http/http'
export function rootOrganization() {
  return http(`/unifiedplatform/v1/organization/root`, {
    method: 'GET'
  })
}
export function codeOrganization(organizationCode?: string) {
  return http(
    organizationCode ?
      `/unifiedplatform/v1/organization/user?code=${organizationCode}`
      : `/unifiedplatform/v1/organization/user?`, {
    method: 'GET'
  }
  )
}
export function childOrganization(organizationCode: string) {
  return http(`/unifiedplatform/v1/organization/child/${organizationCode}`,
    { method: 'GET' })
}
export function getAllUserByOrg(data: any) {
  return http(`/unifiedplatform/v1/organization/origina/users`, {
    method: 'POST',
    data
  })
}
// 获取列表
export function getteacherlist(code: string) {
  return http(`/learn/v1/teaching/course/get/teacher/list?id=${code}`, { method: 'GET' })
}
// 初始化已选数据 
export function initGroupUsers(params: any) {
  return http(`/rman/v1/group/users`,
    {
      method: 'GET',
      params:params
    })
}
