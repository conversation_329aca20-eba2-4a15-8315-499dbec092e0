import examManageApis from '@/api/exam';
import { IconFont } from '@/components/iconFont/iconFont';
import examType from '@/types/examType';
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  DownloadOutlined,
  PlusCircleOutlined,
  QuestionCircleFilled,
  UndoOutlined,
} from '@ant-design/icons';
import {
  Button,
  Checkbox,
  DatePicker,
  Form,
  Input,
  Modal,
  Pagination,
  Popover,
  Radio,
  Select,
  Table,
  TreeSelect,
  message,
} from 'antd';
import { useEffect, useState } from 'react';
import { history, useSelector } from 'umi';
import './index.less';
// import { filterXSS } from 'xss';
import Preview from '@/components/preview';
import TopicComponents from '@/components/TopicComponents';
import TeacherItem from '@/components/select/teacherItem';
import TemplateImport from '@/components/templateImport';
import { codeToName, noSupport } from '@/utils';
// const xss = require('xss');
// import { IconFont } from '../../components/index';
import paperManageApis from '@/api/Contract';
import QuestionGroupPreview from '@/components/QuestionGroupPreview';
import RenderHtml from '@/components/renderHtml';
import { IConfig } from '@/models/config';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;

function TopicManagement() {
  const [form] = Form.useForm();
  const [shareForm] = Form.useForm();
  const type_enum = examType.optionType_; //题目类型
  const [major, setMajor] = useState<any[]>([]); //适用专业
  const [creator, setCreator] = useState<any[]>([]); //创建人
  const [selectRows, setSelectedRows] = useState<any>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [total, setTotal] = useState(0);
  const [topicType, setTopicType] = useState<any>(0);
  const [data, setData] = useState<any>([]);
  const [preObject, setPreObject] = useState<any>(undefined);
  const [preGroupObject, setPreGroupObject] = useState<any>(undefined); //创建人
  const [previsible, setPrevisible] = useState<boolean>(false);
  const [GroupPrevisible, setGroupPrevisible] = useState<boolean>(false);
  const [newTopicVisible, setNewTopicVisible] = useState<boolean>(false);
  const [shareModalVisible, setShareModalVisible] = useState<boolean>(false);
  const [templateImportVisible, setTemplateImportVisible] = useState(false);
  const { parameterConfig, permissions, modules } = useSelector<
    { permission: any },
    any
  >(({ permission }) => permission);
  const globalData = useSelector<any, any>((state) => {
    // console.log(state)
    return state.globalData.major;
  });
  const configs: IConfig = useSelector<{ config: any }, IConfig>(
    ({ config }) => config,
  );
  const [checkAll, setCheckAll] = useState<boolean>(false); //全选
  const [indeterminated, setIndeterminated] = useState<boolean>(false);
  const [moreSearch, setMoreSearch] = useState<boolean>(false);
  const [operatMenuVisible, setOpreatMenuVisible] = useState<boolean>(false);
  const [query, setQuery] = useState<any>({
    questions_content: '', //题目名称
    questions_type: undefined,
    questions_difficulty: undefined,
    questions_major: [],
    select_type: 1, //查询范围 1全部 2我创建的 3分享的
    lesson_name: '', //查询范围 1全部 2我创建的 3分享的
    description: 0, // 排序的属性,0:按照创建时间排序,1:按照更新时间排序
    sort: 0, // 0:降序,1:升序
    // page: 1,
    // size: 10,
    courseIds: [],
    chapterIds: [],
    sectionIds: [],
  });
  useEffect(() => {
    //分页保存在本地
    let paginationStorage = sessionStorage.getItem('pagination_exam')
    if (!paginationStorage) {
      setQuery((pre: any) => ({ ...pre, page: 1, size: 10 }))
    } else {
      const localPagination = JSON.parse(paginationStorage)
      setQuery((pre: any) => ({ ...pre, page: localPagination.page, size: localPagination.size }))
    }
  }, []);

  //#region 新增的筛选条件
  // 应用分类|考核方向
  const [initClassify, setInitClassify] = useState<any>([]);
  const [examDirection, setExamDirection] = useState<any>([]);
  const fetchClassify = async () => {
    const res = await paperManageApis.classification({ page: 1, size: 100 });
    if (res.status === 200) {
      setInitClassify(res.data?.data || []);
      setExamDirection(res.data?.data?.[0]?.children || []);
    }
  };
  // 题目来源
  const [initQuestionSource, setInitQuestionSource] = useState<any>([]);
  const fetchQuestionSource = async () => {
    const res = await examManageApis.questionSource();
    if (res.status === 200) {
      setInitQuestionSource(res.data);
    }
  };
  // 课程树
  const [initCourseTree, setInitCourseTree] = useState<any>([]);
  const courseTree = async () => {
    const res = await examManageApis.TreecoureList();
    if (res.status === 200) {
      console.log(res.data.data, 'TreecoureList');
      setInitCourseTree(res.data.data);
    }
  };

  useEffect(() => {
    fetchClassify();
    fetchQuestionSource();
    // courseTree();
  }, [])
  //#endregion
  // 遍历目录树
  const forTree = (tree: any, level: number = 1) => {
    return tree?.map((item: any) => {
      return {
        // key: item.categoryCode + ',' + item.categoryName,
        title: item.categoryName,
        value: item.categoryCode + ',' + item.categoryName,
        disabled: level < 3 ? true : false,
        children: item.children ? forTree(item.children, level + 1) : [],
      };
    });
  };
  useEffect(() => {
    if (!(query?.page > 0) || !(query?.size > 0)) return
    fetchDataList();
  }, [query]);
  const columns: any = [
    Table.SELECTION_COLUMN,
    Table.EXPAND_COLUMN,
    {
      title: '题目类型',
      width: '125px',
      dataIndex: 'questions_type',
      key: 'questions_type',
      ellipsis: true,
      render: (item: any, record: any) => (
        <div>{examType.optionType_[Number(item)]}</div>
      ),
    },
    {
      title: '题目',
      width: '270px',
      dataIndex: 'questions_content',
      key: 'questions_content',
      ellipsis: true,
      render: (value: any) => (
        <RenderHtml cname="auto-img" value={value}></RenderHtml>
      ),
    },
    {
      title: '答案',
      width: '90px',
      dataIndex: 'questions_answers',
      key: 'questions_answers',
      ellipsis: true,
      render: (value: any) =>
        value?.map((item: any) => (
          <RenderHtml cname="auto-img" value={item}></RenderHtml>
        )),
    },
    {
      title: '解析',
      width: '180px',
      dataIndex: 'questions_analysis',
      key: 'questions_analysis',
      ellipsis: true,
      render: (value: any) => (
        <RenderHtml cname="auto-img" value={value}></RenderHtml>
      ),
    },
    {
      title: '难度',
      width: '5%',
      dataIndex: 'questions_difficulty',
      key: 'questions_difficulty',
      ellipsis: true,
    },
    {
      title: '知识点',
      width: '8%',
      dataIndex: 'knowledge_points',
      key: 'knowledge_points',
      ellipsis: true,
      render: (item: any, record: any) => {
        let arr: any = [];
        if (item?.length) {
          item.forEach((item_: any) => {
            arr.push(item_.entity);
          });
        }
        return arr.join(',');
      },
    },
    {
      title: '正确率',
      width: '5%',
      dataIndex: 'accuracy',
      key: 'accuracy',
      ellipsis: true,
    },
    {
      title: '适用层次',
      width: '10%',
      dataIndex: 'questions_level',
      key: 'questions_level',
      ellipsis: true,
      render: (item: any, record: any) =>
        item?.map((item_: any) => {
          return examType.levelType_[Number(item_)] || '' + ' ';
        }),
    },
    {
      title: '适用院系/部门',
      width: '10%',
      dataIndex: 'questions_major',
      key: 'questions_major',
      ellipsis: true,
      render: (item: any, record: any) => codeToName(item),
    },
    {
      title: '适用课程',
      width: '10%',
      dataIndex: 'questions_lesson',
      key: 'questions_lesson',
      ellipsis: true,
    },
    {
      title: '创建人',
      width: '8%',
      dataIndex: 'add_username',
      key: 'add_username',
      ellipsis: true,
    },
    {
      title: '创建时间',
      width: '12%',
      dataIndex: 'creator_time',
      key: 'creator_time',
      ellipsis: true,
      render: (item: any, record: any) => {
        // 格式化时间
        return dayjs(item).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      key: '操作',
      dataIndex: 'editable',
      width: '8%',
      align: 'center',
      fixed: 'right',
      render: (disable: any, record: any) => (
        <div className="table_opt">
          <Button type="link" onClick={() => preview(record)} title="预览">
            <IconFont type="iconyulan" />
          </Button>
          {disable && (
            <>
              <Button
                type="link"
                disabled={!disable}
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  edit(record);
                }}
                title="编辑"
              >
                <IconFont type="iconbianji-heise" />
              </Button>
            </>
          )}
          {
            <Button
              type="link"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                batchdelete(record);
              }}
              title="删除"
            >
              <IconFont type="iconshanchu-heise-copy" />
            </Button>
          }
        </div>
      ),
    },
  ];
  const rowSelection = {
    type: 'checkbox',
    onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
      setSelectedRowKeys(newSelectedRowKeys.filter(Boolean));
      setSelectedRows(newSelectedRows.filter(Boolean)); //得处理勾选移除后的残余空值对象
    },
    preserveSelectedRowKeys: true,
    selectedRowKeys,
  };
  const preview = (item: any) => {
    if (item?.questions_type === 5) {
      setPreGroupObject({
        ...item,
        groupQuestions: item.groupQuestions.map((item: any) => ({
          ...item,
          showDetail: false
        }))
      });
      setGroupPrevisible(true)
      return
    }
    setPreObject(item);
    setPrevisible(true);
  };
  const edit = (item: any) => {
    if (item?.questions_type === 5) {
      history.push({
        pathname: '/group/manage',
        query: {
          opt_type: 'edit',
          detail: item.id,
        }
      })
      return
    }
    history.push({
      pathname: `/topic/manage`,
      query: {
        opt_type: 'edit',
        detail: item.id,
      },
    });
  };
  const batchdelete = async (item: any) => {
    const temp = (window as any).login_useInfo?.roles?.some((item: any) =>
      ['r_second_manager', 'r_sys_manager', 'admin_S1'].includes(item.roleCode),
    );
    Modal.confirm({
      content: temp
        ? '删除后该试题不可见，使用过该试题的测验仍可查看，确认删除吗？'
        : '确定要删除吗？',
      title: '删除确认',
      icon: <QuestionCircleFilled style={{ color: 'var(--primary-color)' }} />,
      onOk: async () => {
        const data = item ? [item.id] : selectRows.map((item: any) => item.id);
        const res = await examManageApis.deleteTopics(data, { deleteType: true });
        if (res.status === 200) {
          message.success('删除成功');
          setSelectedRows([]);
          setSelectedRowKeys([]);
          fetchDataList();
          setIndeterminated(false);
          setCheckAll(false);
        } else {
          message.error('删除失败');
        }
      },
    });
  };
  const fetchDataList = async () => {
    const res = await examManageApis.fetchTopicLists(query);
    if (res.status === 200) {
      const tempList = res.data?.data?.map((item: any) => {
        if (item?.questions_type === 5) {
          return {
            ...item,
            // children: item.groupQuestions
          }
        }
        return item
      })
      setData(tempList);
      setTotal(res.data?.totalCount);
    }
  };
  const newTopic_ = () => {
    if (configs.mobileFlag) {
      noSupport();
      return;
    }
    setNewTopicVisible(true);
  };
  const createTopic = () => {
    // 题组
    if (topicType === 5) {
      history.push({
        pathname: `/group/manage`,
        query: {
          opt_type: 'new',
          type: topicType,
        },
      });
      return
    }
    history.push({
      pathname: `/topic/manage`,
      query: {
        opt_type: 'new',
        type: topicType,
      },
    });
  };
  const onChange = (e: any) => {
    console.log(e.target.value);
    setTopicType(e.target.value);
  };
  const onClose = () => {
    setPrevisible(false);
    setGroupPrevisible(false);
  };
  const batchimport = () => {
    setTemplateImportVisible(true);
  };
  const share = () => {
    //试卷分享
    setShareModalVisible(true);
  };
  const handleshare = async () => {
    //试卷分享
    const res = await examManageApis.topicBatchShare({
      ...shareForm.getFieldsValue(),
      ids: selectRows.map((item: any) => item.id),
    });
    console.log(shareForm.getFieldsValue(), res);
    if (res.status === 200) {
      message.success('分享成功');
      setShareModalVisible(false);
      setSelectedRowKeys([]);
      setSelectedRows([]);
      setIndeterminated(false);
      setCheckAll(false);
    } else {
      message.error('分享失败');
    }
  };
  const handlecopy = (item?: any) => {
    console.log('handlecopy', item);
    history.push({
      pathname: `/topic/manage`,
      query: {
        opt_type: 'copy',
        detail: item.id,
      },
    });
  };
  const batchhandlecopy = async () => {
    const res: any = await examManageApis.topicBatchCopy(
      selectRows.map((item: any) => item.id),
    );
    console.log(res);
    if (res.status === 200) {
      message.success('复制成功');
      setSelectedRows([]);
      setSelectedRowKeys([]);
      fetchDataList();
      setIndeterminated(false);
      setCheckAll(false);
    } else {
      message.error('复制失败，请重试');
    }
  };
  const handlegroup = () => {
    //通过window暂存
    console.log('selectRows', selectRows);
    window.sessionStorage.setItem('tempTopics', JSON.stringify(selectRows));
    history.push({
      pathname: `/paper/manage`,
      query: {
        opt_type: 'grouppaper',
      },
    });
  };
  const resetForm = () => {
    form?.resetFields();
  };
  const handleFormSubmit = () => {
    let values = form.getFieldsValue();
    if (values.search) {
      //移动端
      values = {
        ...values,
        ...values.search,
      };
      delete values.search;
      setMoreSearch(false);
    }
    setQuery({
      ...query,
      ...values,
      page: 1,
    });
  };
  const bdaonleupdatExportConfig = async (data_: any) => {
    const res: any = await paperManageApis.updatExportConfig(
      data_,
    );
    console.log(res);
    if (res.status === 200) {
      // 获取文件路径
      const filePath = res.data;
      
      // 提取文件名（从路径的最后一部分）
      const fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
      
      // 发起下载请求
      fetch(filePath)
        .then(response => response.blob())
        .then(blob => {
          // 创建下载链接
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = fileName; // 设置下载的文件名
          document.body.appendChild(a);
          a.click();
          
          // 清理
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        })
        .catch(error => {
          console.error('下载失败:', error);
        });
    } else {
      message.error('下载失败，请重试');
    }
  };
  const daonLoang = () => {
    const ID = selectRows.map((item: any) => item.id)
    if (data.length === selectRows.length) {
      bdaonleupdatExportConfig(query)
    } else {
      var pram = {
        exportIds: ID
      }
      bdaonleupdatExportConfig(pram)
    }

  }
  const btnList: any = [];
  // btnList.push({
  //   title: '批量导入',
  //   func: () => batchimport(),
  //   // disabled: selectRows.length === 0,
  //   dom: <IconFont type="iconbatchImport" />,
  // });
  btnList.push({
    title: '复制',
    func: () => batchhandlecopy(),
    disabled: selectRows.length === 0,
    dom: <IconFont type="iconcopy" />,
  });
  btnList.push({
    title: '删除',
    func: () => batchdelete(null),
    disabled: selectRows.length === 0,
    dom: <IconFont type="icondelete" />,
  });
  btnList.push({
    title: '分享至',
    func: () => share(),
    disabled: selectRows.length === 0,
    dom: <IconFont type="iconshare" />,
  });
  if (parameterConfig.target_customer !== 'ppsuc') {
    btnList.push({
      title: '组卷',
      func: () => handlegroup(),
      disabled: selectRows.length === 0,
      dom: <IconFont type="iconcacel" />,
    });
  }
  btnList.push({
    title: '导出',
    func: () => daonLoang(),
    disabled: selectRows.length === 0,
    dom: <DownloadOutlined />,
  });
  const checkAllChange = (e: any) => {
    const check = e.target.checked;
    setCheckAll(check);
    setIndeterminated(false);
    if (check) {
      setSelectedRows(data);
    } else {
      setSelectedRows([]);
    }
  };
  const checkChange = (check: any) => {
    setSelectedRows(check);
    setIndeterminated(!!check.length && check.length < data.length);
    setCheckAll(check.length === data.length);
  };
  // 扩展表格
  const expandedRowRender = (expandList: any) => {
    const columns: any = [
      {
        title: '题目类型',
        width: '125px',
        dataIndex: 'questions_type',
        key: 'questions_type',
        ellipsis: true,
        render: (item: any, record: any) => (
          <div>{examType.optionType_[Number(item)]}</div>
        ),
      },
      {
        title: '题目',
        width: '270px',
        dataIndex: 'questions_content',
        key: 'questions_content',
        ellipsis: true,
        render: (value: any) => (
          <RenderHtml cname="auto-img" value={value}></RenderHtml>
        ),
      },
      {
        title: '答案',
        width: '90px',
        dataIndex: 'questions_answers',
        key: 'questions_answers',
        ellipsis: true,
        render: (value: any) =>
          value?.map((item: any) => (
            <RenderHtml cname="auto-img" value={item}></RenderHtml>
          )),
      },
      {
        title: '解析',
        width: '180px',
        dataIndex: 'questions_analysis',
        key: 'questions_analysis',
        ellipsis: true,
        render: (value: any) => (
          <RenderHtml cname="auto-img" value={value}></RenderHtml>
        ),
      },
      {
        title: '难度',
        // width: '5%',
        dataIndex: 'questions_difficulty',
        key: 'questions_difficulty',
        ellipsis: true,
        render: () => null
      },
      {
        title: '知识点',
        // width: '8%',
        dataIndex: 'knowledge_points',
        key: 'knowledge_points',
        ellipsis: true,
        render: () => null
      },
      {
        title: '正确率',
        // width: '5%',
        dataIndex: 'accuracy',
        key: 'accuracy',
        ellipsis: true,
        render: () => null
      },
      {
        title: '适用层次',
        // width: '10%',
        dataIndex: 'questions_level',
        key: 'questions_level',
        ellipsis: true,
        render: () => null
      },
      {
        title: '适用院系/部门',
        // width: '10%',
        dataIndex: 'questions_major',
        key: 'questions_major',
        ellipsis: true,
        render: () => null
      },
      {
        title: '适用课程',
        // width: '10%',
        dataIndex: 'questions_lesson',
        key: 'questions_lesson',
        ellipsis: true,
        render: () => null
      },
      {
        title: '创建人',
        // width: '8%',
        dataIndex: 'add_username',
        key: 'add_username',
        ellipsis: true,
        render: () => null
      },
      {
        title: '创建时间',
        // width: '12%',
        dataIndex: 'creator_time',
        key: 'creator_time',
        ellipsis: true,
        render: () => null
      },
      // {
      //   title: '操作',
      //   key: '操作',
      //   dataIndex: 'editable',
      //   width: '8%',
      //   align: 'center',
      //   fixed: 'right',
      //   render: '',
      // },
    ];

    return <Table sticky={false} style={{ paddingLeft: 31 }} columns={columns} showHeader={false} dataSource={expandList} pagination={false} />;
  };
  // 传递给子组件的处理函数
  const handleSelectionChange = (keys: any[], rows: any[]) => {
    setSelectedRowKeys(keys);
    setSelectedRows(rows);
  };
  return (
    <div
      className={`topic_manage${configs.mobileFlag ? ' topic_manage_mobile' : ''
        }`}
    >
      <div className="search_box">
        <Form
          name="topic_form"
          form={form}
          onFinish={handleFormSubmit}
          style={{ flexWrap: 'wrap' }}
          initialValues={{
            search: {
              questions_type: '',
            },
          }}
        >
          {configs.mobileFlag ? (
            <>
              <Input.Group compact>
                <Form.Item name={['search', 'questions_type']} noStyle>
                  <Select>
                    <Select.Option value={''}>全部</Select.Option>
                    {type_enum.map((item: any, index: number) => (
                      <Select.Option value={index} key={item + index}>
                        {item}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item name={['search', 'questions_content']} noStyle>
                  <Input
                    placeholder="搜索题目名称"
                    allowClear
                    autoComplete={'off'}
                    addonAfter={
                      <IconFont onClick={handleFormSubmit} type="iconsousuo2" />
                    }
                  />
                </Form.Item>
              </Input.Group>
              <Button
                onClick={() => setMoreSearch(true)}
                icon={<IconFont type="iconshaixuan" />}
              >
                筛选
              </Button>
              <div className={moreSearch ? 'ant-modal-mask' : ''}></div>
              <div className={`moreSearch${moreSearch ? '' : ' none'}`}>
                <div className="head">
                  <span>更多筛选</span>
                  <IconFont
                    type="iconguanbi2"
                    onClick={() => setMoreSearch(false)}
                  />
                </div>
                <Form.Item
                  name={'questions_difficulty'}
                  className="form_select"
                >
                  <Select placeholder="题目难度">
                    <Select.Option value={1}>1</Select.Option>
                    <Select.Option value={2}>2</Select.Option>
                    <Select.Option value={3}>3</Select.Option>
                    <Select.Option value={4}>4</Select.Option>
                    <Select.Option value={5}>5</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item
                  name={'questions_major'}
                  className="form_select_major"
                >
                  <TreeSelect
                    treeData={forTree(globalData)}
                    maxTagCount={2}
                    // value={''}
                    key={'categoryCode'}
                    // onChange={onProfessionChange}
                    treeCheckable={true}
                    placeholder={'适用院系/部门'}
                    allowClear={true}
                    treeNodeFilterProp="categoryName"
                    showSearch
                    // defaultValue={[]}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  />
                </Form.Item>
                <Form.Item name={'select_type'} className="form_select">
                  <Select placeholder="创建人">
                    <Option value={1}>全部</Option>
                    <Option value={2}>我创建的</Option>
                    <Option value={3}>分享给我的</Option>
                  </Select>
                </Form.Item>
                <Form.Item name={'lesson_name'} className="form_input last">
                  <Input autoComplete="off" placeholder="适用课程搜索" />
                </Form.Item>
                <div className="btns">
                  <Button onClick={resetForm}>重置</Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    onClick={handleFormSubmit}
                  >
                    确认
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <>
              <Form.Item name={'questions_content'} className="form_input">
                <Input autoComplete="off" placeholder="搜索题目名称" />
              </Form.Item>
              <Form.Item name={'questions_type'} className="form_select">
                <Select placeholder="题目类型">
                  {type_enum.map((item: any, index: number) => (
                    <Select.Option value={index} key={item + index}>
                      {item}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item name={'questions_difficulty'} className="form_select">
                <Select placeholder="题目难度">
                  <Select.Option value={1}>1</Select.Option>
                  <Select.Option value={2}>2</Select.Option>
                  <Select.Option value={3}>3</Select.Option>
                  <Select.Option value={4}>4</Select.Option>
                  <Select.Option value={5}>5</Select.Option>
                </Select>
              </Form.Item>
              {parameterConfig.target_customer !== 'npu' && (
                <Form.Item
                  name={'questions_major'}
                  // className="form_select_major"
                  style={{ minWidth: 320 }}
                >
                  <TreeSelect
                    treeData={forTree(globalData)}
                    maxTagCount={2}
                    // value={''}
                    key={'categoryCode'}
                    // onChange={onProfessionChange}
                    treeCheckable={true}
                    placeholder={'适用院系/部门'}
                    allowClear={true}
                    treeNodeFilterProp="title"
                    // defaultValue={[]}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  />
                </Form.Item>
              )}
              <Form.Item name={'select_type'} className="form_select">
                <Select placeholder="创建人">
                  <Option value={1}>全部</Option>
                  <Option value={2}>我创建的</Option>
                  <Option value={3}>分享给我的</Option>
                </Select>
              </Form.Item>
              {/* <Form.Item name={'lesson_name'} className="form_input">
                <Input autoComplete="off" placeholder="适用课程搜索" />
              </Form.Item> */}
              {/* <Form.Item name='applicationClassCode' className="form_select">
                <Select placeholder="应用分类" options={initClassify.map((item: any) => {
                  return {
                    value: item.id,
                    label: item.name,
                    children: item.children || []
                  }
                })}
                  onChange={(_, option: any) => {
                    setExamDirection(option?.children || [])
                    form.setFieldsValue({
                      assessmentCode: ''
                    })
                  }}
                />
              </Form.Item>
              <Form.Item name='questionSourceCode' className="form_select">
                <Select placeholder="题目来源" options={initQuestionSource.map((item: any) => {
                  return {
                    value: item.questionSourceCode,
                    label: item.questionSourceName
                  }
                })} />
              </Form.Item>
              <Form.Item name='assessmentCode' className="form_select last">
                <Select placeholder="考核方向" options={examDirection.map((item: any) => {
                  return {
                    value: item.id,
                    label: item.name
                  }
                })} />
              </Form.Item> */}
              <Button
                onClick={() => {
                  resetForm();
                  form.submit();
                }}
                style={{
                  backgroundColor: '#e5effa',
                  color: '#549CFF',
                  border: '1px solid #549CFF',
                  marginRight: 10,
                }}
                // type="link"
                type="primary"
              >
                清空
                <UndoOutlined />
              </Button>
              <Button type="primary" htmlType="submit">
                搜索
                <IconFont type="iconsousuo2" />
              </Button>
            </>
          )}
        </Form>
      </div>
      <div className="split_line"></div>
      <div className="content">
        {/* <div style={{ display: 'flex', justifyContent: 'space-between' }} >
          <div className="opt_btn">
            {configs.mobileFlag && (
              <Checkbox
                checked={checkAll}
                onChange={checkAllChange}
                indeterminate={indeterminated}
              >
                全选
              </Checkbox>
            )}
            <Button type="primary" onClick={newTopic_}>
              <PlusCircleOutlined />
              新建题目
            </Button>
            {configs.mobileFlag ? (
              <>
                {btnList.length > 3 ? (
                  <>
                    {btnList.slice(0, 1).map((item: any, index: number) => {
                      return (
                        <Button
                          key={index}
                          className={item.disabled ? 'disabled' : ''}
                          onClick={() => {
                            if (!item.disabled) {
                              setOpreatMenuVisible(false);
                              item.func();
                            }
                          }}
                        >
                          {item.dom}
                          {item.title}
                        </Button>
                      );
                    })}
                    {btnList.slice(1, btnList.length).length > 0 && (
                      <Popover
                        placement="bottomLeft"
                        className="mobile_btns_popover"
                        getPopupContainer={(e: any) => e.parentNode}
                        onOpenChange={(newOpen: boolean) =>
                          setOpreatMenuVisible(newOpen)
                        }
                        open={operatMenuVisible}
                        content={
                          <div className="mobile_btns">
                            {btnList
                              .slice(1, btnList.length)
                              .map((item: any, index: number) => {
                                return (
                                  <div
                                    key={index}
                                    className={item.disabled ? 'disabled' : ''}
                                    onClick={() => {
                                      if (!item.disabled) {
                                        setOpreatMenuVisible(false);
                                        item.func();
                                      }
                                    }}
                                  >
                                    {item.dom}
                                    <span>{item.title}</span>
                                  </div>
                                );
                              })}
                          </div>
                        }
                      >
                        <Button
                          onClick={(e: any) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setOpreatMenuVisible(!operatMenuVisible);
                          }}
                        >
                          <IconFont type="iconziyuanku1" />
                          管理
                        </Button>
                      </Popover>
                    )}
                  </>
                ) : (
                  btnList.map((item: any, index: number) => {
                    return (
                      <Button
                        key={index}
                        className={item.disabled ? 'disabled' : ''}
                        onClick={() => {
                          if (!item.disabled) {
                            setOpreatMenuVisible(false);
                            item.func();
                          }
                        }}
                      >
                        {item.dom}
                      </Button>
                    );
                  })
                )}
              </>
            ) : (
              btnList.map((item: any, index: number) => {
                return (
                  <div
                    key={index}
                    className={item.disabled ? 'disabled item_' : 'item_'}
                    onClick={() => {
                      if (!item.disabled) {
                        item.func();
                      }
                    }}
                  >
                    {item.dom}
                    <span>{item.title}</span>
                  </div>
                );
              })
            )}
          </div>
          <div>
            <Select onChange={(value) => setQuery((prev: any) => ({ ...prev, sort: Number(value) }))} style={{ borderRadius: '6px' }} defaultValue="0">
              <Option value="0">更新时间 <ArrowDownOutlined /></Option>
              <Option value="1">更新时间 <ArrowUpOutlined /></Option>
            </Select>
          </div>
        </div> */}
        {configs.mobileFlag ? (
          <>
            <div className="list">
              <Checkbox.Group value={selectRows} onChange={checkChange}>
                {data.map((item: any, index: number) => {
                  return (
                    <div key={item.id} className="item">
                      <div className="head">
                        <Checkbox value={item} />
                        <div className="type">
                          【{examType.optionType_[Number(item.questions_type)]}
                          】
                        </div>
                        <RenderHtml
                          cname="topic"
                          value={item.questions_content}
                        ></RenderHtml>
                      </div>
                      <div className="answer">
                        <span>【答案】</span>
                        <RenderHtml value={item.answer}></RenderHtml>
                      </div>
                      <div className="analysis">
                        <span>【解析】</span>
                        <RenderHtml
                          value={item.questions_analysis}
                        ></RenderHtml>
                      </div>
                      <div className="info">
                        <div>
                          <div>
                            <label>难度</label>
                            <span>{item.questions_difficulty}</span>
                          </div>
                          <div>
                            <label>适用层次</label>
                            <span>
                              {item.questions_level?.map((item_: any) => (
                                <span>
                                  {examType.levelType_[Number(item_)] || ''}
                                </span>
                              ))}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div>
                            <label>适用院系/部门</label>
                            <span>{codeToName(item.questions_major)}</span>
                          </div>

                          <div>
                            <label>适用课程</label>
                            <span>{item.questions_lesson}</span>
                          </div>
                        </div>
                      </div>
                      <div className="bottom">
                        <Button
                          onClick={() => preview(item)}
                          icon={<IconFont type="iconyulan" />}
                        >
                          预览
                        </Button>
                        <Button
                          onClick={noSupport}
                          icon={<IconFont type="iconbianji-heise" />}
                        >
                          编辑
                        </Button>
                        <Button
                          onClick={() => batchdelete(item)}
                          icon={<IconFont type="iconshanchu-heise-copy" />}
                        >
                          删除
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </Checkbox.Group>
            </div>
            <div className="pagination">
              <Pagination
                showSizeChanger
                total={total}
                current={query.page}
                showQuickJumper
                onChange={(page: number, size: any) =>
                  setQuery({
                    ...query,
                    page,
                    size,
                  })
                }
                showTotal={(total) => `共 ${total} 条`}
                size="small"
              />
            </div>
          </>
        ) : (
          // <Table
          //   onRow={(record, index) => {
          //     return {
          //       onClick: () => {
          //         // console.log('点击行',record,index)
          //         preview(record);
          //       }, // 点击行
          //       onDoubleClick: (event) => {},
          //     };
          //   }}
          //   dataSource={data}
          //   rowKey={'id'}
          //   columns={columns}
          //   rowSelection={rowSelection as any}
          //   expandable={{
          //     expandedRowRender: record => expandedRowRender(record?.groupQuestions || []),
          //     rowExpandable: record => record.questions_type == 5,
          //     expandedRowClassName:() => 'group_expand'
          //   }}
          //   pagination={{
          //     position: ['bottomCenter'],
          //     showSizeChanger: true,
          //     total: total,
          //     showQuickJumper: true,
          //     current: query.page,
          //     onChange: (page: number, size: any) =>
          //       {setQuery({
          //         ...query,
          //         page,
          //         size,
          //       })
          //       sessionStorage.setItem('pagination_exam',JSON.stringify({page, size}))
          //     },
          //     showTotal: (total) => `共 ${total} 条`,
          //     size: 'small',
          //   }}
          //   scroll={{ x: 2100, y: 'calc(100vh - 340px)' }}
          // />
          <TopicComponents
            rowSelection={selectedRowKeys}
            dataSource={data}
            total={total}
            currentPage={query.page}
            btnList={btnList}
            onSelectionChange={handleSelectionChange}
            initCourseTree={initCourseTree} // 课程树
            handedit={(item) => {
              edit(item)
            }}
            handledit={(item) => {
              batchdelete(item);
            }}
            handbatchimport={() => {
              batchimport();
            }}
            handnewTopic_={() => {
              newTopic_();
            }}
            handpreview={(item) => {
              preview(item);
            }}
            onPageChange={(page: any, size: any) => {
              setQuery({
                ...query,
                page,
                size,
              });
            }}
            onCourseChange={(courseIds, chapterIds, sectionIds) => {
              console.log('从子组件接收到的数据:', courseIds?.[0], chapterIds?.[0], sectionIds?.[0]);
              // 在这里处理接收到的数据
              setQuery({
                ...query,
                courseIds:courseIds?.[0],
                chapterIds:chapterIds?.[0],
                sectionIds:sectionIds?.[0],
                page:1,
                size:10,
              });
          }}
          />
        )}
      </div>
      <Preview
        visible={previsible}
        detail={preObject}
        onClose={onClose}
        handlecopy={() => handlecopy(preObject)}
        handledit={() => edit(preObject)}
      />
      <QuestionGroupPreview
        visible={GroupPrevisible}
        detail={preGroupObject}
        onClose={onClose}
        handlecopy={() => handlecopy(preGroupObject)}
        handledit={() => edit(preGroupObject)}
      />
      <Modal
        visible={newTopicVisible}
        className={'newtopicmodal'}
        width={535}
        onCancel={() => setNewTopicVisible(false)}
        footer={[
          <Button type="primary" onClick={createTopic}>
            确定
          </Button>,
        ]}
        title="新建题目类型"
      >
        <Radio.Group onChange={onChange} defaultValue={0}>
          {type_enum.map((item: any, index: number) => {
            if (index === 5) {
              return (
                <Radio key={index} value={index}>{`${item}`}</Radio>
              )
            }
            return (
              <Radio key={index} value={index}>{`${item}题`}</Radio>
            )
          })}
        </Radio.Group>
      </Modal>
      <Modal
        visible={shareModalVisible}
        className={'shareMoadl'}
        onCancel={() => setShareModalVisible(false)}
        footer={[
          <Button
            type="primary"
            onClick={() => {
              shareForm.submit();
            }}
          >
            确定
          </Button>,
        ]}
        title="分享设置"
      >
        <Form name="shareForm" form={shareForm} onFinish={handleshare}>
          <TeacherItem
            multiple={true}
            required={true}
            message={'请至少勾选一个'}
            label={'分享给'}
            name={'share_users'}
            key="teacher1"
          />
        </Form>
      </Modal>
      <TemplateImport
        modalVisible={templateImportVisible}
        modalClose={() => setTemplateImportVisible(false)}
        refresh={fetchDataList}
      />
    </div>
  );
}
export default TopicManagement;
