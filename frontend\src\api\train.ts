// import request from './request';
import request from './index';


// 需要的

// async function rootOrganization() {
//     return request(`/unifiedplatform/v1/organization/general/root`, {
//         method: 'get'
//     })
// }

async function rootOrganization(roleCode: any) {
    return request(`/unifiedplatform/v1/organization/user?code=${roleCode}`, {
        method: 'get'
    })
}

async function childOrganization(organizationCode: string) {
    return request(`/unifiedplatform/v1/organization/child/${organizationCode}`, {
        method: 'get',
    })
}



// 获取组织下的用户
async function getUser(data: any) {
    return request(`/unifiedplatform/v1/organization/origina/users`, {
        method: 'post',
        data: data
    });
}


async function bindUser(
    roleCode: string,
    actionName: string,
    codes: Array<string>,
) {
    return request(`/unifiedplatform/v1/role/user/${roleCode}/${actionName}`, {
        method: 'post',
        data: codes,
    });
}


async function getAllUserByOrg(data: any) {
    return request(`/unifiedplatform/v1/organization/system/origina/users`, {
        method: 'post',
        data,
    });
}



// 需要

let train = {

    bindUser,
    getAllUserByOrg,
    rootOrganization,
    childOrganization,
    getUser
}


export default train;