import http from '../http/http'
import searchTypes from '@/types/searchTypes'

namespace contentListApis {
    export const searchfolder = (data: any) => {//个人公共素材检索
        return http<searchTypes.ISearchRes>('/search/folder', {
            method: 'POST',
            data
        })
    }
    export const searchall = (data: any) => {//搜索
        return http<searchTypes.ISearchRes>('/search/all', {
            method: 'POST',
            data
        })
    }
    export const newpublicfolder = (data: searchTypes.Nfolder) => {//新建公共文件夹
        return http<searchTypes.INfolder>('/folder/public', {
            method: 'POST',
            data: data,
            isSleep: true
        })
    }
    //群组资源
    export const newgroupfolder = (data: any) => {//新建群组文件夹
        return http<any>('/group/folder', {
            method: 'POST',
            data: data
        })
    }
    export const getgroupusers = (data: any) => {//初始化群组人员
        return http<any>('/group/select/user', {
            method: 'POST',
            data: data
        })
    }
    export const addgroupusers = (data: any) => {//添加群组人员
        return http<any>('/group/add/user', {
            method: 'POST',
            data: data
        })
    }
    export const deletegroupusers = (data: any) => {//删除群组人员
        return http<any>('/group/delete', {
            method: 'POST',
            data: data
        })
    }
    export const newprivatefolder = (data: searchTypes.Nfolder) => {//新建个人文件夹
        return http<searchTypes.INfolder>('/folder/private', {
            method: 'POST',
            data: data,
            isSleep: true
        })
    }
    export const renamefolder = (data: searchTypes.RNfolder) => {//文件夹重命名
        return http<boolean>('/folder/resource/rename', {
            method: 'POST',
            data: data,
            isSleep: true
        })
    }
    export const renameentity = (data: searchTypes.RNfolder, isPublic?: boolean) => {//文件重命名
        return http<boolean>('/entity/entitydata/rename', {
            method: 'PATCH',
            data: data,
            params: {
                isPublic: isPublic
            },
            isSleep: true
        })
    }
    export const downloadentity = (data: Array<string>) => {//文件下载
        return http<searchTypes.IDownLoad[]>(`/entity/download/fileinfo`, {
            method: 'POST',
            data: data
        })
    }
    export const getfields = () => {//获取元数据 
        return http<searchTypes.IEntityType[]>(`/unifiedplatform/v1/base/data/database/get/show/base/data`, {
            method: 'GET'
        })
    }
    export const getrecyclebinlist = (data: any) => {//文件下载
        return http<any>(`/recycle/resource/all`, {
            method: 'POST',
            data: data
        })
    }
    export const getmyvideolist = (data: any) => {//我的录播
        return http<any>(`/search/my/video`, {
            method: 'POST',
            data: data
        })
    }
    export const getmycollectionlist = (data: any) => {//查询我的收藏
        return http<any>(`/metadata/resource/search/collection`, {
            method: 'POST',
            data: data
        })
    }
    export const addmycollectionlists = (data: any) => {//批量添加我的收藏
        return http<any>(`/metadata/resource/collections`, {
            method: 'POST',
            data: data
        })
    }
    export const addmycollectionlist = (data: any) => {//单个添加我的收藏
        return http<any>(`/metadata/resource/collection?contentId=${data}`, {
            method: 'POST'
        })
    }
    export const deletemycollectionlist = (data: any) => {//取消我的收藏
        return http<any>(`/metadata/resource/delete/collection`, {
            method: 'POST',
            data: data
        })
    }
    /**
     * 分页查询教师学院信息
     *
     * @param {string} data 
     * 【query】
     * keyWord，pageIndex，pageSize
     * sourceTpye--查询类型 0：教师；1：学院；2：专业
     * 【options】
     * ifLoading：该请求不显示loading
     * @return {*} 
     */
    export const getPagingData = (data: string) => {
        //获取分页元数据
        return http(`/unifiedplatform/v1/base/data/database/source/data?${data}`, {
            method: 'GET',
            ifLoading: false
        })
    }
    //查询当前实例对应的基础信息
    export const getEntityByContentId = (data: string) => {
        //获取分页元数据
        return http(`/rman/v1/entity/${data}`, {
            method: 'GET',
            ifLoading: true
        })
    }
    //查询当前实例对应的字幕（已分析的语音文本）
    export const getSubtitles = (data: string) => {
        return http(`/rman/v1/search/webvtt?contentId=${data}&voice=true`, {
            method: 'GET'
        })
    }
    //资源分享
    export const shareResource = (data: any) => {
        return http(`/rman/v1/share/resource`, {
            method: 'POST',
            data
        })
    }
    //取消分享
    export const cancelShared = (data: any) => {
        return http(`/rman/v1/share/delete`, {
            method: 'POST',
            data
        })
    }
    //移除
    export const removeShared = (data: any) => {
        return http(`/rman/v1/share/delete/to/me`, {
            method: 'POST',
            data
        })
    }
    //查询自己分享的
    export const searchMyShare = (data: any) => {
        return http(`/rman/v1/share/search/owner`, {
            method: 'POST',
            data
        })
    }
    //查询未读分享数量
    export const searchUnreadNums = () => {
        return http(`/rman/v1/share/unread/message/to/me`, {
            method: 'GET'
        })
    }
    //标记已读
    export const markRead = (data:any) => {
        return http(`/rman/v1/share/read/message/to/me`, {
            method: 'POST',
            data
        })
    }
    //查询分享给自己的
    export const shareMyself = (data: any) => {
        return http(`/rman/v1/share/search`, {
            method: 'POST',
            data
        })
    }
}

export default contentListApis
