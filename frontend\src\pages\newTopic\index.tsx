import React, { useState, useEffect } from 'react';
import './index.less';
import {
  Input,
  Select,
  message
} from 'antd';
import { IconFont, TopicCreation } from '@/components/index';
import {useLocation} from 'umi'
import examManageApis from '@/api/exam';
// import {IconFont} from '@/components/iconFont/iconFont';
function newTopic() {
  const location:any = useLocation();
  const [initusers,setInitusers]= useState<any>([]);
  const [detail,setDetail]= useState<any>(undefined);
  
  useEffect(() => {
    fetchSharelist();
    const id = location.query.detail;
    if(id){
      fetchDetail(id);
    }
  }, []);
  const fetchDetail = async (id:number)=>{
    const res = await examManageApis.fetchTopicDetail(id);
    console.log('fetchDetail',res)
    if(res.status===200){
      let data = res.data
      data.questions_level = data.questions_level || []
      data.questions_major = data.questions_major || []
      setDetail(data)
    }
  };
  const fetchSharelist = async ()=>{
    const res = await examManageApis.initShareUsers();
    console.log('fetchSharelist',res)
    if(res.status===200){
      setInitusers(res.data)
    }
  };
  return (
    <div className="topic_manage">
      <TopicCreation
        opt_type={location.query.opt_type}
        type={Number(location.query.type)}
        topicId={location.query.topicId}
        selectKeys={initusers}
        detail={detail}
        key={JSON.stringify(initusers)+JSON.stringify(detail)}
        kecname={location.query.name}
      />
    </div>
  );
}
export default newTopic;
