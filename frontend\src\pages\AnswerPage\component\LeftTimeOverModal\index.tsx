import { Button, Modal } from 'antd';
import { FC, useEffect, useMemo, useRef, useState } from 'react';
import './index.less';

export interface IOrderModalProps {
  isShow: boolean;
  time: number
  onClose: () => void;
}

import { ExclamationCircleFilled } from '@ant-design/icons';

const LeftTimeOverModal: FC<IOrderModalProps> = ({ isShow, time, onClose }) => {
  const [modalCloseTime, setModalCloseTime] = useState<number>(3);
  const timer = useRef<any>(null);

  useEffect(() => {
    if (!isShow) {
      setModalCloseTime(3);
      if (timer.current) {
        clearInterval(timer.current);
      }
      return;
    }
    if (timer.current) {
      clearInterval(timer.current);
    }
    timer.current = setInterval(() => {
      setModalCloseTime((prev) => prev - 1);
    }, 1000);
    return () => {
      clearInterval(timer.current);
    };
  }, [isShow]);
  useEffect(() => {
    if (modalCloseTime === 0) {
      onClose();
    }
  }, [modalCloseTime]);

  const showTime = useMemo(() => {
    return Math.floor(Number(time || 0) / 1000 / 60)
  }, [time])

  return (
    <Modal
      title=""
      open={isShow}
      onOk={onClose}
      onCancel={onClose}
      footer={
        <div
          style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Button type="primary" onClick={onClose}>确定({modalCloseTime})</Button>
        </div>
      }
    >
      <div className="left_time_modal_container">
        <ExclamationCircleFilled
          style={{
            color: '#FAAD14',
            width: '36px',
            height: '36px',
            fontSize: '36px',
          }}
        />
        <div>考试还有{showTime}分钟结束，请注意时间</div>
      </div>
    </Modal>
  );
};

export default LeftTimeOverModal;
