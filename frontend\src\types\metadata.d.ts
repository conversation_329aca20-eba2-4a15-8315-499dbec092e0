declare namespace MetadataTypes {
  /**
   * 元数据分类
   */
  type Type = {
    id: string;
    code: string;
    name: string;
    order: number;
    fixed: number;
  };
  /**
   * 查询元数据分类
   */
  type TypeQuery = {
    page: number;
    size: number;
    code?: string;
  };
  /**
   * 查询元数据
   */
  type ConfigFieldQuery = {
    EntityType: string;
    Type: string;
    ResourceType: string;
  };
  /**
   * 元数据
   */
  type Field = {
    id?: number;
    fieldName: string;
    showName: string;
    fixItemId: number;
    isReadOnly: boolean;
    isMustInput: boolean;
    isMultiSelect?: boolean;
    minLength: number;
    maxLength: number;
    controlType: number;
    metadataType: string;
    fieldPath: string;
    defaultValue: string;
    refResourceField?: string;
    controlData?: string;
    order: number;
    isEnable: boolean;
    programform?: string;
    type: string;
    prompt?: string;
    lexiconName?: string;
    isEditField?: number;
    isUploadField?: number;
    hiveMaxLength?: number;
    hiveMinLength?: number;
    hiveMustInput?: boolean;
    alias?: string;
    dataType: string;
    isArray?: boolean;
  };
  /**
   * 更新元数据配置
   */
  type UpdateConfigFieldQuery = {
    updateField: Field[];
    entityType: string;
    type: string;
    resourceType: string;
    programform: string;
    source: string;
  };

  type typeEntity = {};
  /**
   * entity type response
   */
  type typeResData = {
    entityTypes: {
      name: string;
      code: string;
    }[];
    tabs: {
      name: string;
      code: string;
      type: string;
    }[];
    uploadtabs: {
      name: string;
      source: string;
      code: string;
      type: string;
    }[];
  };
  /**
   * hive type 实体
   */
  type HiveMetadataType = {
    code: string;
    name: string;
    id?: string;
    order: number;
  };
  /**
   * hive实体
   */
  type HiveEntity = {
    id: string;
    code: string;
    name: string;
    description: string;
    type: string;
    required: number;
    editable: number;
    _fixed: number;
    order: number;
    show: number;
    array: number;
    extend: any;
    mapping: {
      keyword: number;
      index: number;
      analyze: number;
      extend: any;
    };
    min_length: number;
    max_length: number;
    min_arr_num: number;
    max_arr_num: number;
    type_id: string;
  };

  interface IHiveEntity2 extends HiveEntity {
    checked?: boolean;
  }

  type HiveEntityData = {
    isSuccess: boolean;
    data: {
      id: string;
      code: string;
      name: string;
      order: number;
      fixed: number;
      entity_data_defines: HiveEntity[];
      metadata_defines: {
        id: string;
        code: string;
        name: string;
        field_defines: HiveEntity[];
      }[];
    };
  };
}
