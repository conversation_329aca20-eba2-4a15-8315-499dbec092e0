import { v4 as uuidv4 } from 'uuid';
// 获取富文本内容
export const fetchEditorContent = (name: any) => {
  const data = (window as any).tinymce.editors[name].getContent();
  return data;
};

//删除富文本
export const handleDelEditor = (name: string) => {
  (window as any).tinymce.editors[name].remove();
};

type TQuestion = 0 | 1 | 2 | 3 | 4;
//初始化选项
export const initOptions = (
  type: TQuestion,
  detail: any,
  setFun: (value: any) => void,
) => {
  if (detail?.questions_options?.length) {
    //这里处理数据是因为模板导入的选项是字幕 ABCD
    const optins = detail?.questions_options?.map((item: any, index: number) => {
      return {
        seq: index + 1,
        content: item.content,
        answerType: item?.answerType || 1,
        answerRange: item?.answerRange || null,
        answerMax: item?.answerMax || null,
        answerMin: item?.answerMin || null,
        uid: uuidv4(),
      };
    });
    setFun(JSON.parse(JSON.stringify(optins)));
  } else {
    switch (type) {
      case 0:
        setFun([
          { seq: 1, uid: uuidv4(), content: '' },
          { seq: 2, uid: uuidv4(), content: '' },
          { seq: 3, uid: uuidv4(), content: '' },
          { seq: 4, uid: uuidv4(), content: '' },
        ]);
        break;
      case 1:
        setFun([
          { seq: 1, uid: uuidv4(), content: '' },
          { seq: 2, uid: uuidv4(), content: '' },
          { seq: 3, uid: uuidv4(), content: '' },
          { seq: 4, uid: uuidv4(), content: '' },
        ]);
        break;
      case 2:
        setFun([]);
        break;
      case 3:
        setFun([]);
        break;
      case 4:
        setFun([
          { seq: 1, content: '正确' },
          { seq: 2, content: '错误' },
        ]);
        break;
      default:
        setFun([]);
    }
  }
};

// 编辑状态下文件列表的格式化，用于回显
export const formatFileList = (fileList: any[]) => {
  return fileList?.map((item: any) => {
    return {
      name: item.attachmentName,
      status: 'done',
      size: item.attachmentSize,
      url: item.attachmentSource,
      uid: item.contentId,
    };
  });
};

