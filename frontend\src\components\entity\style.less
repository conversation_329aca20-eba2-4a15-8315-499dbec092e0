.entity-img {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  > img {
    max-width: 100%;
    max-height: 100%;
    display: block;
  }
}

.entity-document {
  width: 100%;
  height: ~"calc(100vh - 500px)";
  overflow: auto;
  display: flex;
  justify-content: center;

  .react-pdf__message--loading {
    height: 100%;
  }
}

.entity-pdf,.entity-html {
  width: 100%;
  height: ~"calc(100vh - 500px)";
  overflow: auto;
  display: flex;
  justify-content: center;
  iframe{
    border: none;
  }
}


.entity-pdf,.entity-excel {
  width: 100%;
  height: ~"calc(100vh - 300px)";
  overflow: auto;
  display: flex;
  justify-content: center;
  iframe{
    border: none;
  }
}


.entity-wps {
  width: 100%;
  height: ~"calc(100vh - 500px)";
  overflow: auto;
  display: flex;
  justify-content: center;
}

.react-pdf__Document {
  height: 100%;
}

.vjs-remaining-time {
  display: none;
}

.exhibition {
  .video-js {
    max-height: 100%;

    .vjs-current-time,
    .vjs-time-divider,
    .vjs-duration {
      display: block;
    }
  }
}
