import HTTP from './index';

//登录
export function login(loginName: string, password: string) {
  return HTTP.get(
    `/rman/v1/account/login?loginName=${loginName}&password=${password}`,
  ).then(res => {
    if (res.status === 200) {
      return res.data;
    }
  });
}

//获取用户信息
export function userInfo() {
  // return HTTP.get(`/rman/v1/account/login-userinfo/S1`)
  return HTTP.get(`/unifiedplatform/v1/user/current`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

//获取教师信息
export function teacherInfo() {
  return HTTP.get(`/rman/v1/role/user?roleCode=r_teacher`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

//获取元数据
export function metaData() {
  return HTTP.get(
    `/rman/v1/metadata/config/fields?EntityType=biz_sobey_course&Type=basic&ResourceType=model_sobey_object_entity`,
  )
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

//查询课程
export function courseList(data: any) {
  return HTTP.post(`/learn/v1/course/release`, data)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

export function getCourseList(data: MicroCourse.searchParams) {
  const {
    name,
    publishStatus,
    classificationId,
    teacher,
    subjectId,
    startUpdateTime,
    endUpdateTime,
    page,
    size,
  } = data;
  return HTTP.post(`/learn/v1/course/release`, {
    name,
    publishStatus,
    classificationId,
    subjectId,
    teacher,
    startUpdateTime,
    endUpdateTime,
    isTop: null,
    keyword: '',
    courseType: 0,
    // college: [],
    // professionIds: [],
    order: [
      {
        field: 'createDate_',
        isDesc: true,
      },
    ],
    page,
    size: size ? size : 12,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

//删除课程
export function deleteCourse(MyData: string[]) {
  return HTTP.post(`/learn/v1/course/delete`, MyData)
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}

//发布课程
export function publishCourse(data: any) {
  return HTTP.get(`/learn/v1/course/status`, { params: { status: 1, courseId: data.join(",") }})
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}

//下架课程
export function unPublishCourse(data: any) {
  return HTTP.get(`/learn/v1/course/status`, { params: { status: 0, courseId: data.join(",") }})
    .then(res => {
      if (res.status === 200) {
        return res;
      }
    })
    .catch(error => {
      return error;
    });
}

//置顶课程 无
// export function topCourse(id: any) {
//   return HTTP.post(`/cvod/v1/course/top?isTop=true`, id)
//     .then(res => {
//       if (res.status === 200) {
//         return res;
//       }
//     })
//     .catch(error => {
//       return error;
//     });
// }

//取消置顶课程 无
// export function unTopCourse(id: any) {
//   return HTTP.post(`/cvod/v1/course/top?&isTop=false`, id)
//     .then(res => {
//       if (res.status === 200) {
//         return res;
//       }
//     })
//     .catch(error => {
//       return error;
//     });
// }

//获取权限
export function getUserJurisdiction() {
  return HTTP.get(`/unifiedplatform/v1/user/roleapp`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
}

export const getUserJurisdictionV2 = () =>
  HTTP(`/unifiedplatform/v1/user/rolemodule`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
export const getPagingData = (data: string) => {
  //获取分页元数据
  return HTTP.get(`/unifiedplatform/v1/base/data/database/source/data?${data}`)
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
};

/**
 * 查询课程模板数据
 * @param params
 * @param isPublish
 */
export const queryCourseTpl = (params: any, isPublish: boolean) =>
  HTTP(`/learn/v1/curriculum/center/course/template/catalogue`, {
    method: 'POST',
    params: {
      isPublish,
    },
    data: JSON.stringify(params),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 查询课堂回看数据
 * @param params
 */
export const queryCourseReview = (params: any) =>
  HTTP(`/learn/v1/course/record/course/private/authority/course`, {
    method: 'GET',
    params: params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * 查询校区
 * @param params
 */
export const fetchCampus = (params: any) =>
  // HTTP(`/cvod/v1/record/course/custom/course/position`, {
  HTTP(`/learn/v1/course/custom/course/position`, {
    method: 'GET',
    params: params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 查询教学楼
 * @param params
 */
export const fetchAcademicBuilding = (params: any) =>
  HTTP(`/learn/v1/course/custom/course/position`, {
    method: 'GET',
    params: params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 查询教室
 * @param params
 */
export const fetchClassrooms = (params: any) =>
  HTTP(`/learn/v1/course/custom/course/position`, {
    method: 'GET',
    params: params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });

/**
 * 查询所有学年和学期
 */    
export const fetchSchoolYears = () => 
  HTTP("/learn/v1/course/all/years").then(res => {
    if (res.status === 200) {
      return res.data;
    }
  })
  .catch(error => {
    return error;
  });

/**
 * 单个更新权限
 * @param params
 */
export const updateAuthoritySingle = (params: any) =>
  HTTP(`/learn/v1/course/update/authority`, {
    method: 'POST',
    data: JSON.stringify(params),
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * 批量更新权限
 * @param params
 */
export const updateAuthorityBatch = (courseIds: any, authority: string) =>
  HTTP(`/learn/v1/course/bulk/update/authority?authority=${authority}`, {
    method: 'POST',
    data: courseIds,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * 查询学生选课信息(该课程的本班同学)
 * @param params
 */
export const selectClassmates = (params: any) =>
  HTTP(`/learn/v1/record/course/user/info`, {
    method: 'GET',
    params: params,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * 批量新增权限(设置本班同学)
 * @param params
 */
// export const insertCourse = (data: any) =>
//   HTTP(`/cvod/v1/record/course/insert/authority`, {
//     method: 'POST',
//     data: data,
//   })
//     .then(res => {
//       if (res.status === 200) {
//         return res.data;
//       }
//     })
//     .catch(error => {
//       return error;
//     });
// /**
//  * (同步本班同学)
//  * @param params
//  */
// export const studentsSynchronization = (data:any) =>
// HTTP(`/cvod/v1/record/course/insert`, {
//     method: 'POST',
//     params:data
//   })
//   .then((res) => {
//     if (res.status === 200) {
//       return res.data;
//     }
//   })
//   .catch((error) => {
//     return error;
//   });

/**
 * (查询本班同学)-重构接口/cvod/v1/record/course/select/user/info
 * @param params
 */
export const fetchClassmates = (data: any) =>
  HTTP(`/learn/v1/course/classmates`, {
    method: 'GET',
    params: data,
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * (查询课程回看信息)-重构接口/cvod/v1/record/course/select/user/info
 * @param params
 */
export const fetchCourseReview = (courseId: string) =>
  HTTP(`/learn/v1/course/recording/video/info`, {
    method: 'GET',
    params: { courseId },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * (设置课程回看 显示隐藏)
 * @param params
 */
export const updateCourseReviewShow = (courseId: string, show: boolean) =>
  HTTP(`/learn/v1/course/course/back/show`, {
    method: 'GET',
    params: { courseId, show },
  })
    .then(res => {
      if (res.status === 200) {
        return res.data;
      }
    })
    .catch(error => {
      return error;
    });
/**
 * (添加本班同学)
 * @param params
 */
// export const addStudents = (data: any) =>
//   HTTP(`/cvod/v1/record/course/custom/insert/authority`, {
//     method: 'POST',
//     data: data,
//   })
//     .then(res => {
//       if (res.status === 200) {
//         return res.data;
//       }
//     })
//     .catch(error => {
//       return error;
//     });
/**
 * (单个删除本班同学)
 * @param params
 */
// export const deleteStudentSingle = (data: any) =>
//   HTTP(`/cvod/v1/record/course/delete`, {
//     method: 'DELETE',
//     params: data,
//   })
//     .then(res => {
//       if (res.status === 200) {
//         return res.data;
//       }
//     })
//     .catch(error => {
//       return error;
//     });
/**
 * (批量删除本班同学)
 * @param params
 */
// export const deleteStudentBatch = (courseId: string, userCode: any) =>
//   HTTP(`/cvod/v1/record/course/delete/authorities?courseId=${courseId}`, {
//     method: 'DELETE',
//     data: userCode,
//   })
//     .then(res => {
//       if (res.status === 200) {
//         return res.data;
//       }
//     })
//     .catch(error => {
//       return error;
//     });
