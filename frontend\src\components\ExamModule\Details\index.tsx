import React, { useState } from "react";
import { Table, Input, Button, Tag } from "antd";


// yu
const Details = () => {
    const [searchText, setSearchText] = useState("");

    // 数据源
    const data = [
        {
            key: "1",
            name: "李堂",
            account: "23456",
            submitTime: "2024-08-01 11:20:01",
            reviewStatus: "已批改",
            reviewer: "李霞",
            score: 90,
            action: "查看",
        },
        {
            key: "2",
            name: "林语",
            account: "34509",
            submitTime: "2024-08-01 11:20:01",
            reviewStatus: "未批阅",
            reviewer: "陈老师",
            score: 90,
            action: "批阅",
        },
    ];

    // 表格列配置
    const columns = [
        {
            title: "姓名",
            dataIndex: "name",
            key: "name",
        },
        {
            title: "账号",
            dataIndex: "account",
            key: "account",
        },
        {
            title: "提交时间",
            dataIndex: "submitTime",
            key: "submitTime",
        },
        {
            title: "批改状态",
            dataIndex: "reviewStatus",
            key: "reviewStatus",
            render: (text: any) =>
                text === "已批改" ? (
                    <Tag color="green">{text}</Tag>
                ) : (
                    <Tag color="red">{text}</Tag>
                ),
        },
        {
            title: "批改人",
            dataIndex: "reviewer",
            key: "reviewer",
        },
        {
            title: "得分",
            dataIndex: "score",
            key: "score",
        },
        {
            title: "操作",
            key: "action",
            render: (_, record) => (
                <Button type="link">{record.action}</Button>
            ),
        },
    ];

    // 搜索处理逻辑
    const handleSearch = (e) => {
        setSearchText(e.target.value);
    };

    // 过滤数据
    const filteredData = data.filter(
        (item) =>
            item.name.includes(searchText) || item.account.includes(searchText)
    );

    return (
        <div style={{ padding: "20px" }}>
            <div style={{ marginBottom: "20px" }}>
                <h3>测验提交情况</h3>
                <div style={{ marginBottom: "20px" }}>
                    <div>
                        <strong>小程序重新考试</strong>
                    </div>
                    <div>测验开放时间: 2024-04-01 14:00 - 2024-04-30 14:00</div>
                    <div>已提交（含补考）：2</div>
                    <div>待批改（含补考）：1</div>
                </div>
            </div>
            <div style={{ marginBottom: "20px" }}>
                <Input
                    placeholder="请输入学生姓名/学号搜索"
                    style={{ width: 300, marginRight: 10 }}
                    value={searchText}
                    onChange={handleSearch}
                />
                <Button type="primary">搜索</Button>
            </div>
            <Table columns={columns} dataSource={filteredData} pagination={{ pageSize: 5 }} />
        </div>
    );
};

export default Details;
