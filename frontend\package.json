{"private": true, "scripts": {"start": "node theme && cross-env NODE_OPTIONS=--openssl-legacy-provider umi dev", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/pro-components": "2.3.2", "@ant-design/pro-layout": "^6.5.0", "@onlyoffice/document-editor-react": "^1.5.1", "@wiris/mathtype-tinymce5": "^7.28.1", "antd": "^4.24.2", "antd-theme-generator": "^1.2.8", "array-move": "^4.0.0", "axios": "^0.27.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.9", "docx-preview": "0.1.14", "dompurify": "^3.0.3", "echarts": "^5.5.1", "html2canvas-pro": "^1.5.8", "immer": "^10.0.2", "immutability-helper": "^3.1.1", "jspdf": "^2.5.2", "lottie-react": "^2.4.1", "react": "17.x", "react-beautiful-dnd": "^13.1.1", "react-bus": "^3.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "17.x", "react-infinite-scroll-component": "^6.1.0", "react-sortable-hoc": "^2.0.0", "umi": "^3.5.23", "use-immer": "^0.9.0", "uuid": "^9.0.1", "xss": "^1.0.11"}, "devDependencies": {"@types/lodash": "^4.17.13", "@types/react": "^18.2.41", "@types/react-dom": "^18.2.17", "@types/uuid": "^10.0.0", "@umijs/preset-react": "1.x", "@umijs/test": "^3.5.23", "cross-env": "^7.0.3", "lint-staged": "^10.0.7", "prettier": "^2.2.0", "typescript": "^5.3.2", "yorkie": "^2.0.0"}}