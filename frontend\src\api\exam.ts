import HTTP from ".";

/**
 * @msg: 上传文件流
 * @param {any} data
 * @return {*}
 */
namespace examManageApis {
  export function uploadfile(data: any) {
    return HTTP.post(`/rman/v1/upload/save/file`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
  }
  export const getPagingData = (data: string) => {
    //获取分页元数据
    return HTTP(`/unifiedplatform/v1/base/data/database/source/data?${data}`, {
      method: 'GET'
    })
  }
  export const selectUsers = (data: string[]) =>
    HTTP(`/rman/v1/3rd/user/info`, {
      method: 'POST',
      data: {
        ids: data,
      },
    });
  export const initShareUsers = () =>
    HTTP(`/exam-api/DropDownBox/question/shareuser/list`, {
      method: 'GET'
    });
  // 获取适用专业
  export const fetchMajorLists = () =>
    HTTP(`/exam-api/DropDownBox/major/list`, {
      method: 'GET'
    });
  //试题新建
  export const addTopic = (data: any) =>
    HTTP(`/exam-api/examination/add`, {
      method: 'POST',
      data
    });
  //批量导入
  export const batchImport = (data: any) =>
    HTTP(`/exam-api/examination/upload`, {
      method: 'POST',
      data
    });
  //试题查询
  export const fetchTopicLists = (data: any) =>
    HTTP(`/exam-api/examination/list`, {
      method: 'POST',
      data
    });
  //试题详情
  export const fetchTopicDetail = (data: any) =>
    HTTP(`/exam-api/examination/detail/${data}`, {
      method: 'GET'
    });
  //试题预览
  export const topicPreview = (data: any) =>
    HTTP(`/exam-api/examination/individual/preview`, {
      method: 'GET'
    });
  //试题修改
  export const topicUpdate = (id: any, data: any) =>
    HTTP(`/exam-api/examination/update/${id}`, {
      method: 'POST',
      data
    });
  //试题批量复制
  export const topicBatchCopy = (data: any) =>
    HTTP(`/exam-api/examination/batch/clone`, {
      method: 'POST',
      data
    });
  //试题批量分享
  export const topicBatchShare = (data: any) =>
    HTTP(`/exam-api/examination/share`, {
      method: 'POST',
      data
    });
  //试题删除
  export const deleteTopics = (data: any, params?: { deleteType?: boolean }) =>
    HTTP(`/exam-api/examination/delete`, {
      method: 'POST',
      params,
      data
    });

  // 查询知识图谱
  export const fetchKnowledgeGraph = (name: any) =>
    HTTP(`/learn/m1/knowledge/query/usercode?name=${name}`, {
      method: 'GET'
    });
  // 查询知识图谱
  export const courseApiGraph = (name: any) =>
    HTTP(`/ipingestman/schedule/courseApi/?page=1&size=20&course_name=${name}`, {
      method: 'GET'
    });
  export interface IRecyleItem {
    /** 题的名称 */
    questions_content?: string
    /** 试题类型,0:单选,1:多选,2:填空,3:问答 4：判断、 题组：5 */
    questions_type?: string
    /** 试题难度 */
    questions_difficulty?: string
    /** 适用专业id，支持多选 */
    questions_major?: string[]
    /** 查询范围 1全部 2我创建得 3分享的 */
    knowledge_name?: string
    select_type?: number
    /** 适用课程 */
    lesson_name?: string
    /** 查询开始时间, yyyy-MM-dd HH:mm:ss */
    start_time?: string
    /** 查询结束时间, yyyy-MM-dd HH:mm:ss */
    end_time?: string
    /** 分页查询页码 */
    page: number
    /** 分页查询条数 */
    size: number
    /** 0:降序,1:升序 */
    sort?: string | number
    /** 排序的属性,0:按照创建时间排序,1:按照更新时间排序 */
    sortBy?: string | number
  }
  // 查询回收站试题列表
  export const queryRecyleList = (data: IRecyleItem) => {
    return HTTP(`/exam-api/examination/reclaim/list`, {
      method: 'POST',
      data
    })
  }

  // 清空回收站
  export const clearAllRecyle = () => {
    return HTTP(`/exam-api/examination/reclaim/restore/empty`, {
      method: 'POST'
    })
  }

  // 还原回收站
  export const restoreRecyle = (data: string[]) => {
    return HTTP(`/exam-api/examination/reclaim/restore`, {
      method: 'POST',
      data
    })
  }

  // 题目来源
  export const questionSource = () => {
    return HTTP(`/exam-api/questionSource/list`, {
      method: 'GET'
    })
  }

  // 专项训练分页
  export const fetchSpecialTraining = (params: { page: number, size: number, classifyId: string }) => {
    return HTTP(`/exam-api/test/stuPage`, {
      method: 'GET',
      params
    })
  }

  // 开始考试
  export const fetchExamStartInfo = (data: { testId: string }) => {
    return HTTP(`/exam-api/answer/startExam`, {
      method: 'POST',
      data
    })
  }

  export interface IQuestionItemAnswerProp {
    /** 考试ID */
    answerId: string
    /** 部分试题ID */
    partQuestionId: string
    /** 回答 */
    answer: string
    /** 题ID */
    questionId: string
    questionGroupAnswer?: any[]
  }
  // 提交题目答案
  export const postQuestionItemAnswer = (data: IQuestionItemAnswerProp) => {
    return HTTP(`/exam-api/answer/answer`, {
      method: 'POST',
      data
    })
  }

  // 继续考试
  export const fetchContinueExam = (data: { answerId: string }) => {
    return HTTP(`/exam-api/answer/continueExam`, {
      method: 'POST',
      data
    })
  }

  // 提交考试
  export const postSubmitExam = (data: { answerId: string, countdown: number, part: any[] }) => {
    return HTTP(`/exam-api/answer/submitExam`, {
      method: 'POST',
      data
    })
  }

  // 测验报告
  export const fetchExamReport = (params: { page: number, size: number, classifyId: string }) => {
    return HTTP(`/exam-api/answer/pageVo`, {
      method: 'GET',
      params
    })
  }

  // 测验报告详情
  export const fetchExamReportDetail = (id: string) => {
    return HTTP(`/exam-api/answer/listPartCountVo/${id}`, {
      method: 'GET'
    })
  }

  // 收藏题目
  export const fetchCollectQuestion = (data: { id: string, classifyId: string }) => {
    return HTTP(`/exam-api/collectQuestion/collect`, {
      method: 'POST',
      data
    })
  }

  // 获取已经回答的题目的答案记录
  export const fetchAnswerRecord = (params: { answerId: string, partQuestionId: string }) => {
    return HTTP(`/exam-api/testAnswerQuestion/list`, {
      method: 'GET',
      params
    })
  }

  // 同步倒计时
  export const syncCountdown = (data: { answerId: string, countdown: number, answerMillisecond: number, part: { partId: string, countdown: number, answerMillisecond: number }[] }) => {
    return HTTP(`/exam-api/answer/syncCountdown`, {
      method: 'POST',
      data
    })
  }

  export const TreecoureList = () => {
    return HTTP(`/exam-api/courseTree/coureList?page=1&pageSize=9999`, {
      method: 'POST',
    })
  }
  //2级
  export const chapterList = (data: any) => {
    return HTTP(`/exam-api/courseTree/chapterList`, {
      method: 'POST',
      data
    })
  }
  //3级
  export const sectionList = (data: any) => {
    return HTTP(`/exam-api/courseTree/sectionList`, {
      method: 'POST',
      data
    })
  }

}


export default examManageApis
