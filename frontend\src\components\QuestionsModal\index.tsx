import examManageApis from '@/api/exam';
import examType from '@/types/examType';
import { Button, Input, message, Modal, Select, Table } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import RenderHtml from '../renderHtml';
import "./index.less";
import { useLocation } from 'umi'
import Contract from '@/api/Contract';
import paperManageApis from '@/api/Contract';
interface params {
    title: any,
    selectkeys?: any,
    partIds: string,  // partIds ID
    visible: boolean,
    userData: any,  // 传递过来的表单数据
    Switchitem: any,//替换的项
    classifyID: string,
    userID: string,
    itemName: string,
    ppsucAction: Boolean,
    selectedValues?: any,
    updateData: (newUserData: any) => void;  // 更新数据的回调
    callback: (data: any) => void,
    onclose: () => void,
}

const QuestionsModal: React.FC<params> = (params) => {
    const { title, selectkeys, partIds, selectedValues, itemName, visible, ppsucAction, userID, callback, onclose, userData, updateData, Switchitem, classifyID } = params
    const [query, setQuery] = useState<any>({
        questions_content: undefined,
        questions_type: 5,
        testId: userID,
        partIds: partIds,
        page: 1,
        size: 10000000
    })
    const [data, setData] = useState<any>([]);
    const [total, setTotal] = useState<any>([]);
    const [selectRows, setSelectedRows] = useState<any>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);

    // 应用分类|考核方向
    const [initClassify, setInitClassify] = useState<any>([]);
    const [examDirection, setExamDirection] = useState<any>([]);
    const [initQuestionSource, setInitQuestionSource] = useState<any>([]);
    const [selectRows1, setselectRows1] = useState([]);  // 用于保存状态1的选中行
    const [selectRows2, setselectRows2] = useState([]);  // 用于保存状态2的选中行
    const [Visible, setVisible] = useState(1);  // 当前可见的状态，1 或 2


    useEffect(() => {
        fetchClassify();
        fetchQuestionSource();
    }, [])

    useEffect(() => {
        if (userID) {

            if (partIds) {
                const newQuery = {
                    ...query,
                    testId: userID,
                    partIds: partIds
                };
                fetchDataList(newQuery);

            } else {
                // 创建更新后的 query 对象
                const newQuery = {
                    ...query,
                    testId: userID,
                    // 根据 ppsucAction 决定是否设置 partIds
                    ...(ppsucAction ? {} : {
                        partIds: userData.part
                            ?.filter((item: any) => item.questionGroupScoreConfig === 2)
                            .map((item: any) => item.id)  // 这里直接返回 id，确保 partIds 是一个字符串数组
                    })
                };
                // 如果更新后的 query 与当前 query 不同，则更新 state
                if (JSON.stringify(newQuery) !== JSON.stringify(query)) {
                    setQuery(newQuery);
                }
            }
        }
    }, [userID, ppsucAction, userData, query, partIds]);

    useEffect(() => {
        if (partIds) {
        } else {
            if (visible && query.testId) {
                fetchDataList(query);
            }
        }

    }, [visible, query]);


    const fetchClassify = async () => {
        const res = await paperManageApis.classification({ page: 1, size: 100 });
        if (res.status === 200) {
            setInitClassify(res.data?.data || []);
            setExamDirection(res.data?.data?.[0]?.children || []);
        }
    };

    // 题目来源

    const fetchQuestionSource = async () => {
        const res = await examManageApis.questionSource();
        if (res.status === 200) {
            setInitQuestionSource(res.data);
        }
    };

    const nameChange = (e: any) => {
        setQuery({
            ...query,
            questions_content: e.target.value
        })
    }

    const labelNameChange = (e: any) => {
        setQuery({
            ...query,
            knowledge_points: e.target.value
        })
    }
    const knowledge_nameChange = (e: any) => {
        setQuery({
            ...query,
            labels: e.target.value
        })
    }

    const typeChange = (e: any) => {

        setQuery({
            ...query,
            questions_type: e
        })
    }

    const questionChange = (e: any) => {

        setQuery({
            ...query,
            questionSourceCode: e
        })
    }
    const applicationChange = (e: any) => {

        setQuery({
            ...query,
            applicationClassCode: e
        })
    }
    const assessmentCodeChange = (e: any) => {

        setQuery({
            ...query,
            assessmentCode: e
        })
    }

    const assessmentCodeChanges = (e: any) => {

        setQuery({
            ...query,
            assessmentCode: e
        })
    }

    const fetchDataList = async (value: any) => {
        // s数据 fetchTopicLists
        const res = await Contract.groupQuestionPage(value);
        if (res.status === 200) {
            setData(res.data?.data)
            setTotal(res.data?.totalCount)
            // 使用 filter 对数据进行分类
            const rows1 = res.data?.data.filter((item: any) => item.questionGroupScoreConfig == 1);
            const rows2 = res.data?.data.filter((item: any) => item.questionGroupScoreConfig == 2);
            // 更新选中的状态
            setselectRows1(rows1);
            setselectRows2(rows2);
            // 确保 selectedRowKeys 被设置为当前选中的行的 key
            const selectedKeys = [...rows1.map((item: { id: any; }) => item.id), ...rows2.map((item: { id: any; }) => item.id)];
            setSelectedRowKeys(selectedKeys);
        }
    };

    // 

    const updateGroupQuestionScoreConfigAPI = async (value: any) => {
        // s数据 fetchTopicLists
        const res = await Contract.updateGroupQuestionScoreConfig(value);
        if (res.status === 200) {
            fetchDataList(query)
            getVoByIdAPI(query.testId)
        }
    };

    const getVoByIdAPI = async (id: any) => {
        const res = await Contract.getVoById(id);
        if (res.status === 200) {
            updateData(res.data);  // 通知父组件更新数据
            localStorage.setItem('userData', JSON.stringify(userData));  // 将默认数据存储到本地
            callback(selectRows);
        };
    };

    const testSaveAPI = async (data: any) => {
        const res = await Contract.testSave(data);
        if (res.status === 200) {
            localStorage.setItem("userData", JSON.stringify(res.data));  // 将对象转化为字符串
        }
        message.success(res?.message);
    };

    const removeMathML = (html: string): string => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        // 移除数学公式相关的元素
        const mathElements = doc.querySelectorAll('.math-tex, mjx-container');
        mathElements.forEach((el) => el.remove());
        return doc.body.textContent || '';  // 获取去除数学公式后的纯文本内容
    };

    const columns: any = [
        Table.SELECTION_COLUMN,
        Table.EXPAND_COLUMN,
        {
            title: '题目类型',
            width: '13%',
            dataIndex: 'questions_type',
            key: 'questions_type',
            ellipsis: true,
            render: (item: any, record: any) => (
                <div>{examType.optionType_[Number(item)]}</div>
            )
        },
        {
            title: '难度',
            width: '10%',
            dataIndex: 'questions_difficulty',
            key: 'questions_difficulty',
            ellipsis: true
        },
        {
            title: '题目',
            width: '60%',
            dataIndex: 'questions_content',
            key: 'questions_content',
            ellipsis: true,
            render: (value: any) => {
                const extractedText = value ? removeMathML(value) : '';
                return (
                    <RenderHtml
                        cname="auto-img"
                        value={extractedText || '\u200B'}
                    />
                );
            },
        },
        {
            title: '答案',
            width: '10%',
            dataIndex: 'questions_answers',
            key: 'questions_answers',
            ellipsis: true,
            render: (value: any) =>
                value?.map((item: any, index: any) => (
                    <RenderHtml key={index} cname="auto-img" value={item}></RenderHtml>
                )),
        },
        {
            title: '创建人',
            width: '30%',
            dataIndex: 'add_username',
            key: 'add_username',
            ellipsis: true
        },
    ];

    const expandedRowRender = (expandList: any) => {
        const columns: any = [
            {
                title: '题目类型',
                width: '10%',
                dataIndex: 'questions_type',
                key: 'questions_type',
                ellipsis: true,
                render: (item: any, record: any) => (
                    <div>{examType.optionType_[Number(item)]}</div>
                ),
            },
            {
                title: '难度',
                width: '10%',
                dataIndex: 'questions_difficulty',
                key: 'questions_difficulty',
                ellipsis: true,
                render: ''
            },
            {
                title: '题目',
                width: '60%',
                dataIndex: 'questions_content',
                key: 'questions_content',
                ellipsis: true,
                render: (value: any) => (
                    <RenderHtml cname="auto-img" value={value}></RenderHtml>
                ),
            },
            {
                title: '答案',
                width: '10%',
                dataIndex: 'questions_answers',
                key: 'questions_answers',
                ellipsis: true,
                render: (value: any) =>
                    value?.map((item: any) => (
                        <RenderHtml cname="auto-img" value={item}></RenderHtml>
                    )),
            },
            {
                title: '创建人',
                width: '30%',
                dataIndex: 'add_username',
                key: 'add_username',
                ellipsis: true
            },

        ];

        return <Table sticky={false} style={{ paddingLeft: 31 }} columns={columns} showHeader={false} dataSource={expandList} pagination={false} />;
    };

    const rowSelection = {
        type: 'checkbox',
        onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
            if (Visible == 1) {
                const filteredRows = newSelectedRows.filter((row: any) =>
                    !selectRows2.some((selected: any) => selected.id == row.id)
                );
                setSelectedRowKeys(newSelectedRowKeys.filter(Boolean));
                setselectRows1(filteredRows);
            }
            else if (Visible == 2) {
                const filteredRows = newSelectedRows.filter((row: any) =>
                    !selectRows1.some((selected: any) => selected.id == row.id)
                );
                setSelectedRowKeys(newSelectedRowKeys.filter(Boolean));
                setselectRows2(filteredRows);
            }
        },
        preserveSelectedRowKeys: true,
        selectedRowKeys,
        getCheckboxProps: (record: any) => ({
            disabled:
                (Visible === 2 && selectRows1.some((row: any) => row.id == record.id)) || // 禁用 selectRows1 中选中的行
                (Visible === 1 && selectRows2.some((row: any) => row.id == record.id)), // 禁用 selectRows2 中选中的行
        }),
    };


    const toggleVisible = (value: React.SetStateAction<number>) => {
        setVisible(value);
    };

    const confirm = () => {
        const allCorrect = selectRows1.map((item: any) => item.id); // 提取 selectRows1 中的 id
        const partCorrect = selectRows2.map((item: any) => item.id); // 提取 selectRows2 中的 id
        const data = {
            "allCorrect": allCorrect,
            "partCorrect": partCorrect
        };
        var Correcttoal = allCorrect.length + partCorrect.length
        if (Correcttoal == total) {
            updateGroupQuestionScoreConfigAPI(data)
        } else {
            message.warning("请勾选对应试题完成判分设置");
        }
    }

    const search = () => {
        fetchDataList(query)
    }

    const reset = () => {  // 重置
        if (itemName) {
            setQuery({
                ...query,
                questions_content: '',
                applicationClassCode: '',
                knowledge_name: '',
                labelName: '',
                questionSourceCode: '',
                // questions_type: undefined
            })
        } else {
            // setExamDirection('')
            setQuery({
                ...query,
                questions_content: '',
                assessmentCode: '',
                applicationClassCode: '',
                questionSourceCode: '',
                knowledge_name: '',
                labelName: '',
                // questions_type: undefined
            })
        }
    }

    const close = () => {
        setSelectedRowKeys(selectkeys.map((item: any) => item.parent_id ? item.parent_id : item.id));
        setselectRows2([]);
        toggleVisible(1)
        setselectRows1([]);
        onclose();
    }

    return (
        <Modal
            title={title}
            visible={visible}
            onCancel={close}
            width={944}
            maskClosable={false}
            className='questions_modal'
            footer={[
                <Button
                    type='primary'
                    onClick={confirm}
                // disabled={selectRows.length === 0}
                >
                    确定
                </Button>,
                <Button
                    onClick={close}
                >
                    取消
                </Button>,
            ]}
        >
            <div className='header'>
                <div onClick={() => toggleVisible(1)} className={Visible == 1 ? 'acton' : 'box'}>任意小题错误整个题组不得分</div>
                <div onClick={() => toggleVisible(2)} className={Visible == 2 ? 'acton' : 'box'}>任意小题错误只该小题不得分</div>
            </div>
            {/* <div className='searchbox' style={{ marginBottom: ' 10px' }} >
                {!itemName && (
                    <Select
                        placeholder="题目来源"
                        value={query.questionSourceCode || null}
                        onChange={questionChange}
                        options={initQuestionSource.map((item: any) => {
                            return {
                                value: item.questionSourceCode,
                                label: item.questionSourceName
                            }
                        })} />
                )}

                {!itemName && (
                    <Select placeholder="应用分类"
                        onChange={applicationChange}
                        value={query.applicationClassCode || null}
                        options={initClassify.map((item: any) => {
                            return {
                                value: item.id,
                                label: item.name,
                                children: item.children || []
                            }
                        })} />
                )}


                {!itemName && (
                    <Select
                        placeholder="考核方向"
                        onChange={assessmentCodeChanges}
                        value={query.assessmentCode || null}
                        options={examDirection.map((item: any) => {
                            return {
                                value: item.id,
                                label: item.name
                            }
                        })} />
                )}
            </div> */}
            <div className='searchbox'>

                <Input placeholder='请输入名称' onChange={nameChange} value={query.questions_content} />
                <Input placeholder='请输入知识点' onChange={labelNameChange} value={query.labelName} />
                <Input placeholder='请输入标签' onChange={knowledge_nameChange} value={query.knowledge_name} />
                <Button
                    style={{ marginRight: '10px' }}
                    type='primary'
                    onClick={search}
                >
                    搜索
                </Button>
                <Button
                    type='primary'
                    onClick={reset}
                >
                    重置
                </Button>
            </div>
            <div>
                <Table
                    dataSource={data}
                    rowKey={"id"}
                    columns={columns}
                    rowSelection={rowSelection as any}
                    // pagination={false}
                    pagination={{
                        position: ['bottomCenter'],
                        showSizeChanger: true,
                        total: total,
                        showQuickJumper: true,
                        onChange: (page: number, size: any) => {
                            // setQuery({
                            //     ...query,
                            //     page,
                            //     size
                            // });
                        },
                        showTotal: total => `共 ${total} 条`,
                        size: 'small'
                    }}
                    expandable={{
                        expandedRowRender: record => expandedRowRender(record?.groupQuestions || []),
                        rowExpandable: record => record.questions_type == 5,
                        expandedRowClassName: () => 'group_expand'
                    }}
                    scroll={{ y: '420px', x: 1000 }}
                />
            </div>
        </Modal >
    )
}

export default QuestionsModal