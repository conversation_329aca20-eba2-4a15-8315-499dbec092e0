import examManageApis from '@/api/exam';
import examType from '@/types/examType';
import { Button, Input, Modal, Select, Table } from 'antd';
import React, { useEffect, useState } from 'react';
import RenderHtml from '../renderHtml';
import "./index.less";

interface params {
    title: any,
    selectkeys?: any,
    type?: string,// 单选还是多选
    visible: boolean,
    callback: (data: any) => void,
    onclose: () => void,
}

const TopicModal: React.FC<params> = (params) => {
    const { title, selectkeys, type, visible, callback, onclose } = params
    const [query, setQuery] = useState<any>({
        questions_content: undefined,
        questions_type: undefined,
        page: 1,
        size: 10
    })
    const [data, setData] = useState<any>([]);
    const [total, setTotal] = useState<any>([]);
    const [selectRows, setSelectedRows] = useState<any>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
    const type_enum = examType.optionType_; //题目类型
    useEffect(()=>{
        if(selectkeys.length>0){
            console.log('selectkeys',selectkeys)
            setSelectedRowKeys(selectkeys.map((item:any)=>item.parent_id?item.parent_id:item.id));
        }
    },[selectkeys])
    const nameChange = (e: any) => {
        console.log(e)
        setQuery({
            ...query,
            questions_content:e.target.value
        })
    }
    const typeChange = (e: any) => {
        console.log(e)
        setQuery({
            ...query,
            questions_type:e
        })
    }
    const fetchDataList = async () => {
        const res = await examManageApis.fetchTopicLists(query);
        console.log(res);
        if (res.status === 200) {
            setData(res.data?.data)
            setTotal(res.data?.totalCount)
        }
    };
    useEffect(() => {
        fetchDataList()
    }, [query])
    const columns: any = [
        // {
        //   title: '序号',
        //   width: '5%',
        //   ellipsis: true,
        //   render: (item: any, record: any, index: any) => (
        //     <div>{index + 1}</div>
        //   )
        // },
        {
            title: '类型',
            // width: '10%',
            dataIndex: 'questions_type',
            key: 'questions_type',
            ellipsis: true,
            render: (item: any, record: any) => (
                <div>{examType.optionType_[Number(item)]}</div>
            )
        },
        {
            title: '难度',
            // width: '5%',
            dataIndex: 'questions_difficulty',
            key: 'questions_difficulty',
            ellipsis: true
        },
        {
            title: '题目',
            // width: '15%',
            dataIndex: 'questions_content',
            key: 'questions_content',
            ellipsis: true,
            render: (value: any) => (
                <RenderHtml cname="spcialDom" value={value}></RenderHtml>
            ),
        },
        {
            title: '答案',
            // width: '5%',
            dataIndex: 'questions_answers',
            key: 'questions_answers',
            ellipsis: true,
            // render: (value: any, record: any) => (
            //     <div>{value?.join(',')}</div>
            // )
            render: (value: any) =>
                value?.map((item: any) => (
                  <RenderHtml cname="auto-img" value={item}></RenderHtml>
                )),
        },
        {
            title: '创建人',
            // width: '8%',
            dataIndex: 'add_username',
            key: 'add_username',
            ellipsis: true
        },
    ];
    const rowSelection = {
        type: type,
        onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
            setSelectedRowKeys(newSelectedRowKeys.filter(Boolean));
            setSelectedRows(newSelectedRows.filter(Boolean)); //得处理勾选移除后的残余空值对象
            console.log('query',query)
        },
        preserveSelectedRowKeys: true,
        selectedRowKeys,
    };
    const confirm = () => {
        callback(selectRows);
    }
    const reset = () => {
        setQuery({
            ...query,
            questions_content:'',
            questions_type:undefined
        })
    }
    const close = ()=>{
        setSelectedRowKeys(selectkeys.map((item:any)=>item.parent_id?item.parent_id:item.id));
        onclose();
    }
    return (
        <Modal
            title={title}
            visible={visible}
            onCancel={close}
            width={944}
            className='topic_modal'
            footer={[
                <Button
                    type='primary'
                    onClick={confirm}
                    disabled={selectRows.length === 0}
                >
                    确定
                </Button>,
                <Button
                    onClick={close}
                >
                    取消
                </Button>,
            ]}
        >
            <div className='searchbox'>
                <Input placeholder='请输入名称' onChange={nameChange} value={query.questions_content}/>
                <Select placeholder='题目类型' onChange={typeChange} value={query.questions_type}>
                    {
                        type_enum.map((item: any, index: number) => (
                            <Select.Option
                                value={index}
                                key={item + index}
                            >
                                {item}
                            </Select.Option>
                        ))
                    }
                </Select>
                <Button
                    type='primary'
                    onClick={reset}
                >
                    重置
                </Button>
            </div>
            <div>
                <Table
                    dataSource={data}
                    rowKey={"id"}
                    columns={columns}
                    // key={selectedRowKeys}
                    rowSelection={rowSelection as any}
                    pagination={{
                        position: ['bottomCenter'],
                        showSizeChanger: true,
                        total: total,
                        showQuickJumper: true,
                        onChange: (page: number, size: any) =>
                            setQuery({
                                ...query,
                                page,
                                size
                            }),
                        showTotal: total => `共 ${total} 条`,
                        size:'small'
                    }}
                    scroll={{ y: '420px' }}
                />
            </div>
        </Modal>
    )
}

export default TopicModal