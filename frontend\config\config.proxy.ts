// const baseUrl = 'http://172.16.151.200';
// const baseUrl = 'http://172.19.6.78:8085'; // 测试接口
const baseUrl = 'https://172.16.151.200';
// const baseUrl = '';
// const baseUrl = 'http://172.16.151.229';
const proxy = {
  // 代理配置
  '/api': {
    target: `${baseUrl}/rman/v1`,
    // target: baseUrl.indexOf('/learn')===-1?`${baseUrl}/rman/v1`:baseUrl,
    // target: 'http://101.32.35.29:10080/rman/v1',
    // target: 'http://119.3.164.38:30856/rman/v1',
    // 'target': 'http://rap2.taobao.org:38080/app/mock/168113',//数据mock
    changeOrigin: true,
    secure: false, // 支持https代理
    pathRewrite: { '^/api': '' },
    secure: false, // 支持https代理
  },
  '/pubagent': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/rman': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/unifiedplatform': {
    // target: 'http://101.32.35.29:10080',
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/exam-api': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/pluginwiris_engine': {
    target: 'http://math.liwulin.top',
    pathRewrite: { '^/pluginwiris_engine': '/pluginwiris_engine' },
    changeOrigin: true,
    secure: false,
  },
  '/learn': {
    // target: 'http://101.32.35.29:10080',
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/pdfview': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/ff': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
    pathRewrite: { '^/ff': '' },
    secure: false, // 支持https代理
  },
  '/osapi': {
    target: baseUrl,
    // target: 'http://101.32.35.29:10080',
    changeOrigin: true,
    secure: false, // 支持https代理
    pathRewrite: { '^/osapi': '' },
    secure: false, // 支持https代理
  },
  '/bucket-k': {
    target: baseUrl,
    // target: 'http://101.32.35.29:10080',
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/bucket-p': {
    target: baseUrl,
    // target: 'http://101.32.35.29:10080',
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/bucket-z': {
    target: baseUrl,
    // target: 'http://101.32.35.29:10080',
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/unifiedlogin': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/sensitiveword': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/matheditor': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/medicalcaselib': {
    target: baseUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
};
export default proxy;
