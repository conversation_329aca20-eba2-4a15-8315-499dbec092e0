/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/5/5.
 */
const path = require('path');
const { generateTheme } = require('antd-theme-generator');
const options = {
  stylesDir: path.join(__dirname, './src/themes'),
  antDir: path.join(__dirname, './node_modules/antd'),
  varFile: path.join(__dirname, './src/themes/variables.less'),
  mainLessFile: path.join(__dirname, './src/themes/variables.less'),
  themeVariables: [
    //需要动态切换的主题变量
    '@primary-color',
    '@second-color',
    "@third-color",
  ],
  indexFileName: 'index.html',
  outputFilePath: path.resolve(__dirname, './public/antdtheme.less'), //页面引入的主题变量文件
};

generateTheme(options)
  .then(less => {
    console.log('less Theme generated successfully');
  })
  .catch(error => {
    console.log('Error', error);
  });
