import React, { FC, useEffect, useState } from "react";
import { HeaderComponent } from "./react.js";
import "./style.css";

const NPUHeader: FC = () => {
  const [jwt, setJwt] = useState<string>("");
  useEffect(() => {
    if ((window as any).login_useInfo?.extend) {
      setJwt((window as any).login_useInfo?.extend?.jwt || "");
    }
  }, [(window as any).login_useInfo]);
  return <HeaderComponent token={jwt} />;
};

export default NPUHeader;