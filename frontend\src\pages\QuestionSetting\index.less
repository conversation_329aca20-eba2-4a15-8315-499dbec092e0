.teaching-plan {
  height: 100%;
  background: url(../../assets/img/teachingPlan/content_bg.png);
  background-size: cover;
  .content {
    width: 100%;
    display: flex;
    height: 100%;
    padding: 30px;
    .left {
      width: 452px;
      height: 100%;
      border-radius: 20px;
      box-shadow: 0px 0px 40px 0px rgba(106, 117, 133, 0.15);
      background: url(../../assets/img/teachingPlan/left_bg.png)
        no-repeat;
      background-size: 100% 100%;
      padding: 15px;
      margin-right: 30px;
      .left-title {
        width: 100%;
        padding: 15px;
        display: flex;
        align-items: center;
        img {
          &:nth-child(1) {
            margin-right: 17px;
          }
        }
      }
      .teaching-plan-mode {
        padding: 5px 15px 15px 15px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        .mode-item {
          width: 48%;
          padding: 15px;
          height: 50px;
          display: flex;
          align-items: center;
          background: #ffffff;
          box-shadow: 2px 2px 11px 0px
            rgba(192, 192, 192, 0.5);
          border-radius: 25px;
          font-weight: 400;
          font-size: 16px;
          color: #2e2e2e;
          cursor: pointer;
          img {
            margin-right: 5px;
          }
        }
        .active-mode-item {
          background: linear-gradient(
            319deg,
            #7a43ff 0%,
            #7c91fc 100%
          );
          color: #fff;
        }
      }
      .set-teaching-plan-content {
        height: calc(100% - 272px);
        overflow-y: auto;
        margin-bottom: 10px;
        padding: 0 15px 15px 15px;
        &::-webkit-scrollbar {
          width: 5px;
        }
        /* 设置滚动条轨道 */
        &::-webkit-scrollbar-track {
          background: transparent;
        }
        /* 设置滚动条滑块 */
        &::-webkit-scrollbar-thumb {
          background-color: #d1d0d0;
          border-radius: 3px;
        }
        // 教案模板
        .template-content {
          margin-top: 15px;
          .template-title {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 18px;
            color: #2e2e2e;
            margin-bottom: 10px;
            img {
              margin-right: 5px;
            }
          }
          .template-list {
            background: #ffffff;
            box-shadow: 2px 2px 11px 0px
              rgba(192, 192, 192, 0.5);
            border-radius: 15px;
            padding: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            .template-item {
              display: flex;
              flex-direction: column;
              width: 33.3%;
              height: 126px;
              padding: 5px;
              border-radius: 6px;
              border: 1px solid #e2e2e2;
              margin-bottom: 15px;
              .ant-image,
              .ant-image-mask {
                width: 100%;
                height: 80px;
                border-radius: 5px;
              }
            }
          }
        }
        // 题目设置
        .questionSetting {
          margin-top: 15px;
          .setting-title {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 18px;
            color: #2e2e2e;
            margin-bottom: 10px;
            img {
              margin-right: 5px;
            }
          }
          .setting-content { 
            background: #ffffff;
            box-shadow: 2px 2px 11px 0px rgba(192, 192, 192, 0.5);
            border-radius: 15px;
            padding: 20px 15px 20px 20px;
            .question-type-Number {
              .title {
                font-size: 14px;
                color: #2E2E2E;
                margin-bottom: 8px;
                span {
                  color: #CA0A0A;
                }
              }
              .item {
                display: flex;
                align-items: center;
                margin-bottom: 6px;
                .ant-select {
                  margin-right: 20px;
                  width: 190px;
                }
                .ant-select-selector {
                  border-radius: 6px;
                }
                .ant-select-focused .ant-select-selector, .ant-select .ant-select-selector:hover {
                  border-color: #e2e2e2;
                  box-shadow: none;
                }
                .ant-input-number {
                  border-radius: 6px;
                  width: 112px;
                }
                .ant-input-number-focused, .ant-input-number:hover {
                  border-color: #e2e2e2;
                  box-shadow: none;
                }
                .anticon.anticon-close-circle {
                  margin-left: 10px;
                  color: #97989C;
                  font-size: 20px;
                }
              }
            }
            .bottom {
              margin-top: 20px;
              .itemFirst {
                margin-bottom: 14px;
              }
              .item {
                .title {
                  display: flex;
                  margin-bottom: 8px;
                  font-weight: 400;
                  font-size: 14px;
                  color: #2E2E2E;
                  .title-left {
                    width: 161px;
                    margin-right: 12px;
                  }
                }
                .select-input {
                  display: flex;
                  .ant-form-item {
                    margin-bottom: 0;
                  }
                  .ant-select {
                    margin-right: 10px;
                    width: 161px;
                  }
                  .ant-select-selector {
                    border-radius: 6px;
                  }
                  .ant-select-focused .ant-select-selector, .ant-select .ant-select-selector:hover {
                    border-color: #e2e2e2;
                    box-shadow: none;
                  }
                  .ant-select-multiple.ant-select-show-arrow .ant-select-selector, .ant-select-multiple.ant-select-allow-clear .ant-select-selector {
                    padding-right: 0px;
                    height: 32px;
                  }
                }
                .ant-select-selection-overflow-item .ant-select-selection-item {
                  padding: 0px 3px !important;
                }
              }
            }
            .add-btn {
              margin: 20px 0 0;
              display: flex;
              align-items: center;
              color: #7a53fe;
              margin-top: 20px;
              cursor: pointer;
              .anticon {
                margin-right: 5px;
              }
            }
          }
        }
        .require-set-content {
          .add-box {
            min-height: 150px;
          }
          .add-box,
          .add-content {
            // min-height: 150px;
            padding: 15px;
            background-color: #fff;
            box-shadow: 2px 2px 11px 0px
              rgba(192, 192, 192, 0.5);
            border-radius: 15px;
            font-size: 14px;
            color: #2e2e2e;
            margin-bottom: 15px;
            .file-list {
              .file-item {
                display: flex;
                align-items: center;
                height: 37px;
                border-radius: 6px;
                border: 1px dashed #d4ceff;
                padding-right: 10px;
                margin: 10px 0;
                .file-name {
                  width: 100%;
                  white-space: nowrap; /* 不换行 */
                  overflow: hidden; /* 超出隐藏 */
                  text-overflow: ellipsis; /* 超出部分显示省略号 */
                }
                .anticon-close-circle {
                  margin-left: auto;
                }
              }
            }
            .add-btn {
              display: flex;
              align-items: center;
              color: #7a53fe;
              margin-top: 20px;
              cursor: pointer;
              .anticon {
                margin-right: 5px;
              }
            }
          }
          .add-box {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            cursor: pointer;
            &:hover {
              color: #7a43ff;
            }
            .anticon {
              margin-right: 8px;
              font-size: 18px;
              color: #5c43ff;
              font-weight: bold;
            }
          }
          .text-input-aera {
            height: 160px;
            box-sizing: border-box;
            padding: 20px;
            background-color: #fff;
            box-shadow: 2px 2px 11px 0px
              rgba(192, 192, 192, 0.5);
            border-radius: 15px;
            font-size: 14px;
            color: #2e2e2e;
            margin-bottom: 20px;
            .header {
              display: flex;
              align-items: center;
              margin-bottom: 5px;
              span {
                color: #868686;
              }
            }
            .ant-input {
              padding-left: 0;
            }
          }
          .cailiao-upload {
            height: 150px;
            padding: 15px;
            border-radius: 15px;
            background-color: #fff;
            box-shadow: 2px 2px 11px 0px
              rgba(192, 192, 192, 0.5);
            color: #2e2e2e;
            margin-bottom: 15px;
            .ant-upload-drag {
              height: 60px;
              margin-bottom: 20px;
              border-radius: 5px;
              .ant-upload-btn {
                display: flex;
                justify-content: center;
                align-items: center;
              }
              .ant-upload-drag-container {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
              }
              .ant-upload-drag-icon {
                font-size: 16px;
                display: flex;
                align-items: center;
                margin-bottom: 0px;
                .anticon {
                  margin-right: 8px;
                  font-size: 18px;
                  color: #5c43ff;
                  font-weight: bold;
                }
              }
            }
            .ant-upload-text {
              font-size: 14px;
              color: #868686;
              margin: 2px 0;
              a {
                color: #7a43ff;
                text-decoration: underline;
              }
            }
          }
        }
      }
      .ant-btn {
        width: 100%;
        height: 50px;
        background: linear-gradient(
          319deg,
          #7a43ff 0%,
          #7c91fc 100%
        );
        border-radius: 25px;
        color: #fff;
        font-weight: 600;
        font-size: 18px;
      }
    }
    .right {
      width: calc(100% - 482px);
      height: 100%;
      border-radius: 20px;
      box-shadow: 0px 0px 40px 0px rgba(106, 117, 133, 0.15);
      background: url(../../assets/img/teachingPlan/right_bg.png)
        no-repeat;
      background-size: 100% 100%;
      padding: 15px;
      .plan-content {
        height: calc(100% - 30px);
        .right-header {
          display: flex;
          align-items: center;
          font-weight: 600;
          font-size: 18px;
          color: #2e2e2e;
          margin: 15px;
          img {
            margin-right: 10px;
          }
          .ant-btn {
            margin-left: auto;
            background: linear-gradient(
              319deg,
              #5c43ff 0%,
              #7c91fc 100%
            );
            border-radius: 25px;
            color: #fff;
            width: 139px;
            height: 50px;
          }
        }
        .topic-list {
          padding: 15px;
          height: calc(100% - 50px);
          overflow-y: auto;
          background-color: #fff;
          .topic-item {
            padding: 5px;
            border-radius: 10px;
            box-shadow: 2px 2px 11px 0px
              rgba(192, 192, 192, 0.5);
            margin-bottom: 20px;
            .topic-header {
              height: 72px;
              background: #eceffe;
              border-radius: 10px;
              display: flex;
              align-items: center;
              padding: 20px;
              .topic-type {
                width: 17px;
                height: 17px;
                border-radius: 50%;
                background: #DED9FF;
                margin-right: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #7949FE;
                font-size: 12px;
              }
              .topic-title {
                font-weight: 400;
                font-size: 14px;
                color: #868686;
              }
              .ant-space {
                margin-left: auto;
                .ant-btn {
                  width: 110px;
                  height: 40px;
                  background: #ffffff;
                  border-radius: 20px;
                }
                .btn-primary {
                  background: linear-gradient(
                    319deg,
                    #7a43ff 0%,
                    #7c91fc 100%
                  );
                  color: #fff;
                }
              }
            }
            .topic-content {
              padding: 10px;
              .textarea {
                padding: 10px;
                height: unset;
                max-height: 400px;
                border-radius: 10px;
              }
              .notShowborder, .notShowborder.ant-input:focus, .notShowborder.ant-input:hover {
                  border-color: #fff;
                  box-shadow: none;
              }
              .showborder, .showborder.ant-input:focus, .showborder.ant-input:hover {
                  border-color: #7a53fe;
                  box-shadow: none;
              }

              .editContent {
                display: flex;
                padding: 15px 20px 0px 10px;
                flex-direction: column;
                .item {
                  margin-bottom: 10px;
                  display: flex;
                  .theleft {
                    margin-right: 6px;
                    width: 42px;
                    flex-shrink: 0;
                    padding-top: 10px;
                  }
                  .theright {
                    flex: 1;
                  }
                  .ant-input {
                    padding: 10px;
                    height: unset;
                    border-radius: 10px;
                    resize: none;
                    max-height: 300px;
                  }
                  .ant-input:focus, .ant-input:hover {
                    border-color: #d9d9d9;
                    box-shadow: none;
                  }
                }
                .title .theright {

                }
                .options .theright .option {
                  display: flex;
                  margin-bottom: 10px;
                  .option-left {
                    margin-right: 10px;
                    padding-top: 10px;
                    .ant-radio-wrapper {
                      margin-right: 4px;
                    }
                  }
                  .option-right {
                    flex: 1;
                  }
                }
                .options .theright {
                  .ant-radio-wrapper {
                    margin-right: 10px !important;
                  }
                  .option-radioGroup {
                    padding: 10px 0;
                  }
                  .ant-radio-group {
                    display: flex;
                    align-items: center;
                    .ant-space {
                      width: 100%;
                    }
                    .ant-radio-wrapper {
                      display: flex;
                      align-items: center;
                      margin-right: 0;
                      .ant-radio {
                        margin-bottom: 4px;
                      }
                      >span:last-child {
                        width: calc(100% - 15px);
                        padding-right: 0;
                      }
                      .option-right {
                        display: flex;
                        align-items: center;
                        >div {
                          margin-right: 10px;
                        }
                        textarea {
                          flex: 1;
                        }
                      }
                    }
                  }
                  .ant-checkbox-group {
                    display: flex;
                    align-items: center;
                    .ant-space{
                      width: 100%;
                    }
                    .ant-space-item {
                      display: flex;
                      align-items: center;
                      .ant-checkbox-wrapper {
                        width: 100%;
                        display: flex;
                        align-items: center;
                        >span:last-child {
                          width: calc(100% - 15px);
                          padding-right: 0;
                        }
                        .option-right {
                          display: flex;
                          align-items: center;
                          >div {
                            margin-right: 10px;
                          }
                          textarea {
                            flex: 1;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      .empty {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: -34px;
        p {
          font-weight: 600;
          font-size: 24px;
          color: #2e2e2e;
          margin-top: -50px;
        }
      }
      .loading-page {
        padding: 40px;
        .loading-header {
          display: flex;
          align-items: center;
          margin-bottom: 30px;
          font-weight: 600;
          font-size: 18px;
          color: #2e2e2e;
          img {
            margin-right: 6px;
          }
        }
        .loading-content {
          height: 320px;
          background: url(../../assets/img/teachingPlan/tikuang_bg.png)
            no-repeat;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          font-weight: 600;
          font-size: 24px;
          color: #2e2e2e;
          .animation-content {
            width: 352px;
            height: 270px;
          }
        }
      }
    }
  }
}
.map-modal-wrapper {
  .ant-modal {
    height: 100%;
    .ant-modal-body {
      height: calc(100% - 108px);
    }
    .ant-tabs, .ant-tabs-content, .ant-tabs-tabpane, .ant-pro, .ant-pro-table, .ant-table-wrapper, .ant-spin-nested-loading, .ant-spin-container {
      height: 100%;
    }
    .ant-table {
      height: calc(100% - 26px);
      overflow: auto;
    }
    .ant-pro-card:nth-child(2) {
      height: calc(100% - 80px);
    }
  }
  .ant-modal-body {
    padding-top: 0;
    .ant-tabs-nav {
      margin: 0;
    }
  }
  .ant-modal-content {
    height: 80%;
  }
  table {
    .ant-table-tbody {
      max-height: 200px;
      overflow-y: auto;
    }
  }
}
.chapter-modal-wrapper {
  overflow: hidden!important;
  .chapter-content {
    .ant-pagination {
      display: flex;
      justify-content: center;
    }
    .chapter-list {
      margin-top: 20px;
      margin-bottom: 20px;
      .chapter-card-checked {
        background-color: #ded9ff;
        border-color: #7a43ff;
      }
      .chapter-card-checked::after {
        opacity: 1;
        border: 10px solid #7a43ff;
        border-block-end: 10px solid transparent;
        border-inline-start: 10px solid transparent;
        border-start-end-radius: 6px;
      }
      .chapter-card {
        display: flex;
        padding: 10px;
        border: 1px dashed #e0e0e0;
        border-radius: 6px;
        cursor: pointer;
        height: 140px;
        &::after {
          position: absolute;
          inset-block-start: 4px;
          inset-inline-end: 12px;
          width: 0;
          height: 0;
          opacity: 1;
          transition: all 0.3s
            cubic-bezier(0.645, 0.045, 0.355, 1);
          border-block-end: 10px solid transparent;
          border-inline-start: 10px solid transparent;
          border-start-end-radius: 6px;
          content: '';
        }
        img {
          width: 150px;
          height: 110px;
          margin-right: 5px;
        }
        .card-content {
          width: calc(100% - 150px);
          font-size: 14px;
          color: #868686;
          p {
            margin: 0;
          }
          .title {
            color: #2e2e2e;
            font-size: 16px;
            font-weight: 500;
            display: -webkit-box; /* 创建伸缩盒子模型 */
            -webkit-box-orient: vertical; /* 垂直排列子元素 */
            -webkit-line-clamp: 2; /* 显示两行 */
            overflow: hidden; /* 超出隐藏 */
            text-overflow: ellipsis; /* 省略号 */
            /* 兼容性更好可以加上下面两行 */
            word-break: break-word;
            white-space: normal;
          }
          .semester,
          .teacher,
          .school {
            width: 100%;
            white-space: nowrap; /* 不换行 */
            overflow: hidden; /* 超出隐藏 */
            text-overflow: ellipsis; /* 超出部分显示省略号 */
          }
        }
      }
    }
  }
  .ant-modal {
    height: 86%;
    padding-bottom: 0;
    .ant-modal-content {
        height: 100%;
    }
    .ant-modal-body {
        height: calc(100% - 107px);
        .ant-tabs {
            height: 100%;
            .ant-tabs-content-holder {
                height: calc(100% - 62px);
                .ant-tabs-content, .ant-tabs-tabpane, .chapter-content {
                    height: 100%;
                    .ant-row {
                        height: calc(100% - 83px);
                        overflow: auto;
                    }
                }
            }
        }
    }
}
}
.chapter-modal-tree-wrapper {
  .course-name {
    font-size: 16px;
    margin-bottom: 10px;
  }
}
.gray-row {
  background-color: #f5f5f5; // 浅灰色背景
  color: #999; // 灰色文字
}