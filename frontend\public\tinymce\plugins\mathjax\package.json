{"name": "@dimakorotkov/tinymce-mathjax", "version": "1.0.11", "description": "Plugin using MathJax library for rendering math font", "main": "plugin.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/dimakorotkov/tinymce-mathjax.git"}, "keywords": ["<PERSON><PERSON><PERSON>", "mathjax"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/dimak<PERSON><PERSON><PERSON>/tinymce-mathjax/issues"}, "homepage": "https://github.com/dimak<PERSON>t<PERSON>/tinymce-mathjax#readme"}