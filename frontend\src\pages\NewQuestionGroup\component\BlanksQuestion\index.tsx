/**
 * 填空题
 */
import { Editor, IconFont } from '@/components';
import Retract from '@/images/icons/retract.png';
import Unfold from '@/images/icons/unfold.png';
import { getSensitiveWordPost } from '@/utils';
import { CloseOutlined, PaperClipOutlined } from '@ant-design/icons';
import { Button, Input, message, Select, Upload } from 'antd';
import { FC, useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { IQuestionComponentProp, IQuestionGropItem } from '../../types';
import {
  fetchEditorContent,
  formatFileList,
  handleDelEditor,
  initOptions,
} from '../../utils';
import './index.less';

const BlankQuestion: FC<IQuestionComponentProp> = (props) => {
  const { detail, itemIndex, editType, onChangeItem, onDeleteTopic } = props;

  const [options, setOptions] = useState<any[]>([]); //选项内容
  const optionRef = useRef<any>([]);
  const uploadRef = useRef<any>(null);
  const [answer, setAnswer] = useState<any>(undefined);
  // 试题是否隐藏
  const [isShowQuestion, setIsShowQuestion] = useState(true);
  // 添加空格标识
  const isAddBlank = useRef(false);
  // 小题的数据副本
  const initData = useRef<IQuestionGropItem>(detail);

  // id标识
  const blankId = useRef('blank_topic_' + detail.id);
  const analysisId = useRef('blan_analysis_' + detail.id);

  // 填空题
  const selectOptions = [
    { value: 1, label: '文本' },
    { value: 2, label: '数字' },
  ];
  const selectNumberType = [
    { value: 1, label: '唯一答案' },
    { value: 2, label: '范围' },
  ];

  //#region
  // options的修改
  const updateOptions = (value: any) => {
    setOptions(value);
    optionRef.current = value;
    handleUpdate('questions_options', value);
  };
  //更新数据
  const handleUpdate = (type: keyof IQuestionGropItem, value: any) => {
    onChangeItem(type, value, initData.current.id);
  };
  //#endregion
  //#region 附件相关
  const [fileList, setFileList] = useState<any>([]);
  //附件添加
  const uploadProps = {
    name: 'file',
    action: '/rman/v1/upload/reference/material/import',
    headers: {
      authorization: 'authorization-text',
    },
    beforeUpload: async (file: any, list: any) => {
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        message.error('上传文件不能超过100M');
      }
      if (fileList.length + 1 > 10) {
        message.info('上传文件数量不能超过10个');
        return false;
      }
      let names = list.map((e: any) => e.name).join('');
      let res: any = await getSensitiveWordPost(
        names,
        '附件名',
        () => true,
        () => false,
      );
      if (!res) return Upload.LIST_IGNORE;
      return isLt100M || Upload.LIST_IGNORE; //隐藏不符合列表的文件
    },
    fileList,
    showUploadList: {
      removeIcon: <CloseOutlined />,
    },
    maxCount: 10,
    onChange(info: any) {
      setFileList(info.fileList);
      handleUpdate('fileList', info.fileList);
      if (info.file.status !== 'uploading') {
      }
      if (info.file.status === 'done' && info.file.response?.success) {
        message.success(`${info.file.name} 上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败.`);
      } else if (info.file.status === 'done' && !info.file.response?.success) {
        message.error(`${info.file.name} 上传失败,不支持该格式`);
        const cloneList = fileList.map((item: any) => {
          if (item.uid === info.file.uid) {
            return {
              ...item,
              status: 'error',
            };
          }
          return item;
        });
        setFileList(cloneList);
        handleUpdate('fileList', cloneList);
      }
    },
  };
  //#endregion
  const topicChange = (e: any, item: string) => {
    const data = e.level?.content;
    if (item === blankId.current) {
      if (isAddBlank.current) {
        isAddBlank.current = false;
        return;
      }
      //通过e.target.content获取的内容可能不是想要的数据，并且直接使用getContent会使图片的src是相对路径，跨项目引用会访问不到,只能手动避开；
      const realTopic = fetchEditorContent(blankId.current) ? data : '';
      console.log(data, realTopic, 'change///');

      if (realTopic === undefined) return; //处理插入化学公式会出现的bug;
      handleUpdate('questions_content', realTopic);
    } else {
      const realAnalysis = fetchEditorContent(analysisId.current) ? data : '';
      handleUpdate('questions_analysis', realAnalysis);
    }
  };

  const addBlanks = () => {
    const temp = JSON.parse(JSON.stringify(optionRef.current));

    temp.push({
      seq: temp.length + 1,
      content: '',
      answerType: 1,
      answerRange: null,
      answerMax: null,
      answerMin: null,
      uid: uuidv4(),
    });
    updateOptions(temp);
    isAddBlank.current = true;
    (window as any).tinymce.editors[blankId.current].insertContent(
      `<span>_______</span>`,
    );
  };

  // 填空题的改变
  type TChangeType = 'type' | 'numberType' | 'value'; // 填空类型|数字类型|值
  const handleChangeBlank = (
    e: any,
    item?: any,
    type?: TChangeType,
    key?: 'answerMax' | 'answerMin' | 'content',
  ) => {
    let data: any;
    //如果是选择数字和文本的改变
    if (type === 'type') {
      data = e;
      const temp = JSON.parse(JSON.stringify(optionRef.current));
      temp[item.seq - 1].answerType = data;
      if (data === 2 && !temp[item.seq - 1]?.answerRange) {
        temp[item.seq - 1].answerRange = 1;
      }
      updateOptions(temp);
      if (data === 2) {
        handleDelEditor(`answers_${item.uid}`);
      }
    }
    //
    if (type === 'numberType') {
      data = e;
      const temp = JSON.parse(JSON.stringify(optionRef.current));
      temp[item.seq - 1].answerRange = data;
      updateOptions(temp);
    }

    if (type === 'value' && !!key) {
      data = e.target?.value;
      const temp = JSON.parse(JSON.stringify(optionRef.current));
      temp[item.seq - 1][key] = data;

      updateAnswer(
        temp.map((item: any) => {
          if (item?.answerType === 2 && item?.answerRange === 2) {
            return `${item?.answerMin}~${item?.answerMax}`;
          }
          return item.content;
        }),
      );
      updateOptions(temp);
    }
  };

  // 答案更新
  const updateAnswer = (value: any) => {
    setAnswer(value);
    handleUpdate('questions_answers', value);
  };
  //
  const blinkInputChange = (
    e: any,
    uid: string,
    editorName?: string,
    isBlank = false,
  ) => {
    const data = e.level?.content;
    const realData = fetchEditorContent(editorName) ? data : '';

    const temp = JSON.parse(JSON.stringify(optionRef.current));
    const itemIndex = temp.findIndex((cell: any) => cell.uid === uid);
    if (itemIndex != -1) {
      temp[itemIndex].content = realData;
    }
    if (isBlank) {
      updateAnswer(temp.map((cell: any) => cell.content));
    }
    updateOptions(temp);
  };

  const deleteOption = (item: any) => {
    const temp = JSON.parse(JSON.stringify(optionRef.current));
    temp.splice(item.seq - 1, 1);
    for (let i = item.seq - 1; i < temp.length; i++) {
      temp[i].seq--;
    }
    const last = temp[temp.length - 1];
    //如果删除的答案已经不在剩余选项里了就重置答案
    if (String.fromCharCode(64 + Number(last?.seq)) <= answer) {
      updateAnswer(undefined);
    }
    updateOptions(temp);
  };

  useEffect(() => {
    if (editType === 'edit') {
      setAnswer(detail?.questions_answers);
      setOptions(detail?.questions_options || []);
      optionRef.current = detail?.questions_options || [];
      setFileList(formatFileList(detail?.fileList || []));
      return;
    }
    initOptions(2, detail, updateOptions);
  }, []);

  useEffect(() => {
    if (detail?.isSort) {
      setAnswer(detail?.questions_answers?.[0]);
      setOptions(detail?.questions_options || []);
      optionRef.current = detail?.questions_options || [];
      setFileList(formatFileList(detail?.fileList || []));
      handleUpdate('isSort', false);
    }
  }, [detail?.isSort])

  return (
    <div className="blank_container">
      <div className="form_item">
        <div className="form_item_header">
          <img
            className="unfold_icon"
            src={isShowQuestion ? Unfold : Retract}
            onClick={() => setIsShowQuestion(!isShowQuestion)}
          />
          <span className="tag"></span>
          <span>小题{itemIndex + 1}</span>
        </div>
        <div className="form_item_body">
          <Editor
            name={blankId.current}
            value={initData.current?.questions_content}
            onChange={(e: any) => topicChange(e, blankId.current)}
            addBlanks={addBlanks}
            addFile={() => {
              uploadRef.current?.click();
            }}
            // addCase={
            //   parameterConfig.target_customer === 'tcm'
            //     ? () => setCaseVisible(true)
            //     : undefined
            // }
            textSetting={{
              max: 5000,
              spaces: true,
              toast: function () {
                message.info(`题目输入不能超过${(this as any).max}个字`);
              },
            }}
          />
          <Button
            title="删除此选项"
            type="link"
            icon={<IconFont type="iconweiwancheng1" />}
            className="del_item"
            onClick={() => onDeleteTopic(initData.current.id)}
          ></Button>
        </div>
        <div className={`enclosure${fileList.length > 0 ? '' : ' hidden'}`}>
          <span className="label">附件列表:</span>
          <Upload {...uploadProps}>
            <Button ref={uploadRef} icon={<PaperClipOutlined />} type="ghost">
              添加附件
            </Button>
          </Upload>
        </div>
      </div>

      {isShowQuestion ? (
        <>
          <div className="form_item">
            <div className="form_item_header">
              <span className="tag"></span>
              <span>答案</span>
            </div>
            <div className="form_item_body">
              <div className="answer_container">
                {options.map((item: any, index: number) => {
                  return (
                    <div
                      key={item.uid}
                      style={{ position: 'relative' }}
                      className="answer_list"
                    >
                      <span style={{ width: '55px' }}>{`第${item.seq}空`}</span>
                      <Select
                        defaultValue={item?.answerType === 2 ? '数字' : '文本'}
                        style={{ width: 120, marginRight: '10px' }}
                        onChange={(value) =>
                          handleChangeBlank(Number(value), item, 'type')
                        }
                        options={selectOptions}
                      />
                      {item?.answerType === 2 ? (
                        <Select
                          defaultValue={
                            item?.answerRange === 2 ? '范围' : '唯一答案'
                          }
                          style={{ width: 120 }}
                          onChange={(value) =>
                            handleChangeBlank(Number(value), item, 'numberType')
                          }
                          options={selectNumberType}
                        />
                      ) : (
                        <Editor
                          name={`answers_${item.uid}`}
                          height={120}
                          onChange={(e: any) =>
                            blinkInputChange(
                              e,
                              item.uid,
                              `answers_${item.uid}`,
                              true,
                            )
                          }
                          disabledImage
                          value={item.content}
                          textSetting={{
                            max: 500,
                            spaces: true,
                            toast: function () {
                              message.info(
                                `选项输入不能超过${(this as any).max}个字`,
                              );
                            },
                          }}
                        />
                      )}
                      {item?.answerType === 2 &&
                        (item?.answerRange === 2 ? (
                          <>
                            <Input
                              type="number"
                              defaultValue={item?.answerMin}
                              onChange={(e) =>
                                handleChangeBlank(e, item, 'value', 'answerMin')
                              }
                            />
                            ~
                            <Input
                              type="number"
                              defaultValue={item?.answerMax}
                              onChange={(e) =>
                                handleChangeBlank(e, item, 'value', 'answerMax')
                              }
                            />
                            (答案包含最小值和最大值)
                          </>
                        ) : (
                          <Input
                            defaultValue={item.content}
                            type="number"
                            onChange={(e) =>
                              handleChangeBlank(e, item, 'value', 'content')
                            }
                          />
                        ))}
                      <Button
                        title="删除此选项"
                        type="link"
                        icon={<IconFont type="iconweiwancheng1" />}
                        onClick={() => deleteOption(item)}
                        style={{ position: 'absolute', right: '-32px' }}
                      ></Button>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <div className="form_item">
            <div className="form_item_header">
              <span className="tag"></span>
              <span>解析</span>
            </div>
            <div className="form_item_body">
              <Editor
                name={analysisId.current}
                value={initData.current?.questions_analysis}
                height={115}
                onChange={(e: any) => topicChange(e, analysisId.current)}
                textSetting={{
                  max: 5000,
                  spaces: true,
                  toast: function () {
                    message.info(`解析输入不能超过${(this as any).max}个字`);
                  },
                }}
              />
            </div>
          </div>
        </>
      ) : (
        <div />
      )}
    </div>
  );
};

export default BlankQuestion;
