import http from '@/http/http';
import { IFormItem } from '@/types/entityTypes';

namespace uploadApis {
  export const getALLFields = (source: string = 'webupload') => {
    return http(`/old/upload/get-all-fields-by-source?source=${source}`);
  };
  export const getuploadFormat = (type?: string) => {
    return http(`/upload/format`, {
      method: 'GET',
      params: {
        type,
      },
    });
  };
  export const uploadImport = (
    folderId: string,
    filePath: string,
    uploadMetas: IFormItem[],
  ) => {
    return http<boolean>('/upload/import', {
      method: 'POST',
      data: {
        folderId,
        filePath,
        uploadMetas,
      },
    });
  };
  export const filemerge = (guid: string, fileName: string) => {
    return http<string>('/upload/filemerge', {
      method: 'POST',
      data: {
        guid,
        fileName,
      },
    });
  };
  /**
   * 查询合并进度
   * @param fileGuid
   */
  export const fetchMergeStatus = (fileGuid: string) =>
    http<{
      state: number;
      errorMsg: string;
      finalFilePath: string;
    }>(`/upload/get/composite/task/details/${fileGuid}`);
  /**
   * 删除合成任务
   * @param fileGuid
   */
  export const deleteMergeTask = (fileGuid: string) =>
    http(`/upload/delete/composite/task/${fileGuid}`, {
      method: 'DELETE',
    });
  /**
   * 取消文件任务
   * @param guid
   */
  export const cancelFileTask = (guid: string) => {
    return http(`/upload/delete/fragment/file`, {
      method: 'POST',
      params: {
        guid,
      },
    });
  };
}

export default uploadApis;
