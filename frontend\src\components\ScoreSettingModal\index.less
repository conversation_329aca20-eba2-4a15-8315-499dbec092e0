.point_modal {
  width: 100%;

  // .ant-input,
  // .ant-select {
  //   width: 190px !important;
  // }

  .auto-img {
    overflow: hidden;

    img {
      max-width: 100%;
      height: auto;
      vertical-align: bottom;
      transform: scale(1);
      transform-origin: center;
    }
  }

  .inputs {
    width: 90px !important;
    height: 28px !important;
    // margin-left: px;
    background: #FFFFFF;
    border-radius: 1px;
    border: 1px solid #CBCBCB;
  }

  .ant-modal-body {
    max-height: 60vh;
    overflow: auto;

    .header {
      width: 100%;
      height: 40px;
      background: #F7F8FA;
      display: flex;
      align-items: center;

      >span:nth-child(1) {
        display: inline-block;
        width: 34px;
      }

      >span:nth-child(2) {
        margin-left: 106px;
      }

      >span:nth-child(3) {
        margin-left: 428px;
        width: 60px;
        text-align: center;
      }
    }

    .content_row {
      height: 60px;
      background: #FFFFFF;
      display: flex;
      align-items: center;

      >span:nth-child(1) {
        margin-left: 20px;
        color: var(--primary-color);
      }

      >span:nth-child(2) {
        margin-left: 106px;
      }

      .ant-input {
        width: 90px !important;
        height: 28px !important;
        margin-left: 470px;
        background: #FFFFFF;
        border-radius: 1px;
        border: 1px solid #CBCBCB;
      }
    }

    .ant-table-wrapper {
      padding-left: 124px;

      .ant-table {
        .ant-table-tbody {

          .ant-table-cell {
            p {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}
