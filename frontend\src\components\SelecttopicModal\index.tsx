import examManageApis from '@/api/exam';
import examType from '@/types/examType';
import { Button, Input, message, Modal, Select, Table } from 'antd';
import React, { useEffect, useState } from 'react';
import RenderHtml from '../renderHtml';
import "./index.less";
import { useLocation } from 'umi'
import Contract from '@/api/Contract';
import paperManageApis from '@/api/Contract';
interface params {
    title: any,
    selectkeys?: any,
    type?: string,// 单选还是多选
    visible: boolean,
    userData: any,  // 传递过来的表单数据
    Switchitem: any,//替换的项
    Replace: any,//状态 状态是替换题目 或者 组成部分
    classifyID: string,
    userID: string,
    itemName: string,
    updateData: (newUserData: any) => void;  // 更新数据的回调
    callback: (data: any) => void,
    onclose: () => void,
}

const TopicModal: React.FC<params> = (params) => {
    const { title, selectkeys, type, itemName, visible, Replace, userID, callback, onclose, userData, updateData, Switchitem, classifyID } = params
    const [query, setQuery] = useState<any>({
        questions_content: undefined,
        questions_type: undefined,
        page: 1,
        size: 10
    })
    const [data, setData] = useState<any>([]);
    const [total, setTotal] = useState<any>([]);
    const [selectRows, setSelectedRows] = useState<any>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
    const type_enum = examType.optionType_; //题目类型

    // 应用分类|考核方向
    const [initClassify, setInitClassify] = useState<any>([]);
    const [examDirection, setExamDirection] = useState<any>([]);
    const [initQuestionSource, setInitQuestionSource] = useState<any>([]);
    const [result, setresult] = useState<any>([]);
    const [selectedPart, setSelectedPart] = useState<string | undefined>(); // 初始值为第一个部分


    useEffect(() => {
        fetchClassify();
        fetchQuestionSource();
    }, [])

    useEffect(() => {
        if (itemName) {
            setTimeout(() => {
                setQuery({
                    ...query,
                    assessmentCode: userID
                })
            });
        }
    }, [itemName])

    useEffect(() => {
        fetchDataList()
    }, [query])


    useEffect(() => {
        if (!userData?.part || userData?.part.length === 0) {
            console.log(11);
        } else {
            const uniqueQuestionIds = userData?.part.reduce((acc: any, part: { questionIds: any }) => {

                const questionIds = Array.isArray(part.questionIds) ? part.questionIds : [];
                return new Set([...acc, ...questionIds]);
            }, []);
            const result = [...uniqueQuestionIds];
            setresult(result);
        }

    }, [userData?.part])

    useEffect(() => {
        if (Replace == '替换题目') {

            const uniqueQuestionIds = userData?.part.reduce((acc: any, part: { questionIds: any; }) => {
                return new Set([...acc, ...part.questionIds]);
            }, []);
            const result = [...uniqueQuestionIds];
            setresult(result)
            setSelectedPart(Switchitem.name)
            var sting = Switchitem.questionId
            setSelectedRowKeys(result)
        } else {
            setSelectedPart(Replace)
            if (Switchitem?.questionIds == undefined) {

            } else {
                if (Switchitem?.questionIds.length != 0) {
                    setSelectedRowKeys(Switchitem?.questionIds)
                }
            }

        }
    }, [Switchitem, Replace])

    const fetchClassify = async () => {
        const res = await paperManageApis.classification({ page: 1, size: 100 });
        if (res.status === 200) {
            setInitClassify(res.data?.data || []);
            setExamDirection(res.data?.data?.[0]?.children || []);
        }
    };

    // 题目来源

    const fetchQuestionSource = async () => {
        const res = await examManageApis.questionSource();
        if (res.status === 200) {
            setInitQuestionSource(res.data);
        }
    };

    const nameChange = (e: any) => {
        setQuery({
            ...query,
            page: 1,
            size: 10,
            questions_content: e.target.value
        })
    }

    const labelNameChange = (e: any) => {
        setQuery({
            ...query,
            page: 1,
            size: 10,
            labelName: e.target.value
        })
    }
    const knowledge_nameChange = (e: any) => {

        setQuery({
            ...query,
            page: 1,
            size: 10,
            knowledge_name: e.target.value
        })
    }

    const typeChange = (e: any) => {

        setQuery({
            ...query,
            page: 1,
            size: 10,
            questions_type: e
        })
    }

    const questionChange = (e: any) => {

        setQuery({
            page: 1,
            size: 10,
            ...query,
            questionSourceCode: e
        })
    }
    const applicationChange = (e: any) => {

        setQuery({
            ...query,
            page: 1,
            size: 10,
            applicationClassCode: e
        })
    }
    const assessmentCodeChange = (e: any) => {

        setQuery({
            ...query,
            page: 1,
            size: 10,
            assessmentCode: e
        })
    }

    const assessmentCodeChanges = (e: any) => {

        setQuery({
            ...query,
            page: 1,
            size: 10,
            assessmentCode: e
        })
    }


    const fetchDataList = async () => {
        const res = await examManageApis.fetchTopicLists(query);
        if (res.status === 200) {
            setData(res.data?.data)
            setTotal(res.data?.totalCount)
        }
    };




    const columns: any = [
        Table.SELECTION_COLUMN,
        Table.EXPAND_COLUMN,
        {
            title: '题目类型',
            width: '13%',
            dataIndex: 'questions_type',
            key: 'questions_type',
            ellipsis: true,
            render: (item: any, record: any) => (
                <div>{examType.optionType_[Number(item)]}</div>
            )
        },
        {
            title: '难度',
            width: '10%',
            dataIndex: 'questions_difficulty',
            key: 'questions_difficulty',
            ellipsis: true
        },
        {
            title: '题目内容',
            width: '60%',
            dataIndex: 'questions_content',
            key: 'questions_content',
            ellipsis: true,
            render: (value: any) => {
                const extractedText = removeMathML(value);
                return <RenderHtml cname="auto-img" value={extractedText} />;
            },
        },
        {
            title: '答案',
            width: '10%',
            dataIndex: 'questions_answers',
            key: 'questions_answers',
            ellipsis: true,
            render: (value: any) =>
                value?.map((item: any, index: any) => (
                    <RenderHtml key={index} cname="auto-img" value={item}></RenderHtml>
                )),
        },
        {
            title: '创建人',
            width: '30%',
            dataIndex: 'add_username',
            key: 'add_username',
            ellipsis: true
        },
    ];

    const expandedRowRender = (expandList: any) => {
        const columns: any = [
            {
                title: '题目类型',
                width: '10%',
                dataIndex: 'questions_type',
                key: 'questions_type',
                ellipsis: true,
                render: (item: any, record: any) => (
                    <div>{examType.optionType_[Number(item)]}</div>
                ),
            },
            {
                title: '难度',
                width: '10%',
                dataIndex: 'questions_difficulty',
                key: 'questions_difficulty',
                ellipsis: true,
                render: ''
            },
            {
                title: '题目',
                width: '60%',
                dataIndex: 'questions_content',
                key: 'questions_content',
                ellipsis: true,
                render: (value: any) => (
                    <RenderHtml cname="auto-img" value={value}></RenderHtml>
                ),
            },
            {
                title: '答案',
                width: '10%',
                dataIndex: 'questions_answers',
                key: 'questions_answers',
                ellipsis: true,
                render: (value: any) =>
                    value?.map((item: any) => (
                        <RenderHtml cname="auto-img" value={item}></RenderHtml>
                    )),
            },
            {
                title: '创建人',
                width: '30%',
                dataIndex: 'add_username',
                key: 'add_username',
                ellipsis: true
            },

        ];

        return <Table sticky={false} style={{ paddingLeft: 31 }} columns={columns} showHeader={false} dataSource={expandList} pagination={false} />;
    };


    const rowSelection = {
        type: type,
        getCheckboxProps: (record: any) => ({
            disabled: result.includes(record.id),
        }),
        onChange: (newSelectedRowKeys: Array<any>, newSelectedRows: any) => {
            setSelectedRowKeys(newSelectedRowKeys.filter(Boolean));
            setSelectedRows(newSelectedRows.filter(Boolean));
        },

        preserveSelectedRowKeys: true,
        selectedRowKeys,
    };



    const typeChangeti = (value: string) => {
        setSelectedPart(value); // 设置为选中的值
        if (Replace != '替换题目') {
            userData?.part?.forEach((item: { name: string; questionIds: string | any[]; }) => {
                if (item.name === value) {
                    if (item?.questionIds != undefined && item?.questionIds.length > 0) {
                        setSelectedRowKeys(item?.questionIds)
                    } else {
                        setSelectedRowKeys([])
                    }
                }
            });
        }
    };

    const testSave = async (data: any) => {
        const res = await Contract.testSave(data);
        if (res.status === 200) {
            localStorage.setItem("userData", JSON.stringify(res.data));  // 将对象转化为字符串
            updateData(res.data);
            callback(selectRows);
            setTimeout(() => {
                setSelectedRowKeys([])
            });
        } else {
            message.success(res.message);
        }
    };

    const exchangeQuestionAPI = async (data: any) => {
        const res = await Contract.exchangeQuestion(data);
        if (res.status === 200) {
            getVoByIdAPI(userData.id)
        }
    };

    const getVoByIdAPI = async (id: any) => {
        const res = await Contract.getVoById(id);
        if (res.status === 200) {
            // updateData(res.data);
            // localStorage.setItem("userData", JSON.stringify(res.data));
            // updateData(res.data);
            // callback(selectRows);
            localStorage.setItem("userData", JSON.stringify(res.data));  // 将对象转化为字符串
            updateData(res.data);
            callback(selectRows);
            setTimeout(() => {
                setSelectedRowKeys([])
            });
            // testSave(res.data)
        }
    };

    const confirm = () => {

        if (selectedPart) {
            
            if (Replace === '替换题目') {
                var data = {
                    "oldId": Switchitem.id,
                    "newId": selectedRowKeys[selectedRowKeys.length - 1]
                }
                exchangeQuestionAPI(data);
            } else {
                callback(selectRows);
                const updatedPart = userData?.part?.map(item => {
                    if (item.name == selectedPart) {
                        item.questionIds = item.questionIds || [];
                        // 添加 selectedRowKeys 到该项的 questionIds
                        item.questionIds.push(...selectedRowKeys);
                    }
                    return item;
                });

                const updatedUserData = {
                    ...userData, // 保留原有数据
                    part: updatedPart // 更新 part 部分
                };

                testSave(updatedUserData)
            }
        } else {
            message.success('选择将题目加入的部分');
        }

    }
    const reset = () => {
        if (itemName) {
            setQuery({
                // ...query,
                questions_content: '',
                page: 1,
                size: 10,
                applicationClassCode: '',
                knowledge_name: '',
                labelName: '',
                questionSourceCode: '',
                questions_type: undefined
            })
        } else {
            // setExamDirection('')
            setQuery({
                // ...query,
                page: 1,
                size: 10,
                questions_content: '',
                assessmentCode: '',
                applicationClassCode: '',
                questionSourceCode: '',
                knowledge_name: '',
                labelName: '',
                questions_type: undefined
            })
        }
    }
    const close = () => {
        setSelectedRowKeys(selectkeys.map((item: any) => item.parent_id ? item.parent_id : item.id));
        onclose();
    }


    const removeMathML = (html: string): string => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        // 移除数学公式相关的元素
        const mathElements = doc.querySelectorAll('.math-tex, mjx-container');
        mathElements.forEach((el) => el.remove());
        return doc.body.textContent || '';  // 获取去除数学公式后的纯文本内容
    };

    return (
        <Modal
            title={title}
            visible={visible}
            onCancel={close}
            width={944}
            className='topic_modal'
            footer={[
                <Button
                    type='primary'
                    onClick={confirm}
                    disabled={selectRows.length === 0}
                >
                    确定
                </Button>,
                <Button
                    onClick={close}
                >
                    取消
                </Button>,
            ]}
        >
            <div className='searchbox' style={{ marginBottom: ' 10px' }} >
                {!itemName && (
                    <Select disabled={Replace === '替换题目'} placeholder="选择将题目加入的部分"
                        onChange={typeChangeti}
                        value={selectedPart}   >
                        {
                            userData?.part?.map((item: any, index: number) => (
                                <Select.Option
                                    value={item.name}
                                    key={index}
                                >
                                    {item.name}
                                </Select.Option>
                            ))
                        }
                    </Select>
                )}

                {!itemName && (
                    <Select
                        placeholder="题目来源"
                        value={query.questionSourceCode || null}
                        onChange={questionChange}
                        options={initQuestionSource.map((item: any) => {
                            return {
                                value: item.questionSourceCode,
                                label: item.questionSourceName
                            }
                        })} />
                )}

                {itemName && (
                    <Select
                        disabled={!!itemName}
                        placeholder="考核方向"
                        onChange={assessmentCodeChange}
                        value={examDirection.find((item: any) => item.name === itemName)?.id}
                        options={examDirection.map((item: any) => {
                            return {
                                value: item.id,
                                label: item.name
                            }
                        })} />
                )}
                {!itemName && (
                    <Select
                        placeholder="考核方向"
                        onChange={assessmentCodeChanges}
                        value={query.assessmentCode || null}
                        options={examDirection.map((item: any) => {
                            return {
                                value: item.id,
                                label: item.name
                            }
                        })} />
                )}

                {!itemName && (
                    <Select placeholder="应用分类"
                        onChange={applicationChange}
                        value={query.applicationClassCode || null}
                        options={initClassify.map((item: any) => {
                            return {
                                value: item.id,
                                label: item.name,
                                children: item.children || []
                            }
                        })} />
                )}


                <Select placeholder='题目类型' onChange={typeChange} value={query.questions_type}>
                    {
                        type_enum.map((item: any, index: number) => (
                            <Select.Option
                                value={index}
                                // key={item + index}
                                key={index}
                            >
                                {item}
                            </Select.Option>
                        ))
                    }
                </Select>

            </div>
            <div className='searchbox'>

                <Input placeholder='请输入名称' onChange={nameChange} value={query.questions_content} />
                <Input placeholder='请输入知识点' onChange={labelNameChange} value={query.labelName} />
                <Input placeholder='请输入标签' onChange={knowledge_nameChange} value={query.knowledge_name} />
                <Button
                    type='primary'
                    onClick={reset}
                >
                    重置
                </Button>
            </div>
            <div>
                <Table
                    dataSource={data}
                    rowKey={"id"}
                    columns={columns}
                    // key={selectedRowKeys}
                    rowSelection={rowSelection as any}
                    pagination={{
                        position: ['bottomCenter'],
                        showSizeChanger: true,
                        total: total,
                        showQuickJumper: true,
                        current: query.page, // 绑定当前页
                        pageSize: query.size, // 绑定每页条数
                        onChange: (page: number, size: any) =>
                            setQuery({
                                ...query,
                                page,
                                size
                            }),
                        showTotal: total => `共 ${total} 条`,
                        size: 'small'
                    }}
                    expandable={{
                        expandedRowRender: record => expandedRowRender(record?.groupQuestions || []),
                        rowExpandable: record => record.questions_type == 5,
                        expandedRowClassName: () => 'group_expand'
                    }}
                    scroll={{ y: '420px', x: 1000 }}
                />
            </div>
        </Modal >
    )
}

export default TopicModal