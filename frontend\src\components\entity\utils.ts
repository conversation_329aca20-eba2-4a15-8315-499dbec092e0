import CryptoJS from "crypto-js";

export const getDocumentType = (fileExtension: string) => {
  const w = ["doc", "docm", "docx", "docxf", "dot", "dotm", "dotx", "epub", "fodt", "fb2", "htm", "html", "mht", "odt", "oform", "ott", "oxps", "pdf", "rtf", "txt", "djvu", "xml", "xps"];
  const c = ["csv", "fods", "ods", "ots", "xls", "xlsb", "xlsm", "xlsx", "xlt", "xltm", "xltx"];
  const s = ["fodp", "odp", "otp", "pot", "potm", "potx", "pps", "ppsm", "ppsx", "ppt", "pptm", "pptx"];
  let documentType = "";
  if (w.indexOf(fileExtension) > -1) {
    documentType = "word";
  }
  else if (c.indexOf(fileExtension) > -1) {
    documentType = "cell";
  }
  else if (s.indexOf(fileExtension) > -1) {
    documentType = "slide";
  }
  return documentType;
};

export const getFileTypeByPath = (path: string) => {
  const index = path.lastIndexOf("."); // lastIndexOf("/")  找到最后一个  /  的位置
  const fileType = path.substring(index + 1); // substr() 截取剩余的字符，即文件名doc
  // 删除文件名中的?后面的内容
  const index2 = fileType.indexOf("?");
  if (index2 > -1) {
    return fileType.substring(0, index2);
  }
  return fileType;
};

export const getFileNameByPath = (path: string) => {
  const index = path.lastIndexOf("/"); // lastIndexOf("/")  找到最后一个  /  的位置
  const fileName = path.substring(index + 1); // substr() 截取剩余的字符，即得文件名xxx.doc
  // 删除文件名中的?后面的内容
  const index2 = fileName.indexOf("?");
  if (index2 > -1) {
    return fileName.substring(0, index2);
  }
  return fileName;
};

export const getPathKey = (path: string) => {
  const fileName = getFileNameByPath(path);
  const dir = path.replace("/" + fileName, "");
  const index = dir.lastIndexOf("/"); // lastIndexOf("/")  找到最后一个  /  的位置
  const key = dir.substr(index + 1);
  return key;
};
export const base64ToUrlData = (base64data: string) => {
  const base64UrlData = base64data.replace(/\+/g, '-').replace(/\//g, '_').replace(/\=/g, '');
  return base64UrlData;
};

export const getBase64UrlData = (data: any) => {
  let dataStr = "";
  if (typeof (data) == 'string') {
    dataStr = data;
  } else {
    dataStr = JSON.stringify(data);
  }
  dataStr = CryptoJS.enc.Utf8.parse(dataStr);
  let base64UrlData = base64ToUrlData(CryptoJS.enc.Base64.stringify(dataStr));
  return base64UrlData;
};
export const timestamp = () => { let date = new Date(); date.setMinutes(date.getMinutes() + 5); let outcome = Math.round(date.getTime() / 1000).toString(); return outcome; };

export const jwtSign = function (payload: any, secretKey: string) {
  // key = CryptoJS.enc.Utf8.parse(key);
  let header = '{"alg":"HS256","typ":"JWT","exp":' + timestamp() + '}';
  let headerBase64 = getBase64UrlData(header);
  let payloadBase64 = getBase64UrlData(payload);
  let base64Token = headerBase64 + '.' + payloadBase64;
  var base64Signature = base64ToUrlData(CryptoJS.HmacSHA256(base64Token, secretKey).toString(CryptoJS.enc.Base64));
  let jwt = base64Token + '.' + base64Signature;
  return jwt;
};
