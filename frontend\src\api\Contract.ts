import HTTP from './index';

/**
 * @msg: 分类管理  测试管理
 * @param {any} data
 * @return {*}
 */
namespace paperManageApis {
    //分类管理---分页 测试管理的侧边栏
    export const classification = (data: any) => {
        const { page, size } = data;
        return HTTP(`/exam-api/classification/page?page=${page}&size=${size}`, {
            method: 'GET',
        });
    };

    // 删除
    export const classificationDelete = (data: any) => {
        return HTTP(`/exam-api/classification/delete`, {
            method: 'POST',
            data
        });
    };

    // 新增
    export const classificationInsert = (data: any) => {
        return HTTP(`/exam-api/classification/insert`, {
            method: 'POST',
            data
        });
    };

    // 更新 编辑 
    export const classificationUpdate = (data: any) => {
        return HTTP(`/exam-api/classification/update`, {
            method: 'POST',
            data
        });
    };
    //测验管理

    /**
 * @msg:  测试管理
 * @param {any} data
 * @return {*}
 */

    export const testcation = (data: any) => {
        const { page, size, classifyId, name, readState, testState, createId } = data;

        // 构建查询参数字符串
        const queryParams = new URLSearchParams({
            page: String(page),
            size: String(size),
            classifyId: classifyId || "",
            name: name || "",
            readState: readState || "",
            testState: testState || "",
            createId: createId || "",
        });
        return HTTP(`/exam-api/test/page?${queryParams.toString()}`, {
            method: 'GET',
        });
    };

    // 创建人下拉
    export const addusercodeList = () => {
        return HTTP(`/exam-api/DropDownBox/addusercode/list`, {
            method: 'GET',
        });
    };


    // 复制
    export const testCopy = (data: any) => {
        return HTTP(`/exam-api/test/copy`, {
            method: 'POST',
            data
        });
    };

    // 删除
    export const testDelete = (data: any) => {
        return HTTP(`/exam-api/test/delete`, {
            method: 'POST',
            data
        });
    };

    // 保存
    export const testSave = (data: any) => {
        return HTTP(`/exam-api/test/save`, {
            method: 'POST',
            data
        });
    };

    // 添加测试数据
    export const addTestData = () => {
        return HTTP(`/exam-api/test/addTestData`, {
            method: 'GET',
        });
    };

    //详情
    export const getVoById = (id: any) => {
        return HTTP(`/exam-api/test/getVoById/${id}`, {
            method: 'GET',
        });
    };

    //发布

    export const issue = (id: any) => {
        return HTTP(`/exam-api/test/issue/${id}`, {
            method: 'POST',
        });
    };

    // 取消
    export const cancel = (id: any) => {
        return HTTP(`/exam-api/test/issue/cancel/${id}`, {
            method: 'POST',
        });
    };



    //删除题目 data
    export const deleteQuestion = (data: any) => {
        return HTTP(`/exam-api/test/deleteQuestion/${data}`, {
            method: 'POST',
        });
    };


    export const deletePart = (data: any) => {
        return HTTP(`/exam-api/test/deletePart/${data}`, {
            method: 'POST',
        });
    };

    export const exchangeQuestion = (data: any) => {
        return HTTP(`/exam-api/test/exchangeQuestion`, {
            method: 'POST',
            data
        });
    };

    //统计
    export const getCountVo = (id: any) => {
        return HTTP(`/exam-api/test/getCountVo/${id}`, {
            method: 'GET',
        });
    };

    //考试情况
    export const getAnswerCountVo = (data: any) => {
        const { page, size, testId, nameCode, } = data;

        // 构建查询参数字符串
        const queryParams = new URLSearchParams({
            page: String(page),
            size: String(size),
            testId: testId || " ",
            nameCode: nameCode || " ",
        });
        return HTTP(`/exam-api/test/getAnswerCountVo?${queryParams.toString()}`, {
            method: 'GET',
        });
    };

    // 统计详情
    export const questionPage = (data: any) => {
        const { page, size, testId, } = data;

        // 构建查询参数字符串
        const queryParams = new URLSearchParams({
            page: String(page),
            size: String(size),
            answerId: testId || " ",
        });
        return HTTP(`/exam-api/test/questionPage?${queryParams.toString()}`, {
            method: 'GET',
        });
    };

    // 考试详情页面 /exam-api/testAnswerQuestion/listVoByAnswerId
    export const listVoByAnswerId = (id: any) => {
        return HTTP(`/exam-api/testAnswerQuestion/listVoByAnswerId/${id}`, {
            method: 'GET',
        });
    };

    //考试记录
    export const page = (data: any) => {
        const { page, size, Id, nameCodeStr, } = data;

        const queryParams = new URLSearchParams({
            page: String(page),
            size: String(size),
            testId: Id || " ",
            nameCodeStr: nameCodeStr || " ",
        });
        return HTTP(`/exam-api/answer/page?${queryParams.toString()}`, {
            method: 'GET',
        });
    };

    export const getSubmitVo = (id: any) => {
        return HTTP(`/exam-api/test/getSubmitVo/${id}`, {
            method: 'GET',
        });
    };

    //获取
    export const criticismlist = (id: any) => {
        return HTTP(`/exam-api/criticism/list/${id}`, {
            method: 'GET',
        });
    };

    //获取所有记录
    export const listByAnswer = (id: any) => {
        return HTTP(`/exam-api/criticism/listByAnswer/${id}`, {
            method: 'GET',
        });
    };

    // 添加  批阅记录
    export const insert = (data: any) => {
        return HTTP(`/exam-api/criticism/insert`, {
            method: 'POST',
            data
        });
    };

    // 打分
    export const testAnswerQuestionscore = (data: any) => {
        return HTTP(`/exam-api/testAnswerQuestion/score`, {
            method: 'POST',
            data
        });
    };

    // 删除
    export const criticismdelete = (id: any) => {
        return HTTP(`/exam-api/criticism/delete/${id}`, {
            method: 'POST',
        });
    };
    
    // 考试 保存
    export const preserve = (id: any) => {
        return HTTP(`/exam-api/answer/preserve/${id}`, {
            method: 'POST',
        });
    };

    // 收藏
    export const collect = (data: any) => {
        return HTTP(`/exam-api/collectQuestion/collect`, {
            method: 'POST',
            data,
        });
    };

    // 收藏
    export const collectQuestionpage = (data: any) => {
        const { page, size, questions_content, startDate, endDate, classifyId } = data;
        const queryParams = new URLSearchParams({
            page: String(page),
            size: String(size),
            classifyId: classifyId,
            questions_content: questions_content || "",
            startDate: startDate || "",
            endDate: endDate || "",
        });
        return HTTP(`/exam-api/collectQuestion/page?${queryParams.toString()}`, {
            method: 'GET',
        });
    };

    export const collectQuestioncollect = (data: any) => {
        return HTTP(`/exam-api/collectQuestion/collect/${data}`, {
            method: 'POST',
        });
    };

    export const collectQuestion = (data: any) => {
        return HTTP(`/exam-api/collectQuestion/cancel`, {
            method: 'POST',
            data
        });
    };

    //  
    export const groupQuestionPage = (data: any) => {
        const { page, size, questions_content, labels, knowledge_points, partIds, startDate, endDate, testId } = data;
        // 构建查询参数字符串
        const queryParams = new URLSearchParams({
            page: String(page),
            size: String(size),
            testId: testId,
            questions_content: questions_content || "",
            knowledge_points: knowledge_points || "",
            labels: labels || "",
            partIds: partIds || [],
        });
        return HTTP(`/exam-api/test/groupQuestionPage?${queryParams.toString()}`, {
            method: 'GET',
        });
    };


    export const updateGroupQuestionScoreConfig = (data: any) => { // 题组得分设置接口
        return HTTP(`/exam-api/test/updateGroupQuestionScoreConfig`, {
            method: 'POST',
            data
        });
    };

    export const checkRead = (id: any) => {  // 校验阅卷权限
        return HTTP(`/exam-api/test/checkRead/${id}`, {
            method: 'GET',
        });
    };

    export const checkexcel = (data: any) => {  // 导入学生
        return HTTP(`/exam-api/test/import/excel`, {
            method: 'POST',
            data
        });
    };

    export const updatExportConfig = (data: any) => { // md下载
        return HTTP(`/exam-api/examination/list/export`, {
            method: 'POST',
            data
        });
    };

}


export default paperManageApis