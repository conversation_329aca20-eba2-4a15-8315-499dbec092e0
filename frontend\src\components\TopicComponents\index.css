.topic_components {
  width: 100%;
  display: flex;
}
.topic_components .components_right {
  width: 80%;
  background: #FFFFFF;
  border-radius: 6px;
  padding: 10px;
}
.topic_components .components_right .rightoverflow {
  height: calc(100vh - 280px);
  overflow-y: auto;
}
.topic_components .components_right .right_headerss {
  padding: 15px 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.topic_components .components_right .right_headerss .opt_btns {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.topic_components .components_right .right_headerss .opt_btns button.ant-btn.ant-btn-text:not(:last-child) {
  margin: 0;
}
.topic_components .components_right .right_headerss .opt_btns .disabled {
  color: rgba(0, 0, 0, 0.25);
}
.topic_components .components_right .right_headerss .opt_btns .item_ {
  padding: 4px 15px;
  position: relative;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.topic_components .components_right .right_headerss .opt_btns .item_:hover {
  color: var(--primary-color);
}
.topic_components .components_right .right_headerss .opt_btns .item_:hover .ant-btn {
  color: var(--primary-color);
}
.topic_components .components_right .right_headerss .opt_btns .item_.disabled {
  cursor: no-drop;
  color: rgba(0, 0, 0, 0.25) !important;
}
.topic_components .components_right .right_headerss .opt_btns .item_.disabled .ant-btn {
  color: rgba(0, 0, 0, 0.25) !important;
}
.topic_components .components_right .right_headerss .opt_btns .item_ > span:last-child {
  margin-left: 8px;
}
.topic_components .components_right .right_headerss .opt_btns .item_:not(:last-child)::after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 16px;
  background: #d8d8d8;
  right: 0;
  top: 8px;
  position: absolute;
}
.topic_components .components_right .right_headerss .opt_btns .ant-btn {
  margin-right: 14px;
  display: flex;
  justify-items: center;
  align-items: center;
  margin: 0;
}
.topic_components .components_right .right_headerss .opt_btns .ant-btn span.anticon {
  font-size: 16px;
}
.topic_components .components_right .right_headerss .opt_btns .ant-btn.ant-btn-primary {
  border-radius: 16px;
}
.topic_components .components_right .right_headerss .opt_btns .ant-btn-link:not(:last-child)::after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 16px;
  background: #d8d8d8;
  right: 0;
  top: 8px;
  position: absolute;
}
.topic_components .components_right .pagetion {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}
.topic_components .components_right .right_box {
  padding: 0 10px;
  margin: 10px 10px;
  width: 100%;
  border-bottom: 1px solid #f2f2f2;
  background: #FFFFFF;
  box-shadow: 2px 2px 11px 0px rgba(192, 192, 192, 0.5);
  border-radius: 6px;
}
.topic_components .components_right .right_box .right_header .opt_btns {
  padding: 10px 0;
  min-width: 541px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.topic_components .components_right .right_box .right_header .opt_btns button.ant-btn.ant-btn-text:not(:last-child) {
  margin: 0;
}
.topic_components .components_right .right_box .right_header .opt_btns .disabled {
  color: rgba(0, 0, 0, 0.25);
}
.topic_components .components_right .right_box .right_header .opt_btns .item_ {
  padding: 4px 16px;
  position: relative;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #525252;
  max-width: 160px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* 显示不下时出现... */
  cursor: pointer;
}
.topic_components .components_right .right_box .right_header .opt_btns .item_.disabled {
  cursor: no-drop;
  color: rgba(0, 0, 0, 0.25) !important;
}
.topic_components .components_right .right_box .right_header .opt_btns .item_.disabled .ant-btn {
  color: rgba(0, 0, 0, 0.25) !important;
}
.topic_components .components_right .right_box .right_header .opt_btns .item_ > span:last-child {
  margin-left: 8px;
}
.topic_components .components_right .right_box .right_header .opt_btns .item_:not(:last-child)::after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 16px;
  background: #d8d8d8;
  right: 0;
  top: 8px;
  position: absolute;
}
.topic_components .components_right .right_box .right_header .opt_btns .ant-btn {
  margin-right: 14px;
  display: flex;
  justify-items: center;
  align-items: center;
  margin: 0;
}
.topic_components .components_right .right_box .right_header .opt_btns .ant-btn span.anticon {
  font-size: 16px;
}
.topic_components .components_right .right_box .right_header .opt_btns .ant-btn.ant-btn-primary {
  border-radius: 16px;
}
.topic_components .components_right .right_box .right_header .opt_btns .ant-btn-link:not(:last-child)::after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 16px;
  background: #d8d8d8;
  right: 0;
  top: 8px;
  position: absolute;
}
.topic_components .components_right .right_box .right_header .opt_bj {
  display: flex;
}
.topic_components .components_right .right_box .right_header .opt_bj .dels:not(:last-child)::after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 16px;
  background: #d8d8d8;
  right: 0;
  top: 8px;
  position: absolute;
}
.topic_components .components_right .right_box .right_footer {
  background: #F9F9F9;
  padding: 6px 0;
  margin: 20px 0px 10px 0px;
}
.topic_components .components_right .right_box .right_footer .footerBox {
  padding: 2px 6px;
}
.topic_components .components_right .right_box .right_footer .footerBox .item_ {
  margin-right: 10px;
}
.topic_components .components_right .right_centre .auto-img {
  font-family: AppleSystemUIFont;
  font-size: 16px;
  color: #2E2E2E;
}
.topic_components .components_left {
  width: 20%;
  background-color: #f2f2f2;
  margin: 0 20px;
  background: #FFFFFF;
  border-radius: 6px;
}
.topic_components .components_left .components_left_top {
  background-color: #ffffff;
  padding: 10px;
  border-radius: 6px;
}
.topic_components .components_left .components_left_top .left_top_qb {
  display: flex;
  font-size: 16px;
  align-items: center;
}
.topic_components .components_left .components_left_top .left_top_qb::before {
  content: '';
  width: 4px;
  height: 17px;
  background: linear-gradient(180deg, rgba(84, 156, 255, 0) 0%, #549CFF 100%);
}
.topic_components .components_left .components_left_top .left_top_qb > span {
  margin: 0 5px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #2A2A2A;
  cursor: pointer;
}
.topic_components .components_left .trees {
  padding: 10px 0px;
}
.topic_components .components_left .trees .ant-tree-treenode {
  padding: 10px;
}
