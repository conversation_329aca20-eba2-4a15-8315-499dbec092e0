.uf-exam-layout-wrapper-container {
  position: relative;
  height: 100%;

  &.exam-npu-container {
    padding-top: 4.375rem;
    background: url('~@/images/login/app-bg.jpg') no-repeat;
    background-size: 100% auto;

    // background-color:#0546d2;
    .uf-exam-layout-content {
      height: 100%;
    }
  }

  .uf-exam-layout-content {
    height: calc(100vh - 60px);
    // height: 100vh;
    display: flex;
    background-color: #f7f9fa;

    .uf-exam-left-part {
      width: 70px;

      height: 100%;
      flex-shrink: 0;
      background: white;
      box-shadow: none;
      border-right: 2px #f7f9fa solid;

      .left_menu_container {
        margin-top: 2px;
        width: 70px;
        height: calc(100% - 2px);
        background: #fff;
        padding-top: 13px;
        display: flex;
        flex-direction: column;

        .menu_item {
          width: 70px;
          height: 60px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          color: #2a2a2a;
        }

        .menu_item_active {
          background: var(--primary-color);
          color: #fff;
        }
      }
    }
    .uf-exam-right-part {
      overflow-x: hidden;
      height: 100%;
      position: relative;
      flex: 1;
      padding: 0 205px;
      // box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.07);
      // margin: 24px;
      background: #f7f9fa;

      .ant-input,
      .ant-select .ant-select-selector {
        border-color: #cbcbcb;
      }

      .ant-btn-link {
        color: #525252;

        &:hover {
          color: var(--primary-color);
        }

        &[disabled] {
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }
  }
}
