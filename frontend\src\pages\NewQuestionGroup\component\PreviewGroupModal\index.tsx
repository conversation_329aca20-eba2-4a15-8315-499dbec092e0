import RenderHtml from '@/components/renderHtml';
import { MenuOutlined } from '@ant-design/icons';
import { Modal, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { arrayMoveImmutable } from 'array-move';
import { FC, useEffect, useRef, useState } from 'react';
import {
  SortableContainer,
  SortableContainerProps,
  SortableElement,
  SortableHandle,
  SortEnd,
} from 'react-sortable-hoc';
import './index.less';

interface DataType {
  key: string;
  name: string;
  age: number;
  address: string;
  index: number;
}

const DragHandle = SortableHandle(() => (
  <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />
));


const SortableItem = SortableElement(
  (props: React.HTMLAttributes<HTMLTableRowElement>) => <tr {...props} />,
);
const SortableBody = SortableContainer(
  (props: React.HTMLAttributes<HTMLTableSectionElement>) => (
    <tbody {...props} />
  ),
);

const PreviewGroupModal: FC<any> = ({
  showPreview,
  tableList,
  content,
  onClose,
  onConfirm,
}) => {
  // 保存排序信息
  const sortTimesMap = useRef(new Map<string, number>());
  const handleOk = () => {
    const len = tableList.length;
    const cloneList = dataSource.map((item: any, index: number) => {
      delete item.key;
      delete item.index;
      const preIndex = sortTimesMap.current.get(item.id) ?? len;
      const indexChange = preIndex - index < 0;
      // 修改sortTimes配合id，触发list的组件卸载重新渲染，
      // 原因： 触发排序后，升序的组件的editor不能编辑
      return {
        ...item,
        isSort: indexChange,
        sortTimes: indexChange ? item.sortTimes + 1 : item.sortTimes,
      };
    });
    // onClose()
    onConfirm(cloneList);
  };

  const handleCancel = () => {
    onClose();
  };

  // 删除
  const handleDelItem = (id: string) => {
    const newData = dataSource.filter((item: any) => item.id !== id);
    setDataSource(newData);
  }

  useEffect(() => {
    setDataSource(
      tableList.map((item: any, index: number) => {
        sortTimesMap.current.set(item.id, index);
        return { ...item, key: item.id, index };
      }),
    );
  }, [tableList]);

  const [dataSource, setDataSource] = useState(tableList);
  const onSortEnd = ({ oldIndex, newIndex }: SortEnd) => {
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable(
        dataSource.slice(),
        oldIndex,
        newIndex,
      ).filter((el: any) => !!el);
      setDataSource(newData);
    }
  };
  // 拖拽
  const DraggableContainer = (props: SortableContainerProps) => (
    <SortableBody
      useDragHandle
      disableAutoscroll
      helperClass="group-row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  );
  const DraggableBodyRow: React.FC<any> = ({
    className,
    style,
    ...restProps
  }) => {
    // function findIndex base on Table rowKey props and should always be a right array index
    const index = dataSource.findIndex(
      (x: any) => x.index === restProps['data-row-key'],
    );
    return <SortableItem index={index} {...restProps} />;
  };


  const columns: ColumnsType<any> = [
    {
      title: '',
      dataIndex: 'sort',
      width: 30,
      className: 'drag-visible',
      render: () => <DragHandle />,
    },
    {
      title: '序号',
      dataIndex: 'id',
      render(value, record, index) {
        return index + 1;
      },
    },
    {
      title: '小题题干',
      dataIndex: 'questions_content',
      className: 'drag-visible',
      render: (value: any) => (
        <RenderHtml cname="auto-img" value={value}></RenderHtml>
      ),
    },
    {
      title: '操作',
      dataIndex: 'operate',
      render(value, record, index) {
        return (
          <div className="operate">
            <a onClick={(e:any) => {
              e.stopPropagation();
              handleDelItem(record.id)
            }}>删除</a>
          </div>
        );
      },
    },
  ];

  return (
    <Modal
      title="题组预览"
      open={showPreview}
      onOk={handleOk}
      onCancel={handleCancel}
      width={'50vw'}
    >
      <div className="pre_modal_container">
        <div style={{overflow: 'auto'}}>
          <RenderHtml cname="auto-img" value={content}></RenderHtml>
        </div>
        <Table
          pagination={false}
          dataSource={dataSource}
          columns={columns}
          rowKey="index"
          components={{
            body: {
              wrapper: DraggableContainer,
              row: DraggableBodyRow,
            },
          }}
        />
      </div>
    </Modal>
  );
};

export default PreviewGroupModal;
