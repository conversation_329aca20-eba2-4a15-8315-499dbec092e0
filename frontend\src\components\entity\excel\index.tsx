import React, { FC, useEffect, useState } from 'react';
// import XLSX from "xlsx";
// import axios from 'axios'
import './index.less';
const xlsx: FC<Entity.IBaseEntity> = ({ src, onError }) => {
  const [excel, setExcel] = useState<any>(null);

  useEffect(() => {
    init();
  }, []);

  const init = () => {
    var nContainer = document.getElementById('xlsx_container');
    var xlsx = new Plus.Xlsx(nContainer);
    xlsx.readFile(src);
    // axios.get(src,{
    //   responseType: "arraybuffer", // 设置响应体类型为arraybuffer
    // }).then(({data})=> {
    //   let workbook = XLSX.read(new Uint8Array(data), {type:"array"}); // 解析数据
    //   let worksheet = workbook.Sheets[workbook.SheetNames[0]]; // workbook.SheetNames 下存的是该文件每个工作表名字,这里取出第一个工作表
    //   let vhtml = XLSX.utils.sheet_to_html(worksheet);
    //   setExcel(vhtml); // 渲染
    // })
    // 
  };

  return (
    <div id='xlsx_container' className="entity-excel"></div>);

};
export default xlsx;