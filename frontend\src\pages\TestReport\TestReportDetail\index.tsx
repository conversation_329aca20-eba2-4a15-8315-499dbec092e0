import examManageApis from '@/api/exam';
import { Button, message, Table } from 'antd';
import dayjs from 'dayjs';
import html2canvas from 'html2canvas-pro';
import jsPDF from 'jspdf';
import { FC, useEffect, useState } from 'react';
import { history, useLocation } from 'umi';
import './index.less';

const TestReportDetail: FC = () => {
  const location: any = useLocation<any>();
  const query = location?.query;
  const [reportDetail, setReportDetail] = useState<any>([]);
  const getDetail = (id: string) => {
    examManageApis.fetchExamReportDetail(id).then((res: any) => {
      if (res?.status == 200) {
        setReportDetail(res?.data);
      }
    });
  };
  useEffect(() => {
    if (location?.query?.id) {
      getDetail(location.query?.id);
    }
  }, [location?.query?.id]);

  // 获取扁平化的题目
  const getFlatQuestion = (data: any[]) => {
    if (data?.length > 0) {
      let index = 0;
      const temp = data.map((item) => {
        const realArr: any[] = [];
        item?.questions?.forEach((cell: any) => {
          if (cell?.question?.questions_type !== 5) {
            const cloneItem = {
              ...cell.question,
              parentId: cell?.question?.id,
              itemIndex: index++,
              isCorrect: cell.answerQuestion?.[0]?.isCorrect
            };
            realArr.push(cloneItem);
          } else {
            const itemArr = cell?.question?.groupQuestions.map(
              (item_1: any) => {
                return {
                  ...item_1,
                  parentId: cell?.question?.id + '_' + item_1?.id,
                  itemIndex: index++,
                  isCorrect: cell.answerQuestion?.[0]?.isCorrect
                };
              },
            );
            realArr.push(...itemArr);
          }
        });
        return realArr;
      });
      return temp;
    }
    return [];
  };

  //#region 表格
  const columns: any = [
    {
      title: '考核部分',
      dataIndex: 'testIndex',
      key: 'testIndex',
      render: (_: any, __: any, index: number) => {
        return `第${index + 1}部分`;
      },
    },
    {
      title: '得分/总分',
      dataIndex: 'count',
      key: 'count',
      render: (_: any, record: any) => {
        return `${record?.score}/${record?.totalScore}`;
      },
    },
    {
      title: '答对试题',
      dataIndex: 'scoringQuantity',
      key: 'scoringQuantity',
    },
    {
      title: '正确率',
      dataIndex: 'scoringRate',
      key: 'scoringRate',
    },
  ];

  // 格式化时间 传入得时间：毫秒
  const formatTime = (time: number) => {
    const seconds = Math.floor(time / 1000);
    const minutes = Math.floor(seconds / 60);
    return {
      m: minutes,
      s: seconds % 60,
    };
  };

  const renderTime = (time: number) => {
    const { m, s } = formatTime(time);
    return `${m}分${s}秒`;
  };
  //#endregion

  //#region 下载报告
  const [loading, setLoading] = useState(false);
  const downloadReport = () => {
    setLoading(true);
    const ele = document.getElementById('report') as HTMLElement;
    html2canvas(ele)
      .then((res) => {
        const dataUrl = res.toDataURL('image/jpeg', 1.0);
        // a4纸大小 595 * 842
        const A4Size = {
          width: 595,
          height: 842,
        };
        // 生成pdf
        const { width, height } = res;
        let pageHeight = (width / A4Size.width) * A4Size.height;
        let leftHeight = height;
        let position = 0;
        const imgHeight = (A4Size.width / width) * height;
        let PDF = new jsPDF('p', 'pt', 'a4');
        if (leftHeight < pageHeight) {
          PDF.addImage(dataUrl, 'JPEG', 0, 0, A4Size.width, imgHeight);
        } else {
          while (leftHeight > 0) {
            PDF.addImage(dataUrl, 'JPEG', 0, position, A4Size.width, imgHeight);
            leftHeight -= pageHeight;
            position -= 842;
            if (leftHeight > 0) {
              PDF.addPage();
            }
          }
        }
        PDF.save(`${query?.name}.pdf`);
      })
      .catch((err) => {
        console.log(err);
        message.error('下载失败');
      })
      .finally(() => setLoading(false));
  };
  //#endregion
  // 点击序号跳转详情页
  const handleToDetailPage = (item: any) => {
    history.push(`/reportdetailpage?id=${query?.id}&reportId=${item.parentId}&name=${query?.name}&classifyId=${query?.classifyId}`);
  };
  return (
    <div className="test_report_detail_container">
      <div className="content_left" id="report">
        <div className="header">{query?.name}</div>
        <div className="content">
          <div className="top_rank">
            <div className="rank_header">
              <div>
                交卷时间：
                {dayjs(Number(query?.subTime) || 0).format(
                  'YYYY-MM-DD HH:mm:ss',
                )}
              </div>
              <div>试卷难度：{query?.level}</div>
              <div>作答用时：{renderTime(query?.answerTime || 0)}</div>
            </div>
            <div className="rank_item_list">
              <div className="rank_item rank_one">
                <div className="line" />
                <div className="rank_item_title">得分</div>
                <div className="rank_num">
                  <span style={{ color: '#549CFF' }}>{query.score}</span>/
                  {query?.total}
                </div>
              </div>
              <div className="rank_item rank_two">
                <div className="line" />
                <div className="rank_item_title">平均分</div>
                <div className="rank_num">{query?.avg}</div>
              </div>
              <div className="rank_item rank_three">
                <div className="line" />
                <div className="rank_item_title">击败比</div>
                <div className="rank_num">{query?.defeatThan}%</div>
              </div>
              <div className="rank_item rank_four">
                <div className="rank_item_title">全站排名</div>
                <div className="rank_num">{query?.rank}</div>
              </div>
            </div>
          </div>
          <div className="table_detail">
            <Table
              columns={columns}
              dataSource={reportDetail}
              pagination={false}
            />
          </div>
        </div>
      </div>
      <div className="content_right">
        <div className="top_btn">
          <Button type="primary" loading={loading} onClick={downloadReport}>
            下载报告
          </Button>
        </div>
        <div className="title_num">
          <div
            className="analys_title"
            onClick={() => {
              history.push(`/reportdetailpage?id=${query?.id}&name=${query?.name}`);
            }}
          >
            查看全部解析
          </div>
          <div className="line" />
          <div className="title_list">
            {getFlatQuestion(reportDetail).map((item: any, index: number) => {
              return (
                <div>
                  <div style={{ marginBottom: 10 }}>第{index + 1}部分</div>
                  <div className="title_part" key={`${index}_${item.id}`}>
                    {item?.map((cell: any, index_2: number) => {
                      const isCorrect = cell.isCorrect;
                      return (
                        <div
                          key={`${index_2}_${cell.parentId}`}
                          className={`title_item ${isCorrect ? 'correct_items' : 'wrong_items'
                            }`}
                          onClick={() => handleToDetailPage(cell)}
                        >
                          {cell.itemIndex + 1}
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestReportDetail;
