import axios from 'axios';
import { RequestConfig, getDvaApp, ErrorShowType } from 'umi';
let configSafe: any = null;
async function getconfig() {
  if ((window as any).configSafe) {
    return (window as any).configSafe;
  } else {
    const res = await fetch('safeMethodConfig.json');
    const result = await res.json();
    (window as any).configSafe = result;
    return result;
  }
}

const HTTP = axios.create({
  // baseURL: 'http://rman.**************.xip.io',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 20000,
  withCredentials: true,
});
// const SAFE_MODE = false;
// 请求的数量
let requsetCount: number = 0;
// HTTP.defaults.headers['Content-Type'] = 'application/json';
// 添加请求拦截器
HTTP.interceptors.request.use(
  async (config: any) => {
    let baseUrl = '';
    // if (process.env.NODE_ENV === 'development') {
    //   baseUrl = '/api';
    // }
    const { _store } = getDvaApp();
    _store.dispatch({
      type: 'config/changeShowLoading',
      payload: {
        value: true,
      },
    });
    requsetCount++;
    config.url=baseUrl+config.url;
    return config;
  },
  error => {
    // 对请求错误做些什么
    return Promise.reject(error);
  },
);
let flag = true;

// 添加响应拦截器
function sleep(ns: number) {
  return new Promise(resolve =>
    setTimeout(() => {
      resolve(null);
    }, ns),
  );
}

// fetch('safeMethodConfig.json').then(res=>res.json()).then((ress)=>{
//   console.log(ress)
// })

HTTP.interceptors.response.use(
  async response => {
    requsetCount--;
    if (requsetCount === 0) {
      const { _store } = getDvaApp();
      _store.dispatch({
        type: 'config/changeShowLoading',
        payload: {
          value: false,
        },
      });
    }
    if (
      response.data?.errorCode === 'course_0000_0002' ||
      response.data?.errorCode === 'notlogged' ||
      response.data?.error_code === 'forumservice.0000.0401' ||
      response.data?.status === 403
    ) {
      if (flag) {
        flag = false;
        // alert('未登录或登录过期！');
        window.parent.postMessage(
          JSON.stringify({ action: 'login' }),
          window.location.origin,
        );
        window.location.replace(
          `/unifiedlogin/v1/loginmanage/login/direction?redirect_url=${encodeURIComponent(
            window.location.href,
          )}`,
        );
      }
    } else {
      flag = true;
    }
    if(response.status === 200){
      if(response.data.status === 401 || response.data.errorCode === '401'|| response.data.error_code === '401'){
        window.location.replace('/unifiedplatform/#/not_authority');
      }
      return response.data;
    }
  },
  error => {
    requsetCount--;
    if (requsetCount === 0) {
      const { _store } = getDvaApp();
      _store.dispatch({
        type: 'config/changeShowLoading',
        payload: {
          value: false,
        },
      });
    }
    //拦截
    if (error.response.data.error?.code === '401'|| error.response.data.statusCode === '401') {
      // alert('未登录或登录过期！');
      window.parent.postMessage(
        JSON.stringify({ action: 'login' }),
        window.location.origin,
      );
      window.location.replace(
        `/unifiedlogin/v1/loginmanage/login/direction?redirect_url=${encodeURIComponent(
          window.location.href,
        )}`,
      );
    } else {
      console.log('进了异常处理 但是没有判断code');
      // 对响应错误做点什么
      return Promise.reject(error);
    }
  },
);

export default HTTP;
