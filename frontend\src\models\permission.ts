import loginApis from '@/service/loginApis';
import loginTypes from '@/types/loginTypes';

export interface IPermission {
  permissions: string[];
  modules: string[];
  rmanGlobalParameter: string[];
  parameterConfig: IObj<'true' | 'false'>;
}
interface IObj<T> {
  [propsName: string]: T;
}

const _rmanCode = 'rman';

export default {
  namespace: 'permission',
  subscriptions: {
    setup({ dispatch, history }: any) {
      return history.listen(({ pathname }: any) => {
        dispatch({
          type: 'fetchPermissions',
        });
        dispatch({
          type: 'fetchGlobalParameter',
        });
        dispatch({
          type: 'fetchGlobalParameterConfigs'
        })
        // if (pathname.indexOf('basic') > -1) {
        //   dispatch({
        //     type: 'fetchPermissions',
        //   });
        //   dispatch({
        //     type: 'fetchGlobalParameter',
        //   });
        //   dispatch({
        //     type: 'fetchGlobalParameterConfigs'
        //   })
        // }
      });
    },
  },
  state: {
    permissions: [],
    rmanGlobalParameter: [],
    modules: [],
    parameterConfig: {}
  },
  effects: {
    *fetchPermissions(_: any, { call, put }: any) {
      const {
        errorCode,
        extendMessage,
      }: API.OsResponse<loginTypes.IPermission[]> = yield call(
        loginApis.fetchUserPermissionsV2,
      );
      if (errorCode === 'success') {
        // const rmanPermission = extendMessage.find(
        //   (per: loginTypes.IPermission) => per.code === _rmanCode,
        // )?.moduleFeatures;
        // if (rmanPermission) {
        //   const permissions = Object.keys(rmanPermission)
        //     .map(key => rmanPermission[key])
        //     .flat(2);
        //   yield put({
        //     type: 'updateState',
        //     payload: {
        //       permissions,
        //     },
        //   });
        // }
        if (typeof extendMessage.moduleFeatures === 'object') {
          const permissions = Object.keys(extendMessage.moduleFeatures)
            .map(key => extendMessage.moduleFeatures[key])
            .flat(2);
          const modules = extendMessage.modules
          yield put({
            type: 'updateState',
            payload: {
              permissions,
              modules
            },
          });
        }
      }
    },
    *fetchGlobalParameter(_: any, { call, put }: any) {
      const {
        errorCode,
        extendMessage,
      }: API.OsResponse<loginTypes.IGlobalParam[]> = yield call(
        loginApis.fetchRmanGlobalParam,
      );
      if (errorCode === 'success') {
        if (Array.isArray(extendMessage)) {
          const rmanGlobalParameter = extendMessage
            .filter(item => item.value === "true")
            .map(item => item.code);
          yield put({
            type: 'updateState',
            payload: {
              rmanGlobalParameter
            },
          });
        }
      }
    },
    *fetchGlobalParameterConfigs(_: any, { call, put }: any) {
      const { errorCode, extendMessage} = yield call(loginApis.fetchParameterConfigs);
      if (errorCode === 'success') {
        if (Array.isArray(extendMessage)) {
          const parameterConfig: IObj<'true' | 'false'> = {};
          extendMessage.forEach((r: any) => {
            parameterConfig[r.code] = r.value;
          })
          yield put({
            type: 'updateState',
            payload: {
              parameterConfig
            },
          });
        }
      }
    }
  },
  reducers: {
    updateState(state: IPermission, { payload }: any) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
