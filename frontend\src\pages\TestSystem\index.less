.test_system_container {
  width: 100vw;
  height: 100vh;
  position: relative;
  .system_bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
  }
  .test_index_container {
    width: 100%;
    height: calc(100vh - 60px);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 100px;
    .menu_item {
      width: 440px;
      height: 300px;
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 10px;
      gap: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 24px;
      color: #282c3c;
    }
    .menu_item_img {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
      top: 0;
      left: 0;
      z-index: -1;
      border-radius: 10px;
    }
    .menu_item_icon {
      width: 62px;
      height: 66px;
      object-fit: cover;
    }
  }
}
